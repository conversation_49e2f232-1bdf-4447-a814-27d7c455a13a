# 平台表单硬编码实现任务列表

## 📋 任务概述
将平台表单的验证逻辑从动态配置改为硬编码实现，移除对平台规范文件的依赖，提升性能和代码可维护性。

## 🎯 执行步骤

### 第一步：创建全局硬编码验证器
**目标文件**: `src/renderer/src/pages/Publish/components/platform-forms/video/[平台名]PlatformForm.tsx`

**验证规则迁移原则**：
- 只迁移平台规范文件中已存在的验证规则，不添加额外的验证逻辑
- 将 specification 中的动态配置值改为硬编码常量
- 保持原有的验证逻辑和错误消息不变

**在文件顶部（导入语句后）添加全局验证器**：

**推荐使用工厂模式减少重复代码**：
```typescript
import { PlatformValidatorFactory } from './validators/platform-validator-factory'
```

**根据原规范文件中的配置决定添加哪些验证器**：

⚠️ **重要**：工厂方法现在支持选择性配置，只有在options中包含的配置项才会添加相应的验证规则。

1. **如果原规范中有 `titleMaxLength`、`titleMinLength` 或标题必填要求**，添加标题验证器：
```typescript
// 使用工厂模式（推荐）- 只传递原规范中存在的配置项
const titleValidator = PlatformValidatorFactory.createTitleValidator(platforms.[平台名], {
  maxLength: [原规范中的titleMaxLength值], // 只有原规范中有此项时才添加
  minLength: [原规范中的titleMinLength值], // 只有原规范中有此项时才添加
  required: [是否必填], // 如果标题为必填字段则设置为true
})
```

2. **如果原规范中有 `descriptionMaxLength` 或 `descriptionMinLength`**，添加描述验证器：
```typescript
// 使用工厂模式（推荐）- 只传递原规范中存在的配置项
const descriptionValidator = PlatformValidatorFactory.createDescriptionValidator(platforms.[平台名], {
  maxLength: [原规范中的descriptionMaxLength值], // 只有原规范中有此项时才添加
  minLength: [原规范中的descriptionMinLength值], // 只有原规范中有此项时才添加
})
```

3. **如果原规范中有 `topicMaxCount` 或 `topicMaxLength`**，添加话题验证器：
```typescript
// 使用工厂模式（推荐）
const topicValidator = PlatformValidatorFactory.createTopicValidator(platforms.[平台名], {
  maxCount: [原规范中的topicMaxCount值],
  maxLength: [原规范中的topicMaxLength值],
  required: [是否必填], // 可选
})
```

3a. **如果原规范中有 `tagMaxCount`、`tagMinCount` 或 `tagMaxLength` 等标签相关配置**，添加标签验证器：
```typescript
// 标签字段使用专门的标签验证器工厂 - 支持完整的标签验证配置
const tagsValidator = PlatformValidatorFactory.createTagsValidator(platforms.[平台名], {
  maxCount: [原规范中的tagMaxCount值], // 如果存在
  minCount: [原规范中的tagMinCount值], // 如果存在
  maxLength: [原规范中的tagMaxLength值], // 如果存在
  required: [是否必填],
})
```

3b. **如果原规范中有 `categoryRequired: true`**，添加分类验证器：
```typescript
// 分类字段验证器 - 主要用于必填验证
const categoryValidator = PlatformValidatorFactory.createCategoryValidator(platforms.[平台名], {
  required: true, // 根据原规范中的categoryRequired值
})
```

4. **如果原规范中有 `scheduledTimeMinTimeSpan` 或 `scheduledTimeMaxTimeSpan`**，添加定时发布验证器：

```typescript
// 使用工厂模式（推荐）- 只传递原规范中存在的配置项
const scheduledTimeValidator = PlatformValidatorFactory.createScheduledTimeValidator(platforms.[平台名], {
  minTimeSpan: TimeSpan.from[原规范配置](), // 只有原规范中有此项时才添加
  maxTimeSpan: TimeSpan.from[原规范配置](), // 只有原规范中有此项时才添加
})
```

### 第二步补充：标签和分类字段的最佳实践

**标签字段实现**：
- 优先使用 `TagsInput` 组件而不是自定义标签选择器
- 数据类型使用 `string[]`，与 `TagsInput` 组件匹配
- 验证器使用 `PlatformValidatorFactory.createTopicValidator`

```typescript
// 视图模型中
tags: string[] = [] // 标签字段，必填

// 表单组件中
import { TagsInput } from '@renderer/pages/Publish/components/tags-input'

<PublishFormItem label="标签" required>
  <TagsInput
    value={model.tags}
    onChange={(tags) => {
      formState.setDirty(true)
      onChange((draft) => {
        draft.tags = tags
      })
    }}
  />
</PublishFormItem>
```

**分类字段实现**：
- 优先使用现有的 `VideoCategorySelector` 组件而不是自定义实现
- 数据类型使用 `CascadingPlatformDataItem<[平台]VideoCategoryItem>[]`
- 需要先导出 `VideoCategorySelector` 组件（如果尚未导出）

```typescript
// 视图模型中
import type { CascadingPlatformDataItem } from '@common/structure'
import type { [平台名]VideoCategoryItem } from '@renderer/infrastructure/services'

category: CascadingPlatformDataItem<[平台名]VideoCategoryItem>[] = [] // 分类字段，必填

// 表单组件中
import { VideoCategorySelector } from '@renderer/pages/Publish/components/VideoCategory'
import { [平台名小写]PlatformService } from '@renderer/infrastructure/services'

<PublishFormItem label="分类" required>
  <VideoCategorySelector
    value={model.category}
    onChange={(category) => {
      formState.setDirty(true)
      onChange((draft) => {
        draft.category = category
      })
    }}
    categoriesGetter={() => [平台名小写]PlatformService.getCategories()}
    platform={platforms.[平台名]}
  />
</PublishFormItem>
```

### 第二步补充：单选字段的统一实现规范

**所有单选字段实现原则**：
- 统一使用 `RadioGroupField` 组件，不使用原生 `RadioGroup` 或 `Select` 下拉选择器
- 组件会根据选项数量自动决定布局方式：
  - 选项数量 ≤ 2 时：横向排列（`flex flex-row space-x-6 pt-2`）
  - 选项数量 > 2 时：纵向排列（`flex flex-col gap-3 pt-2`）
- 声明字段默认值统一为 `null`（对应"无需声明"选项）
- 所有单选字段都自动遵循 `pt-2` 的样式规范

**单选字段标准实现模板**：

```typescript
// 表单组件中导入统一的单选组件和Option类
import { RadioGroupField } from '@renderer/pages/Publish/components/RadioGroupField'
import { Option } from '@renderer/infrastructure/model/option'

// 示例1：声明字段（支持null值）
const declarationOptions: Option<string | null>[] = [
  Option.of('无需声明', { value: null }),
  Option.of('图片/视频由AI生成'),
]

<PublishFormItem label="声明" required={false}>
  <RadioGroupField
    value={model.declaration}
    onChange={(value) => {
      formState.setDirty(true)
      onChange((draft) => {
        draft.declaration = value
      })
    }}
    options={declarationOptions}
  />
</PublishFormItem>

// 示例2：类型字段（不支持null值，可以自动推断类型）
const typeOptions = [
  Option.of('原创'),
  Option.of('转载'),
]

<PublishFormItem label="类型" required>
  <RadioGroupField
    value={model.type}
    onChange={(value) => {
      formState.setDirty(true)
      onChange((draft) => {
        draft.type = value as ZhiHuContentType
      })
    }}
    options={typeOptions}
  />
</PublishFormItem>

// 示例3：多选项声明字段（>2个选项，自动纵向排列）
const declarationOptions: Option<string | null>[] = [
  Option.of('无需声明', { value: null }),
  Option.of('内容由AI生成', { value: 'ai-generated' }),
  Option.of('虚构演绎仅供娱乐', { value: 'entertainment' }),
  Option.of('危险行为,请勿模仿', { value: 'dangerous' }),
]

<PublishFormItem label="声明" required={false}>
  <RadioGroupField
    value={model.declaration}
    onChange={(value) => {
      formState.setDirty(true)
      onChange((draft) => {
        draft.declaration = value
      })
    }}
    options={declarationOptions}
  />
</PublishFormItem>
```

**RadioGroupField组件特性**：
- 使用Option.of工厂方法创建选项，简化代码
- **类型声明原则**：优先让TypeScript自动推断，必要时才显式声明
  - 包含null值时：`const options: Option<string | null>[] = [...]`
  - 普通字符串时：`const options = [...]`（自动推断）
- key和value默认使用label值，减少认知负担
- 需要特殊值时使用：`Option.of('label', { value: specialValue })`
- 自动根据选项数量决定布局（≤2横向，>2纵向）
- 自动应用pt-2样式规范
- 正确处理null值（使用`Option.of('label', { value: null })`）
- 支持泛型类型，保证类型安全
- 统一的交互体验和视觉效果

**重要原则**：
- **⚠️ 关键**：只添加原规范文件中存在相应配置的验证器
- 不存在配置的字段不需要添加验证器
- **⚠️ 关键**：验证规则的数值必须与原规范文件中的配置完全一致
- **⚠️ 关键**：必须使用对象参数形式调用验证器工厂方法
- **⚠️ 关键**：不能遗漏原规范文件中已经存在的验证配置，如果工厂方法不支持，应该重构工厂方法使其支持
- 优先复用现有组件（TagsInput、VideoCategorySelector）而不是自定义实现
- 保持数据类型与组件接口的一致性
- **所有单选字段统一使用RadioGroupField组件**
- **组件自动根据选项数量决定布局和样式**
- **声明字段默认值为null（无需声明）**

### 第二步：移除自定义组件实现

**删除自定义标签选择器**：
如果存在自定义的标签选择器组件，应删除并使用 `TagsInput`：
```typescript
// ❌ 删除类似的自定义组件
function TagsSelector({ value, onChange, session }) {
  // ... 复杂的实现逻辑
}

// ✅ 使用现有的TagsInput组件
import { TagsInput } from '@renderer/pages/Publish/components/tags-input'
```

**删除自定义分类选择器**：
如果存在自定义的分类选择器组件，应删除并使用 `VideoCategorySelector`：
```typescript
// ❌ 删除类似的自定义组件
function CategorySelector({ value, onChange }) {
  // ... 复杂的实现逻辑
}

// ✅ 使用现有的VideoCategorySelector组件
import { VideoCategorySelector } from '@renderer/pages/Publish/components/VideoCategory'
```

### 第三步：移除组件内部的验证器定义
**删除以下代码块**：
```typescript
// 删除所有类似的useMemo验证器定义
const titleValidator = useMemo(() => {
  return new Validator<string, PublishRuleResultExtra>().addRule((subject: string) => {
    // ... 验证逻辑
  })
}, [])

const descriptionValidator = useMemo(() => {
  // ... 验证逻辑
}, [])

const topicValidator = useMemo(() => {
  // ... 验证逻辑
}, [])
```

**删除specification常量**：
```typescript
// 删除这个常量定义
const specification = {
  titleMaxLength: [数值],
  descriptionMaxLength: [数值],
  topicMaxCount: [数值],
  topicMaxLength: [数值],
}
```

### 第四步：更新输入框限制
**修改标题输入框的maxLength**：
```typescript
// 修改前
maxLength={specification.titleMaxLength}

// 修改后
maxLength={[标题最大长度硬编码数值]}
```

### 第五步：验证配置完整性检查
**⚠️ 关键步骤**：在完成实现后，必须进行验证配置对比检查

1. **检查原始规范配置**：
```bash
# 查看原始规范文件配置（替换为实际的commit hash）
git show [commit-hash]:src/renderer/src/pages/Publish/specification/content-type/video/platforms/[平台名].ts
```

2. **验证配置对比清单**：
- [ ] 原规范中的所有验证规则是否都已经迁移
- [ ] 所有数值是否与原规范完全一致
- [ ] 没有遗漏任何验证规则
- [ ] 没有添加原规范中不存在的验证规则

### 第六步：验证和测试
1. **运行类型检查**：`npm run typecheck`
2. **确保无编译错误**
3. **验证功能完整性**：确保所有验证逻辑正常工作

## ⚠️ 注意事项

1. **验证规则迁移原则**：
   - 只迁移平台规范文件中已存在的验证规则，不添加新的验证逻辑
   - 没有特别说明的字段不需要添加额外的验证规则
   - 保持原有验证逻辑的行为和错误消息不变

2. **保留useMemo**：只保留话题提取的useMemo，因为需要从描述中动态提取话题
   ```typescript
   const topics = useMemo(() => htmlService.getTopics(model.description), [model.description])
   ```

3. **平台名称**：确保使用正确的平台名称（如`platforms.XiaoHongShu`、`platforms.WeiXinShiPinHao`等）

4. **字段支持**：某些平台可能不支持某些字段（如标题、话题等），需要根据实际情况调整

5. **验证消息**：确保错误消息中的数值与硬编码的限制值一致

## ✅ 完成检查清单

- [ ] **⚠️ 关键**：已检查原始规范文件，确认所有验证配置
- [ ] 全局验证器已创建，使用硬编码限制值
- [ ] **⚠️ 关键**：验证器使用对象参数形式，包含所有必要的验证规则
- [ ] **⚠️ 关键**：标题和描述验证器包含最小长度验证（如果原规范中存在）
- [ ] **⚠️ 关键**：标签验证器包含最小数量和最大长度验证（如果原规范中存在）
- [ ] **⚠️ 关键**：定时发布验证器时间配置与原规范完全一致
- [ ] 标签字段使用 `TagsInput` 组件和 `createTagsValidator` 验证器
- [ ] 分类字段使用 `VideoCategorySelector` 组件
- [ ] **所有单选字段统一使用 `RadioGroupField` 组件**
- [ ] **RadioGroupField组件自动处理布局和样式**
- [ ] **声明字段默认值为null（无需声明）**
- [ ] 自定义标签/分类选择器组件已删除
- [ ] 组件内部的useMemo验证器已删除
- [ ] specification常量已删除
- [ ] 输入框maxLength已更新为硬编码值
- [ ] **⚠️ 关键**：已进行验证配置完整性检查，确认无遗漏
- [ ] TypeScript类型检查通过
- [ ] 功能测试正常

## 🎯 预期效果

完成后，平台表单将：
- 不再依赖平台规范文件的动态配置
- 验证器在模块加载时创建，提升性能
- 验证逻辑直接可见，便于维护
- 代码更简洁，无需null检查和动态查询
- 标签和分类字段使用统一的组件，保持一致性
- **所有单选字段统一使用RadioGroupField组件，交互体验一致**
- 减少重复代码，提高可维护性
- 标签验证器与话题验证器分离，语义更清晰

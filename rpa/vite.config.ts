import { defineConfig } from 'vite'
import fs from 'fs-extra'

const PackageRoot = __dirname

console.log(process.env.MIN)

/**
 * @see https://vitejs.dev/config/
 */
const config = defineConfig({
  root: PackageRoot,
  envDir: process.cwd(),
  define: {
    __DOUYIN_RENDER_CODE__: JSON.stringify(
      fs.readFileSync('./dist-render/douyin.js', 'utf-8').toString(),
    ),
    __WEIBO_RENDER_CODE__: JSON.stringify(
      fs.readFileSync('./dist-render/weibo.js', 'utf-8').toString(),
    ),
    __KUAISHOU_RENDER_CODE__: JSON.stringify(
      fs.readFileSync('./dist-render/kuaishou.js', 'utf-8').toString(),
    ),
    __XIAOHONGSHU_RENDER_CODE__: JSON.stringify(
      fs.readFileSync('./dist-render/xiaohongshu.js', 'utf-8').toString(),
    ),
    __BAIJIAHAO_RENDER_CODE__: JSON.stringify(
      fs.readFileSync('./dist-render/baijiahao.js', 'utf-8').toString(),
    ),
    __BILIBILI_RENDER_CODE__: JSON.stringify(
      fs.readFileSync('./dist-render/bilibili.js', 'utf-8').toString(),
    ),
    __SHIPINGHAO_RENDER_CODE__: JSON.stringify(
      fs.readFileSync('./dist-render/shipinghao.js', 'utf-8').toString(),
    ),
    __TOUTIAO_RENDER_CODE__: JSON.stringify(
      fs.readFileSync('./dist-render/toutiao.js', 'utf-8').toString(),
    ),
    __ZHIHU_RENDER_CODE__: JSON.stringify(
      fs.readFileSync('./dist-render/zhihu.js', 'utf-8').toString(),
    ),
    __QIE_RENDER_CODE__: JSON.stringify(
      fs.readFileSync('./dist-render/qie.js', 'utf-8').toString(),
    ),
    __SOUHU_RENDER_CODE__: JSON.stringify(
      fs.readFileSync('./dist-render/souhu.js', 'utf-8').toString(),
    ),
    __YIDIAN_RENDER_CODE__: JSON.stringify(
      fs.readFileSync('./dist-render/yidian.js', 'utf-8').toString(),
    ),
    __WANGYI_RENDER_CODE__: JSON.stringify(
      fs.readFileSync('./dist-render/wangyi.js', 'utf-8').toString(),
    ),
    __AQY_RENDER_CODE__: JSON.stringify(
      fs.readFileSync('./dist-render/aqy.js', 'utf-8').toString(),
    ),
    __WEISHI_RENDER_CODE__: JSON.stringify(
      fs.readFileSync('./dist-render/weishi.js', 'utf-8').toString(),
    ),
    __DOUYIN_IMAGE_RENDER_CODE__: JSON.stringify(
      fs.readFileSync('./dist-render/douyin-image.js', 'utf-8').toString(),
    ),
    __KUAISHOU_IMAGE_RENDER_CODE__: JSON.stringify(
      fs.readFileSync('./dist-render/kuaishou-image.js', 'utf-8').toString(),
    ),
    __XIAOHONGSHU_IMAGE_RENDER_CODE__: JSON.stringify(
      fs.readFileSync('./dist-render/xiaohongshu-image.js', 'utf-8').toString(),
    ),
    __SHIPINGHAO_IMAGE_RENDER_CODE__: JSON.stringify(
      fs.readFileSync('./dist-render/shipinghao-image.js', 'utf-8').toString(),
    ),
    __WEIBO_IMAGE_RENDER_CODE__: JSON.stringify(
      fs.readFileSync('./dist-render/weibo-image.js', 'utf-8').toString(),
    ),
  },
  esbuild: {
    keepNames: true,
  },
  build: {
    ssr: true,
    target: `node20`,
    outDir: 'dist',

    minify: false,
    lib: {
      entry: 'src/index.ts',
      formats: ['cjs'],
    },
    rollupOptions: {
      output: {
        entryFileNames: '[name].js',
      },
    },
    emptyOutDir: true,
    reportCompressedSize: false,
  },
})

export default config

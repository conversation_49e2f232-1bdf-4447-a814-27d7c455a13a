import type { IImageTask } from '../type'
import {
  getElementByText,
  getFileBuffer,
  getFileExtension,
  manualDispatchFileEvent,
  onSendEntryInput,
  onSendInput,
  retryCount,
  uint8ArrayToFile,
  baseStartPush,
  wait,
} from '../renderUtils'

const startPush = baseStartPush

const renderTaskMap = {
  image: 0,
  description: 0,
  music: 0,
}

async function renderImage(config: IImageTask) {
  if (renderTaskMap.image < retryCount) {
    const wapperDom = document.querySelector('#rc-tabs-0-panel-2 div[class^="_dragger-content_"]')

    if (wapperDom && wapperDom instanceof HTMLDivElement) {
      const files = new DataTransfer()

      for (let i = 0; i < config.images.length; i++) {
        const uint8Array = await getFileBuffer(config.images[i].path)
        if (uint8Array) {
          const file = uint8ArrayToFile(
            uint8Array,
            `${config.title}_${Math.random()}.${config.images[i].format.toLocaleLowerCase()}`,
            `image/${config.images[i].format.toLocaleLowerCase()}`,
          )

          files.items.add(file)
        }
      }

      const dropEvent = new DragEvent('drop', {
        bubbles: true,
        cancelable: true,
        dataTransfer: files,
      })

      wapperDom.dispatchEvent(dropEvent)

      renderTaskMap.image = retryCount
      await wait(3000)
    }

    renderTaskMap.image++
  }
}

async function render(config: IImageTask) {
  if (renderTaskMap.image < retryCount) {
    return
  }

  if (renderTaskMap.description < retryCount) {
    const divDom = document.getElementById('work-description-edit')

    if (divDom && divDom instanceof HTMLDivElement) {
      const text = config.description.split('___!!!!___')

      for (let i = 0; i < text.length; i++) {
        const item = text[i]

        if (item) {
          if (i) {
            await onSendInput(divDom, '#', config.tabId)
            await wait(300)
            await onSendInput(divDom, `${item} `, config.tabId)
            await wait(300)
          } else {
            await onSendInput(divDom, item, config.tabId)
            await wait(300)
          }
        }
      }

      renderTaskMap.description = retryCount
    }

    renderTaskMap.description++
  }

  if (renderTaskMap.music < retryCount) {
    if (config.music) {
      const spanDom = getElementByText('div', '添加音乐')

      if (spanDom && spanDom instanceof HTMLDivElement) {
        spanDom.click()
        await wait(300)
        const wapperDom = document.getElementById('microSupport')
        const inputDom = wapperDom.querySelector('input[placeholder="搜索音乐"]')

        if (inputDom && inputDom instanceof HTMLInputElement) {
          manualDispatchFileEvent({
            dom: inputDom,
            element: HTMLInputElement,
            elementKey: 'value',
            value: config.music,
            event: 'change',
          })

          await wait(1000)

          let count = 0
          while (count < 10) {
            const items = wapperDom.querySelectorAll('[class^="_item_"]')

            if (items && items.length) {
              for (const item of items) {
                const titleDom = item.querySelector('[class^="_info-title_"]')
                if (titleDom && titleDom instanceof HTMLDivElement) {
                  if (titleDom.textContent === config.music) {
                    const spanDom = getElementByText('div', '添加', item as HTMLElement)
                    if (spanDom && spanDom instanceof HTMLDivElement) {
                      spanDom.parentElement.click()
                      break
                    }
                  }
                }
              }
            }

            count++
            await wait(1000)
          }

          renderTaskMap.music = retryCount
        }
      }
    } else {
      renderTaskMap.music = retryCount
    }

    renderTaskMap.music++
  }

  const taskList = Object.keys(renderTaskMap).filter((key) => renderTaskMap[key] < retryCount)
  if (!taskList.length) {
    isDone = true
  }
}

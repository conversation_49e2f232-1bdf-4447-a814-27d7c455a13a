import type { IVideoTask } from '../type'
import {
  getFileBuffer,
  manualDispatchFileEvent,
  onSendInput,
  uint8ArrayToFile,
  baseStartPush,
  wait,
  retryCount,
} from '../renderUtils'

const startPush = baseStartPush

const renderTaskMap = {
  video: 0,
  title: 0,
  description: 0,
}

async function renderVideo(config: IVideoTask) {
  if (renderTaskMap.video < retryCount) {
    const shadowm = document.querySelector('.wujie_iframe')
    if (!shadowm) {
      return
    }
    const videoDom = shadowm.shadowRoot.querySelector('.upload')

    if (videoDom && Object.prototype.toString.call(videoDom) === '[object HTMLDivElement]') {
      const inputDom = videoDom.querySelector('input[type="file"]')

      if (inputDom) {
        const uint8Array = await getFileBuffer(config.videoPath)

        if (uint8Array) {
          const file = uint8ArrayToFile(uint8Array, config.videoFileName, config.videoMime)
          const files = new DataTransfer()
          files.items.add(file)

          manualDispatchFileEvent({
            dom: inputDom as HTMLInputElement,
            element: HTMLInputElement,
            elementKey: 'files',
            value: files.files,
            event: 'change',
          })

          renderTaskMap.video = retryCount
        }
      }
    }
  }

  renderTaskMap.video++
}

async function render(config: IVideoTask) {
  if (renderTaskMap.video < retryCount) {
    return
  }

  if (renderTaskMap.title < retryCount) {
    if (!config.title) {
      renderTaskMap.title = retryCount
    } else {
      const shadowm = document.querySelector('.wujie_iframe')
      if (!shadowm) {
        return
      }

      const titleDom = shadowm.shadowRoot.querySelector(
        'input[placeholder="概括视频主要内容，字数建议6-16个字符"]',
      )

      if (titleDom) {
        await onSendInput(titleDom as HTMLInputElement, config.title, config.tabId)
        renderTaskMap.title = retryCount
      }
    }
    renderTaskMap.title++
  }

  if (renderTaskMap.description < retryCount) {
    if (!config.description) {
      renderTaskMap.description = retryCount
    } else {
      const shadowm = document.querySelector('.wujie_iframe')
      if (!shadowm) {
        return
      }

      const descDom = shadowm.shadowRoot.querySelector('.input-editor')
      const tag = shadowm.shadowRoot.querySelector('.quick-btns > .finder-tag-wrap.btn')

      if (descDom && tag) {
        const text = config.description.split('___!!!!___')

        for (let i = 0; i < text.length; i++) {
          const item = text[i]

          if (item) {
            if (i && tag) {
              ;(tag as HTMLDivElement).click()
              await wait(500)

              await onSendInput(descDom as HTMLDivElement, item, config.tabId)
            } else {
              await onSendInput(descDom as HTMLDivElement, item, config.tabId)
              await wait(500)
            }
          }
        }

        renderTaskMap.description = retryCount
      }
    }

    renderTaskMap.description++
  }

  const taskList = Object.keys(renderTaskMap).filter((key) => renderTaskMap[key] < retryCount)

  if (!taskList.length) {
    isDone = true
  }
}

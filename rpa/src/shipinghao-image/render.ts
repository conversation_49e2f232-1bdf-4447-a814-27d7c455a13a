import type { IImageTask } from '../type'
import {
  getElementByText,
  getFileBuffer,
  manualDispatchFileEvent,
  onSendInput,
  retryCount,
  uint8ArrayToFile,
  baseStartPush,
  wait,
} from '../renderUtils'

const startPush = baseStartPush

const renderTaskMap = {
  image: 0,
  description: 0,
  title: 0,
  // music: 0,
}

async function renderImage(config: IImageTask) {
  const shadowm = document.querySelector('.wujie_iframe')
  if (!shadowm) {
    return
  }

  if (renderTaskMap.image < retryCount) {
    const inputDom = shadowm.shadowRoot.querySelector('.post-upload-wrap input[accept="image/*"]')

    if (inputDom) {
      const files = new DataTransfer()

      for (let i = 0; i < config.images.length; i++) {
        const uint8Array = await getFileBuffer(config.images[i].path)
        if (uint8Array) {
          const file = uint8ArrayToFile(
            uint8Array,
            `${config.title}_${Math.random()}.${config.images[i].format.toLocaleLowerCase()}`,
            `image/${config.images[i].format.toLocaleLowerCase()}`,
          )

          files.items.add(file)
        }
      }

      manualDispatchFileEvent({
        dom: inputDom as HTMLInputElement,
        element: HTMLInputElement,
        elementKey: 'files',
        value: files.files,
        event: 'change',
      })

      renderTaskMap.image = retryCount
      await wait(3000)
    }

    renderTaskMap.image++
  }
}

async function render(config: IImageTask) {
  const shadowm = document.querySelector('.wujie_iframe')
  if (!shadowm) {
    return
  }

  if (renderTaskMap.image < retryCount) {
    return
  }

  if (renderTaskMap.title < retryCount) {
    const inputDom = shadowm.shadowRoot.querySelector('input[placeholder="填写标题, 22个字符内"]')

    if (inputDom) {
      await onSendInput(inputDom as HTMLInputElement, config.title, config.tabId)
      renderTaskMap.title = retryCount
    }

    renderTaskMap.title++
  }

  if (renderTaskMap.description < retryCount) {
    const divDom = shadowm.shadowRoot.querySelector('.input-editor')

    if (divDom) {
      const text = config.description.split('___!!!!___')

      for (let i = 0; i < text.length; i++) {
        const item = text[i]

        if (item) {
          if (i) {
            await onSendInput(divDom as HTMLDivElement, `#`, config.tabId)
            await wait(200)
            await onSendInput(divDom as HTMLDivElement, `${item}`, config.tabId)
          } else {
            await onSendInput(divDom as HTMLDivElement, item, config.tabId)
            await wait(500)
          }
        }
      }

      renderTaskMap.description = retryCount
    }

    renderTaskMap.description++
  }

  // if (renderTaskMap.music < retryCount) {
  //   if (config.music) {
  //     const spanDom = getElementByText('span', '选择背景音乐')
  //     if (spanDom && spanDom instanceof HTMLSpanElement) {
  //       spanDom.click()
  //       await wait(300)

  //       const inputDom = document.querySelector('input[placeholder="搜索歌名/歌手/歌词/情绪"]')

  //       if (inputDom && inputDom instanceof HTMLInputElement) {
  //         manualDispatchFileEvent({
  //           dom: inputDom,
  //           element: HTMLInputElement,
  //           elementKey: 'value',
  //           value: config.music,
  //           event: 'change',
  //         })

  //         await wait(1000)

  //         let count = 0
  //         while (count < 10) {
  //           const items = document.querySelectorAll('.bgm-item-wrap')

  //           if (items && items.length) {
  //             for (const item of items) {
  //               const titleDom = item.querySelector('.title')
  //               if (titleDom && titleDom instanceof HTMLDivElement) {
  //                 if (titleDom.textContent === config.music) {
  //                   const event = new MouseEvent('mouseenter', {
  //                     bubbles: false,
  //                     cancelable: true,
  //                     view: window,
  //                   })
  //                   item.dispatchEvent(event)
  //                   await wait(200)

  //                   const divDom = item.querySelector('.selected-icon')
  //                   if (divDom && divDom instanceof HTMLDivElement) {
  //                     divDom.click()
  //                     break
  //                   }
  //                 }
  //               }
  //             }
  //           }

  //           count++
  //           await wait(1000)
  //         }

  //         renderTaskMap.music = retryCount
  //       }
  //     }
  //   } else {
  //     renderTaskMap.music = retryCount
  //   }

  //   renderTaskMap.music++
  // }

  const taskList = Object.keys(renderTaskMap).filter((key) => renderTaskMap[key] < retryCount)
  if (!taskList.length) {
    isDone = true
  }
}

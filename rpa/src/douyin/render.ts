import type { IVideoTask } from '../type'
import {
  getElementByText,
  getFileBuffer,
  manualDispatchFileEvent,
  onSendInput,
  uint8ArrayToFile,
  baseStartPush,
  wait,
  retryCount,
} from '../renderUtils'

const renderTaskMap = {
  video: 0,
  title: 0,
  description: 0,
  local: 0,
}

const startPush = baseStartPush

async function renderVideo(config: IVideoTask) {
  if (renderTaskMap.video < retryCount) {
    const videoDom = document.querySelector('.semi-tabs-content.semi-tabs-content-top')

    if (
      videoDom &&
      videoDom.firstChild instanceof HTMLDivElement &&
      videoDom.firstChild.className === 'semi-tabs-pane-active semi-tabs-pane'
    ) {
      const input = videoDom.firstChild.querySelector('input')
      const uint8Array = await getFileBuffer(config.videoPath)

      if (uint8Array) {
        const file = uint8ArrayToFile(uint8Array, config.videoFileName, config.videoMime)
        const files = new DataTransfer()
        files.items.add(file)

        manualDispatchFileEvent({
          dom: input,
          element: HTMLInputElement,
          elementKey: 'files',
          value: files.files,
          event: 'change',
        })

        renderTaskMap.video = retryCount
      }
    }

    renderTaskMap.video++
  }
}

async function render(config: IVideoTask) {
  if (renderTaskMap.video < retryCount) {
    return
  }

  if (renderTaskMap.title < retryCount) {
    if (!config.title) {
      renderTaskMap.title = retryCount
    } else {
      const titleDom = document.querySelector(
        'input[placeholder="填写作品标题，为作品获得更多流量"]',
      )

      if (titleDom && titleDom instanceof HTMLInputElement) {
        await onSendInput(titleDom, config.title.slice(0, 29), config.tabId)
        renderTaskMap.title = retryCount
      }

      renderTaskMap.title++
    }
  }

  if (renderTaskMap.description < retryCount) {
    if (!config.description) {
      renderTaskMap.description = retryCount
    } else {
      const descDom = document.querySelector('.zone-container.editor-kit-container.editor')

      if (descDom && descDom instanceof HTMLDivElement) {
        await onSendInput(descDom, config.description, config.tabId)
        renderTaskMap.description = retryCount
      }

      renderTaskMap.description++
    }
  }

  if (renderTaskMap.local < retryCount) {
    if (!config.locationKeyword) {
      renderTaskMap.local = retryCount
    } else {
      const localDom = document.querySelector('#douyin_creator_pc_anchor_jump')

      if (localDom && localDom instanceof HTMLDivElement) {
        const contentDom = localDom.querySelector('.semi-select-selection')
        if (contentDom && contentDom instanceof HTMLDivElement) {
          contentDom.click()
          await wait(200)
          const localInputDom = contentDom.querySelector('input')
          if (localInputDom && localInputDom instanceof HTMLInputElement) {
            await onSendInput(localInputDom, config.locationKeyword, config.tabId)
            await wait(500)

            const selectDom = document.querySelector(
              '.semi-select-option-list.semi-select-option-list-chosen',
            )

            if (selectDom && selectDom instanceof HTMLDivElement) {
              ;(selectDom.firstChild as HTMLDivElement).click()
            }

            renderTaskMap.local = retryCount
          }
        }
      }

      renderTaskMap.local++
    }
  }

  const taskList = Object.keys(renderTaskMap).filter((key) => renderTaskMap[key] < retryCount)

  if (!taskList.length) {
    isDone = true
  }
}

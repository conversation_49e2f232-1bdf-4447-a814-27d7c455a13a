export const genBaseCode = (code: string, serviceId: string) => `
      // 创建 <svg> 元素
const svgNS = "http://www.w3.org/2000/svg";
const xlinkNS = "http://www.w3.org/1999/xlink";

const svg = document.createElementNS(svgNS, "svg");
svg.setAttribute("xmlns", svgNS);
svg.setAttribute("viewBox", "0 0 100 100");
svg.setAttribute("preserveAspectRatio", "xMidYMid");
svg.setAttribute("width", "22");
svg.setAttribute("height", "22");
svg.style.shapeRendering = "auto";
svg.style.display = "block";
svg.style.background = "rgb(255, 255, 255)";

// 创建 <g> 元素
const g = document.createElementNS(svgNS, "g");

// 创建 <circle> 元素
const circle = document.createElementNS(svgNS, "circle");
circle.setAttribute("stroke-dasharray", "164.93361431346415 56.97787143782138");
circle.setAttribute("r", "35");
circle.setAttribute("stroke-width", "10");
circle.setAttribute("stroke", "#5b75e1");
circle.setAttribute("fill", "none");
circle.setAttribute("cx", "50");
circle.setAttribute("cy", "50");

// 创建 <animateTransform> 动画
const animateTransform = document.createElementNS(svgNS, "animateTransform");
animateTransform.setAttribute("keyTimes", "0;1");
animateTransform.setAttribute("values", "0 50 50;360 50 50");
animateTransform.setAttribute("dur", "1s");
animateTransform.setAttribute("repeatCount", "indefinite");
animateTransform.setAttribute("type", "rotate");
animateTransform.setAttribute("attributeName", "transform");

// 将 <animateTransform> 添加到 <circle> 中
circle.appendChild(animateTransform);

// 将 <circle> 添加到 <g> 中
g.appendChild(circle);

// 将 <g> 添加到 <svg> 中
svg.appendChild(g);

// 创建 <p> 元素用于显示文字
const text = document.createElement('p');
text.textContent = "自动化执行中";
text.style.margin = "0";
text.style.color = "#333"; // 文本颜色
text.style.fontSize = "14px"; // 文本大小

   const div = document.createElement('div')
   div.appendChild(svg)
   div.appendChild(text);
   div.style.display = 'flex'
   div.style.flexShrink = '0'
   div.style.alignItems = 'center'
   div.style.justifyContent = 'center'
   div.style.borderRadius = '8px'
   div.style.position = 'fixed'
   div.style.gap = '8px'
   div.style.padding = '4px 8px'
   div.style.backgroundColor = 'white'
   div.style.boxShadow = '0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05)'
   div.style.top = '8px'
   div.style.left = '50%'
   div.style.transition = 'all 0.3s ease-in-out'
   div.style.opacity = '1'
   div.style.zIndex = 9999;

   div.style.transform = 'translateX(-50%) scale(1)'
   document.body.appendChild(div)

      let isDone = false;${code};
      let isFocused = false;
      window.serviceId = '${serviceId}'
      ;0
    `

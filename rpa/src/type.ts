import type { CascadingPlatformDataItem } from '@common/structure'
import type { ImageFileInfo } from '@renderer/infrastructure/model'
import type { AiQiYiVideoCategoryItem } from '@renderer/infrastructure/services'

export interface IVideoTask {
  coverUrl: string
  tags: string[]
  topic: string[]
  accountId: string
  url: string
  videoPath: string
  videoFileName: string
  videoMime: string
  videoCover: string
  title: string
  description: string
  isOriginal: boolean
  locationKeyword: string
  tabId: string
  categories: CascadingPlatformDataItem<AiQiYiVideoCategoryItem>[]
}

export interface IImageTask {
  topic: string[]
  accountId: string
  url: string
  images: ImageFileInfo[]
  cover: string
  title: string
  description: string
  locationKeyword: string
  tabId: string
  music: string
}

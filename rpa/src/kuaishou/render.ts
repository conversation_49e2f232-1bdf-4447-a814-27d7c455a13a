import type { IVideoTask } from '../type'
import {
  getFileBuffer,
  manualDispatchFileEvent,
  onSendInput,
  retryCount,
  uint8ArrayToFile,
  baseStartPush,
  wait,
} from '../renderUtils'

const startPush = baseStartPush

const renderTaskMap = {
  video: 0,
  description: 0,
}

async function renderVideo(config: IVideoTask) {
  if (renderTaskMap.video < retryCount) {
    const videoDom = document.querySelector('#joyride-wrapper')

    if (videoDom && videoDom instanceof HTMLDivElement) {
      const input = videoDom.querySelector('input[type="file"]')

      if (input && input instanceof HTMLInputElement) {
        const uint8Array = await getFileBuffer(config.videoPath)

        if (uint8Array) {
          const file = uint8ArrayToFile(uint8Array, config.videoFileName, config.videoMime)
          const files = new DataTransfer()
          files.items.add(file)

          manualDispatchFileEvent({
            dom: input,
            element: HTMLInputElement,
            elementKey: 'files',
            value: files.files,
            event: 'change',
          })

          renderTaskMap.video = retryCount
        }
      }
    }

    renderTaskMap.video++
  }
}

async function render(config: IVideoTask) {
  if (renderTaskMap.video < retryCount) {
    return
  }

  if (renderTaskMap.description < retryCount) {
    if (!config.description) {
      renderTaskMap.description = retryCount
    } else {
      const descDom = document.getElementById('work-description-edit')

      if (descDom && descDom instanceof HTMLDivElement) {
        const text = config.description.split('___!!!!___')

        for (let i = 0; i < text.length; i++) {
          const item = text[i]

          if (item) {
            if (i) {
              await onSendInput(descDom, '#', config.tabId)
              await wait(300)
              await onSendInput(descDom, `${item} `, config.tabId)
              await wait(300)
            } else {
              await onSendInput(descDom, item, config.tabId)
              await wait(300)
            }
          }
        }

        renderTaskMap.description = retryCount
      }
    }

    renderTaskMap.description++
  }

  const taskList = Object.keys(renderTaskMap).filter((key) => renderTaskMap[key] < retryCount)

  if (!taskList.length) {
    isDone = true
  }
}

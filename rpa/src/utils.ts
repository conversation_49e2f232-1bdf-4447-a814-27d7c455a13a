import { genBaseCode } from './baseCode'
import type { IImageTask, IVideoTask } from './type'

export function wait(time = 1000) {
  return new Promise((resolve) => {
    setTimeout(resolve, time)
  })
}

export function videoRun(code: string, key: string) {
  return async (task: IVideoTask) => {
    const browserContext = browserContexts[task.accountId]
    const tab = await browserService.openPublishTab(browserContext, task.url)

    task.tabId = tab.id

    try {
      await tab.view.webContents.executeJavaScript(genBaseCode(code, serviceId))
    } catch (error) {
      console.log('executeJavaScript', error)
    }

    // eslint-disable-next-line no-constant-condition
    while (true) {
      await wait(1000)
      if (!tab.view.webContents) {
        break
      }

      if (!tab.view.isLoad) {
        continue
      }

      if (tab.view.webContents.isLoading()) {
        continue
      }

      await tab.view.webContents.executeJavaScript(`
          isFocused = ${JSON.stringify(tab.view.webContents.isFocused())}
        `)

      await tab.view.webContents.executeJavaScript(`
          if (document) {
            renderVideo(${JSON.stringify(task)})
          }
        `)

      if (browserService.isActiveTab(tab.id)) {
        await tab.view.webContents.executeJavaScript(`
              if (document) {
                render(${JSON.stringify(task)})
              }
            `)

        const isDone = await tab.view.webContents.executeJavaScript(`isDone`)
        if (isDone) {
          await tab.view.webContents.executeJavaScript(`
              div.style.transform = 'translateX(-50%) scale(0)'
              div.style.opacity = '0'
            `)
          console.log(`render ${key} done`)
          break
        }
      }

      await wait(1000)
    }
  }
}

export function imageRun(code: string, key: string) {
  return async (task: IImageTask) => {
    const browserContext = browserContexts[task.accountId]
    const tab = await browserService.openPublishTab(browserContext, task.url)

    task.tabId = tab.id

    try {
      await tab.view.webContents.executeJavaScript(genBaseCode(code, serviceId))
    } catch (error) {
      //
    }

    // eslint-disable-next-line no-constant-condition
    while (true) {
      await wait(1000)
      if (!tab.view.webContents) {
        break
      }

      if (!tab.view.isLoad) {
        continue
      }

      if (tab.view.webContents.isLoading()) {
        continue
      }

      await tab.view.webContents.executeJavaScript(`
          isFocused = ${JSON.stringify(tab.view.webContents.isFocused())}
        `)

      await tab.view.webContents.executeJavaScript(`
          if (document) {
            renderImage(${JSON.stringify(task)})
          }
        `)

      if (browserService.isActiveTab(tab.id)) {
        await tab.view.webContents.executeJavaScript(`
              if (document) {
                render(${JSON.stringify(task)})
              }
            `)

        const isDone = await tab.view.webContents.executeJavaScript(`isDone`)
        if (isDone) {
          await tab.view.webContents.executeJavaScript(`
              div.style.transform = 'translateX(-50%) scale(0)'
              div.style.opacity = '0'
            `)
          console.log(`render ${key} done`)
          break
        }
      }

      await wait(1000)
    }
  }
}

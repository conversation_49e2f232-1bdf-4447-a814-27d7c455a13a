import fs from 'fs-extra'
import mime from 'mime'
import path from 'path'
import { wait } from './utils'
import { douyinRun } from './douyin'
import { weiboRun } from './weibo'
import { kuaishouRun } from './kuaishou'
import { xiaohongshuRun } from './xiaohongshu'
import { baijiahaoRun } from './baijiahao'
import { bilibiliRun } from './bilibili'
import { shipinghaoRun } from './shipinghao'
import { toutiaoRun } from './toutiao'
import { zhihuRun } from './zhihu'
import { qieRun } from './qie'
import { souhuRun } from './souhu'
import { yidianRun } from './yidian'
import { wangyiRun } from './wangyi'
import { aqyRun } from './aqy'
import { weishiRun } from './weishi'
import { douyinImageRun } from './douyin-image'
import type {
  LocalImageTextPushingConfig,
  LocalVideoPushingConfig,
} from '@renderer/infrastructure/model/config/pushing-config'
import { kuaishouImageRun } from './kuaishou-image'
import { xiaohongshuImageRun } from './xiaohongshu-image'
import { shipinghaoImageRun } from './shipinghao-image'
import { weiboImageRun } from './weibo-image'

async function videoStart() {
  const TargetPlatformUrl = {
    [platformNames.DouYin]: 'https://creator.douyin.com/creator-micro/content/upload',
    [platformNames.XinLangWeiBo]: 'https://weibo.com/upload/channel',
    [platformNames.KuaiShou]: 'https://cp.kuaishou.com/article/publish/video?tabType=1',
    [platformNames.XiaoHongShu]: 'https://creator.xiaohongshu.com/publish/publish?from=menu',
    [platformNames.BaiJiaHao]: 'https://baijiahao.baidu.com/builder/rc/edit?type=videoV2',
    [platformNames.BiliBili]: 'https://member.bilibili.com/platform/upload/video/frame',
    [platformNames.WeiXinShiPinHao]: 'https://channels.weixin.qq.com/platform/post/create',
    [platformNames.TouTiaoHao]: 'https://mp.toutiao.com/profile_v4/xigua/upload-video',
    [platformNames.ZhiHu]: 'https://www.zhihu.com/zvideo/upload-video',
    [platformNames.QiEHao]: 'https://om.qq.com/main/creation/video',
    [platformNames.SouHuHao]: 'https://mp.sohu.com/mpfe/v4/contentManagement/news/addvideo',
    [platformNames.YiDianHao]: 'https://mp.yidianzixun.com/#/Writing/videoEditor',
    [platformNames.WangYiHao]: 'https://mp.163.com/subscribe_v4/index.html#/video-publish',
    [platformNames.AiQiYi]: 'https://mp.iqiyi.com/wemedia/publish/video',
    [platformNames.TengXunWeiShi]: 'https://media.weishi.qq.com/',
  }

  const TargetPlatformProcess = {
    [platformNames.DouYin]: douyinRun,
    [platformNames.XinLangWeiBo]: weiboRun,
    [platformNames.KuaiShou]: kuaishouRun,
    [platformNames.XiaoHongShu]: xiaohongshuRun,
    [platformNames.BaiJiaHao]: baijiahaoRun,
    [platformNames.BiliBili]: bilibiliRun,
    [platformNames.WeiXinShiPinHao]: shipinghaoRun,
    [platformNames.TouTiaoHao]: toutiaoRun,
    [platformNames.ZhiHu]: zhihuRun,
    [platformNames.QiEHao]: qieRun,
    [platformNames.SouHuHao]: souhuRun,
    [platformNames.YiDianHao]: yidianRun,
    [platformNames.WangYiHao]: wangyiRun,
    [platformNames.AiQiYi]: aqyRun,
    [platformNames.TengXunWeiShi]: weishiRun,
  }

  for (let i = 0; i < startConfigs.length; i++) {
    const config = startConfigs[i] as LocalVideoPushingConfig

    const topic = []

    let description = config.description.replace(/&nbsp;/g, ' ')
    description = description.replace(/<topic text="(.*?)">(.*?)<\/topic>/g, (match, p1) => {
      topic.push(p1)
      switch (config.platform.name) {
        case platformNames.DouYin:
          return `#${p1} `
        case platformNames.XinLangWeiBo:
          return `#${p1}# `
        case platformNames.BiliBili:
        case platformNames.TouTiaoHao:
        case platformNames.BaiJiaHao:
          return ''
        case platformNames.XiaoHongShu:
          return ``
        case platformNames.KuaiShou:
          return `___!!!!___${p1}`
        case platformNames.WeiXinShiPinHao:
          return `___!!!!___${p1} `
        default:
          return `#${p1}`
      }
    })
    description = description.replace(/<br\s*\/?>/gi, '\n')
    description = description.replace(/<[^>]+>/g, '')

    const process = TargetPlatformProcess[config.platform.name]

    if (process && fs.existsSync(config.video.filePath)) {
      process({
        coverUrl: config.coverUrl,
        categories: config.platformConfigs[config.platform.name]?.categories,
        tags: config.tags,
        topic,
        accountId: config.accountId,
        url: TargetPlatformUrl[config.platform.name],
        videoPath: config.video.filePath,
        videoFileName: path.basename(config.video.filePath),
        videoMime: mime.getType(config.video.filePath),
        videoCover: config.cover.path,
        title: config.title,
        description,
        isOriginal: config.isOriginal,
        locationKeyword: config.locationKeyword,
        tabId: '',
      })
    }

    await wait(2000)
  }
}

function imageTextStart() {
  const TargetPlatformUrl = {
    [platformNames.DouYin]: 'https://creator.douyin.com/creator-micro/content/upload?default-tab=3',
    [platformNames.KuaiShou]: 'https://cp.kuaishou.com/article/publish/video?tabType=2',
    [platformNames.XiaoHongShu]: 'https://creator.xiaohongshu.com/publish/publish?from=menu',
    [platformNames.WeiXinShiPinHao]:
      'https://channels.weixin.qq.com/platform/post/finderNewLifeCreate',
    [platformNames.XinLangWeiBo]: 'https://weibo.com/',
  }

  const TargetPlatformProcess = {
    [platformNames.DouYin]: douyinImageRun,
    [platformNames.KuaiShou]: kuaishouImageRun,
    [platformNames.XiaoHongShu]: xiaohongshuImageRun,
    [platformNames.WeiXinShiPinHao]: shipinghaoImageRun,
    [platformNames.XinLangWeiBo]: weiboImageRun,
  }

  for (let i = 0; i < startConfigs.length; i++) {
    const config = startConfigs[i] as LocalImageTextPushingConfig

    const topic = []

    let description = config.description.replace(/&nbsp;/g, ' ')

    description = description.replace(/<topic text="(.*?)">(.*?)<\/topic>/g, (match, p1) => {
      topic.push(p1)
      switch (config.platform.name) {
        case platformNames.DouYin:
          if (topic.length > 5) {
            return `${p1} `
          }
          return `#${p1} `
        case platformNames.XinLangWeiBo:
          return ` #${p1}# `
        case platformNames.BiliBili:
        case platformNames.TouTiaoHao:
        case platformNames.BaiJiaHao:
          return ''
        case platformNames.KuaiShou:
        case platformNames.XiaoHongShu:
          return `___!!!!___${p1}`
        case platformNames.WeiXinShiPinHao:
          return `___!!!!___${p1} `
        default:
          return `#${p1}`
      }
    })
    description = description.replace(/<br\s*\/?>/gi, '\n')
    description = description.replace(/<[^>]+>/g, '')

    const process = TargetPlatformProcess[config.platform.name]

    if (process) {
      process({
        topic,
        accountId: config.accountId,
        url: TargetPlatformUrl[config.platform.name],
        images: config.images,
        cover: config.cover.path,
        title: config.title,
        description,
        locationKeyword: config.locationKeyword,
        tabId: '',
        music: config.music[config.platform.name]?.title,
      })
    }
  }
}

switch (type) {
  case 'video':
    videoStart()
    break
  case 'image-text':
    imageTextStart()
    break
}

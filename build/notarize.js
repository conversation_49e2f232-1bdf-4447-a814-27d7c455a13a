const { notarize } = require('@electron/notarize')

exports.default = async function packageTask(context) {
  const appName = context.packager.appInfo.productFilename
  const { electronPlatformName, appOutDir } = context

  // Only notarize the app on macOS only.
  if (electronPlatformName !== 'darwin') {
    return
  }

  if (process.env.CI !== 'true') {
    console.warn('Skipping notarizing step. Packaging is not running in CI')
    return
  }

  if (!('CI_APPLEID' in process.env && 'CI_APPLEIDPASSWORD' in process.env)) {
    console.warn('Skipping notarizing step. APPLE_ID and APPLE_ID_PASS env variables must be set')
    return
  }

  const appPath = `${appOutDir}/${appName}.app`
  const appleId = process.env.CI_APPLEID
  const appBundleId = 'yixiaoer-lite'
  const appleIdPassword = process.env.CI_APPLEIDPASSWORD
  const teamId = process.env.CI_APPLE_TEAM_ID

  return await notarize({
    teamId,
    appBundleId,
    appPath,
    appleId,
    appleIdPassword,
  })
}

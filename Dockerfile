# 编译源码
FROM node:20 AS build

ARG ENV_TYPE
WORKDIR /app
COPY . .
RUN npm config set registry https://registry.npmmirror.com
# 全局安装 pnpm
RUN npm install -g pnpm

# 复制 package.json 和 pnpm-lock.yaml (如果存在)
COPY package.json pnpm-lock.yaml* ./

# 设置镜像源并安装依赖
RUN pnpm config set registry https://registry.npmmirror.com
RUN pnpm install
# 执行构建
RUN /bin/sh -c "pnpm run ${ENV_TYPE}:build-web"

# ARG MIRROR_URL=https://atomhub.openatom.cn/
# 运行环境
# FROM hub.atomgit.com/library/nginx:1.24-bullseye-perl AS runner
# FROM ${MIRROR_URL}library/nginx:stable-alpine AS runner
FROM nginx:stable AS runner
WORKDIR /usr/share/nginx/html
COPY --from=build /app/src/renderer/out/renderer .
COPY nginx.conf /etc/nginx/conf.d/default.conf
ENV TZ=Asia/Shanghai
EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]

<script src="shared.js"></script>
<link rel="stylesheet" href="shared.css" />
<style>
  html, body {
    min-width: 460px;
    min-height: 133px;
  }

  hr {
    width: 100%;
  }
</style>
<p>
  <b>This page includes an extra development build of React. &#x1f6a7;</b>
</p>
<p>
  The React build on this page includes both development and production versions because dead code elimination has not been applied correctly.
  <br />
  <br />
  This makes its size larger, and causes React to run slower.
  <br />
  <br />
  Make sure to <a href="https://reactjs.org/docs/optimizing-performance.html#use-the-production-build">set up dead code elimination</a> before deployment.
</p>
<hr />
<p>
  Open the developer tools, and  "Components" and "Profiler" tabs will appear to the right.
</p>

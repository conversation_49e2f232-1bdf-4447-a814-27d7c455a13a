{"version": 3, "file": "installHook.js", "mappings": "mBAGKA,OAAOC,eAAe,qCCuBpB,SAAqBC,GAC1B,GAAIA,EAAOD,eAAe,kCACxB,OAAO,KAGT,IAAIE,EAAwBC,QACxBC,EAA+C,CAAC,EACpD,IAAK,MAAMC,KAAUF,QACnBC,EAAqBC,GAAUF,QAAQE,GAoPzC,IAAIC,EAAY,KAQhB,SAASC,yCACPC,GAYA,GAAkB,OAAdF,EAEF,OAGF,MAAMG,EAAiD,CAAC,EAExDH,EAAY,KACV,IAAK,MAAMD,KAAUI,EACnB,IACEP,EAAcG,GAAUI,EAAuBJ,EAChD,CAAC,MAAOK,GAAS,CACnB,EAtB4B,CAC7B,QACA,QACA,iBACA,OACA,MACA,QACA,QAkBqBC,SAAQN,IAC7B,IACE,MAAMO,EAAkBH,EAAuBJ,GAAUH,EACvDG,GACAQ,+CACEX,EAAcG,GAAQQ,+CACtBX,EAAcG,GAEZS,eAAiB,IAAIC,KAEpBP,GAODI,EC7Q6B,iCDiKzC,SACEI,KACGC,GAEH,GAAyB,IAArBA,EAAUC,QAAwC,iBAAjBF,EACnC,MAAO,CAACA,KAAiBC,GAG3B,MAAMF,EAAOE,EAAUE,QAEvB,IAAIC,EAAW,GACXC,EAAmB,EACvB,IAAK,IAAIC,EAAI,EAAGA,EAAIN,EAAaE,SAAUI,EAAG,CAC5C,MAAMC,EAAcP,EAAaM,GACjC,GAAoB,MAAhBC,EAAqB,CACvBH,GAAYG,EACZ,QACD,CAED,MAAMC,EAAWR,EAAaM,EAAI,GAIlC,SAHEA,EAGME,GACN,IAAK,IACL,IAAK,IACL,IAAK,MACDH,EACFD,GAAa,IAAGI,IAEhB,MAEF,IAAK,IACL,IAAK,IAAK,CACR,MAAOC,GAAOV,EAAKW,OAAOL,EAAkB,GAC5CD,GAAYO,SAASF,EAAK,IAAIG,WAE9B,KACD,CACD,IAAK,IAAK,CACR,MAAOH,GAAOV,EAAKW,OAAOL,EAAkB,GAC5CD,GAAYS,WAAWJ,GAAKG,WAE5B,KACD,CACD,IAAK,IAAK,CACR,MAAOH,GAAOV,EAAKW,OAAOL,EAAkB,GAC5CD,GAAYK,EAAIG,UACjB,EAEJ,CAED,MAAO,CAACR,KAAaL,EACtB,CAyDgBe,IAA0Bf,GAGlC,EAGHD,eAAeD,+CACbD,EACFA,EAAemB,+CACbjB,eAEFZ,EAAcG,GAAUS,cACzB,CAAC,MAAOJ,GAAS,IAErB,CAUD,IAAIsB,EAAa,EA8CbC,GAAoB,EAsGxB,MAAMC,EAAiD,GACjDC,EAA4D,GAElE,SAASC,uBAAuB1B,GAC9B,MAAM2B,EAAS3B,EAAM4B,MAAMC,MAAM,MAEjC,OADcF,EAAOnB,OAAS,EAAImB,EAAO,GAAK,IAE/C,CA0BD,MAAMG,EAAyC,CAAC,EAC1CC,EAAqB,IAAIC,IACzBC,EAAwC,CAAC,EACzCC,EAAY,IAAIF,IAChBG,EAAW,IAAIH,IAEfI,EAAqB,CACzBL,qBACAE,YAEAE,WAGAD,YAEAG,KA3HF,SAAcC,EAAeC,GACvBN,EAAUK,IACZL,EAAUK,GAAOE,KAAIC,GAAMA,EAAGF,IAEjC,EAwHCG,cAtHF,SAAuBC,GACrB,MAAMC,EAAQd,EAId,OAHKc,EAAMD,KACTC,EAAMD,GAAc,IAAIE,KAEnBD,EAAMD,EACd,EAiHCG,OApMF,SAAgBC,GACd,MAAMC,IAAO1B,EACbY,EAAUe,IAAID,EAAID,GAElB,MAAMG,EAAiB3B,EACnB,WA5TN,SAA8BwB,GAC5B,IACE,GAAgC,iBAArBA,EAASI,QAElB,OAAIJ,EAASK,WAAa,EAIjB,cAKF,aAST,MAAMlC,EAAWmC,SAASC,UAAUpC,SACpC,GAAI6B,EAASQ,OAASR,EAASQ,MAAMC,wBAAyB,CAE5D,MAAMC,EAAiBvC,EAASwC,KAC9BX,EAASQ,MAAMC,yBAGjB,OAA2C,IAAvCC,EAAeE,QAAQ,YAElB,cAKwC,IAA7CF,EAAeE,QAAQ,iBAClB,eAMoD,IAAzDF,EAAeE,QAAQ,8BAImB,IAAxCF,EAAeE,QAAQ,cAKoB,IAA3CF,EAAeE,QAAQ,iBAOa,IAApCF,EAAeE,QAAQ,QAXlB,eAoBoC,IAA3CF,EAAeE,QAAQ,iBAEsB,IAA7CF,EAAeE,QAAQ,iBAIhB,aAGA,eAQkC,IAA3CF,EAAeE,QAAQ,iBAEsB,IAA7CF,EAAeE,QAAQ,iBAEhB,aAIF,UACR,CACF,CAAC,MAAOC,GAKR,CACD,MAAO,YACR,CAuNKC,CAAqBd,GASzB,GAAIxD,EAAOD,eAAe,wCAAyC,CACjE,MAAM,4BAACwE,EAAD,8BAA8BC,GAClCxE,EAAOyE,qCAEgC,mBAAhCF,GACkC,mBAAlCC,IAEPD,EAA4Bf,GAC5BgB,IAEH,CAID,MAAME,EAAS1E,EAAO2E,0BACtB,GAAsB,mBAAXD,EAAuB,CAChC,MAAME,EAAoBF,EAAO7B,EAAMY,EAAID,EAAUxD,GACrD6C,EAAKL,mBAAmBkB,IAAID,EAAImB,EACjC,CAQD,OANA/B,EAAKC,KAAK,WAAY,CACpBW,KACAD,WACAG,mBAGKF,CACR,EA2JCoB,GAlJF,SAAY9B,EAAeG,GACpBR,EAAUK,KACbL,EAAUK,GAAS,IAErBL,EAAUK,GAAO+B,KAAK5B,EACvB,EA8IC6B,IA5IF,SAAahC,EAAeG,GAC1B,IAAKR,EAAUK,GACb,OAEF,MAAMiC,EAAQtC,EAAUK,GAAOqB,QAAQlB,IACxB,IAAX8B,GACFtC,EAAUK,GAAOtB,OAAOuD,EAAO,GAE5BtC,EAAUK,GAAO9B,eACbyB,EAAUK,EAEpB,EAkICkC,IAzJF,SAAalC,EAAeG,GAE1B,OADAL,EAAKgC,GAAG9B,EAAOG,GACR,IAAML,EAAKkC,IAAIhC,EAAOG,EAC9B,EA0JCgC,eAAe,EAGfC,SA7ZF,SAAkBjC,GAGhB,IAEmBY,SAASC,UAAUpC,SACdwC,KAAKjB,GAKlBkB,QAAQ,QAAU,IAEzBpC,GAAoB,EAIpBoD,YAAW,WACT,MAAM,IAAIC,MACR,8LAKH,IAEJ,CAAC,MAAOhB,GAAO,CACjB,EAmYCiB,qBA1HF,SAA8BlC,EAAwBmC,GACpD,MAAMX,EAAoBpC,EAAmBgD,IAAIpC,GACxB,MAArBwB,GACFA,EAAkBa,yBAAyBF,EAE9C,EAsHCG,kBApHF,SACEtC,EACAuC,EACAC,GAEA,MAAMC,EAAehD,EAAKM,cAAcC,GAClC0C,EAAUH,EAAKG,QACfC,EAAcF,EAAaG,IAAIL,GAC/BM,EACqB,MAAzBH,EAAQI,eAA0D,MAAjCJ,EAAQI,cAAcC,QAGpDJ,GAAgBE,EAEVF,GAAeE,GACxBJ,EAAaO,OAAOT,GAFpBE,EAAaQ,IAAIV,GAInB,MAAMf,EAAoBpC,EAAmBgD,IAAIpC,GACxB,MAArBwB,GACFA,EAAkB0B,sBAAsBX,EAAMC,EAEjD,EAgGCW,sBA9FF,SAA+BnD,EAAwBuC,GACrD,MAAMf,EAAoBpC,EAAmBgD,IAAIpC,GACxB,MAArBwB,GACFA,EAAkB4B,0BAA0Bb,EAE/C,EA0FCc,cAxFF,SAAuBrD,EAAwBsD,GAC7C,MAAM9B,EAAoBpC,EAAmBgD,IAAIpC,GACxB,MAArBwB,EACE8B,EACF9B,EAAkB+B,4BAElB/B,EAAkBgC,8BAKhBF,EAIFpG,0CAFiE,IAA/DR,OAAO+G,qDA/IK,OAAdxG,IACFA,IACAA,EAAY,KAoJf,EAyECyG,wBA5DF,WAGE,OAAO5E,CACR,EAyDC6E,4BAvDF,SAAqCtG,GACnC,MAAMuG,EAAkB7E,uBAAuB1B,GACvB,OAApBuG,GACF/E,EAAsB6C,KAAKkC,EAE9B,EAmDCC,2BAjDF,SAAoCxG,GAClC,GAAIwB,EAAsBhB,OAAS,EAAG,CACpC,MAAM+F,EAAkB/E,EAAsBiF,MACxCC,EAAiBhF,uBAAuB1B,GACvB,OAAnB0G,GACFjF,EAAa4C,KAAK,CAACkC,EAAiBG,GAEvC,CACF,GAiDDC,OAAOC,eACLrH,EACA,iCACC,CAGCsH,cAAcC,EACdC,YAAY,EACZhC,IAAG,IACM3C,GAMd,CDnlBC4E,CAAY3H,QAGZA,OAAO4H,+BAA+B7C,GACpC,YACA,UAAU,eAAClB,IACT7D,OAAO6H,YACL,CACEC,OAAQ,sBACRC,QAAS,CACPC,KAAM,0BACNnE,mBAGJ,IAEH,IAIH7D,OAAO4H,+BAA+BK,mBAAqBX,OAAOY,OAClElI,OAAO4H,+BAA+BO,UAAYxF,IAClD3C,OAAO4H,+BAA+BQ,cAAgBC,QACtDrI,OAAO4H,+BAA+BU,UAAY9E,I", "sources": ["../src/contentScripts/installHook.js", "../../react-devtools-shared/src/hook.js", "../../react-devtools-shared/src/constants.js"], "names": ["window", "hasOwnProperty", "target", "targetConsole", "console", "targetConsoleMethods", "method", "unpatchFn", "patchConsoleForInitialCommitInStrictMode", "hideConsoleLogsInStrictMode", "originalConsoleMethods", "error", "for<PERSON>ach", "originalMethod", "__REACT_DEVTOOLS_STRICT_MODE_ORIGINAL_METHOD__", "override<PERSON><PERSON><PERSON>", "args", "maybeMessage", "inputArgs", "length", "slice", "template", "argumentsPointer", "i", "currentChar", "nextChar", "arg", "splice", "parseInt", "toString", "parseFloat", "formatConsoleArguments", "__REACT_DEVTOOLS_STRICT_MODE_OVERRIDE_METHOD__", "uid<PERSON><PERSON><PERSON>", "hasDetectedBadDCE", "openModuleRangesStack", "moduleRanges", "getTopStackFrameString", "frames", "stack", "split", "fiberRoots", "rendererInterfaces", "Map", "listeners", "renderers", "backends", "hook", "emit", "event", "data", "map", "fn", "getFiberRoots", "rendererID", "roots", "Set", "inject", "renderer", "id", "set", "reactBuildType", "version", "bundleType", "Function", "prototype", "Mount", "_renderNewRootComponent", "renderRootCode", "call", "indexOf", "err", "detectReactBuildType", "registerRendererWithConsole", "patchConsoleUsingWindowValues", "__REACT_DEVTOOLS_CONSOLE_FUNCTIONS__", "attach", "__REACT_DEVTOOLS_ATTACH__", "rendererInterface", "on", "push", "off", "index", "sub", "supportsFiber", "checkDCE", "setTimeout", "Error", "onCommitFiberUnmount", "fiber", "get", "handleCommitFiberUnmount", "onCommitFiberRoot", "root", "priorityLevel", "mountedRoots", "current", "isKnownRoot", "has", "isUnmounting", "memoizedState", "element", "delete", "add", "handleCommitFiberRoot", "onPostCommitFiberRoot", "handlePostCommitFiberRoot", "setStrictMode", "isStrictMode", "patchConsoleForStrictMode", "unpatchConsoleForStrictMode", "__REACT_DEVTOOLS_HIDE_CONSOLE_LOGS_IN_STRICT_MODE__", "getInternalModuleRanges", "registerInternalModuleStart", "startStackFrame", "registerInternalModuleStop", "pop", "stopStackFrame", "Object", "defineProperty", "configurable", "__DEV__", "enumerable", "installHook", "__REACT_DEVTOOLS_GLOBAL_HOOK__", "postMessage", "source", "payload", "type", "nativeObjectCreate", "create", "nativeMap", "nativeWeakMap", "WeakMap", "nativeSet"], "sourceRoot": "", "ignoreList": [0, 1, 2]}
# 负责人表格排序功能

## 功能概述

我们为负责人数据表格（PrincipalDataTable）添加了完整的排序功能，用户可以通过点击表头对各个数据列进行排序。

## 功能特性

### 1. 支持的排序字段
- **负责人**：按姓名字母顺序排序
- **发布**：按发布总数排序
- **粉丝**：按粉丝总数排序
- **播放**：按播放总数（播放+阅读）排序
- **评论**：按评论总数排序
- **点赞**：按点赞总数排序
- **收藏**：按收藏总数排序

### 2. 排序状态
每个字段支持三种排序状态：
- **无排序**：默认状态，数据按原始顺序显示
- **升序**：数据从小到大排序（箭头向上高亮）
- **降序**：数据从大到小排序（箭头向下高亮）

### 3. 排序交互
- **首次点击**：设置为升序排序
- **再次点击**：切换为降序排序
- **第三次点击**：取消排序，恢复原始顺序
- **点击其他列**：直接设置该列为升序排序

### 4. 视觉反馈
- 每个表头都有上下箭头图标
- 当前排序列的箭头会高亮显示（蓝色）
- 非排序列的箭头显示为灰色
- 表头支持悬停效果，提升用户体验

## 技术实现

### 核心组件
```typescript
// 排序状态类型
type SortField = 'name' | 'publish' | 'fans' | 'play' | 'comments' | 'likes' | 'favorites'
type SortOrder = 'asc' | 'desc' | null

interface SortState {
  field: SortField | null
  order: SortOrder
}
```

### 排序逻辑
- 使用项目中现有的 `sort.by` 工具函数
- 支持数字和字符串类型的排序
- 播放数据为播放量和阅读量的总和
- 中文姓名按Unicode编码排序

### 可排序表头组件
```typescript
const SortableTableHead = ({ field, children }: { field: SortField; children: React.ReactNode }) => {
  // 显示排序图标和处理点击事件
}
```

## 使用方法

### 基本用法
```typescript
<PrincipalDataTable 
  data={principalData} 
  showSearch={true} 
  showExport={true} 
/>
```

### 排序操作
1. 点击任意表头开始排序
2. 观察箭头图标的变化
3. 再次点击切换排序方向
4. 第三次点击取消排序

## 测试覆盖

我们为排序功能编写了完整的单元测试：
- 名称升序/降序排序
- 各数值字段的升序/降序排序
- 播放数据（播放+阅读）的正确计算
- 排序算法的正确性验证

运行测试：
```bash
npx vitest run src/renderer/src/pages/Overview/components/PrincipalDataTable.test.tsx --config vitest.web.config.mts
```

## 兼容性

- 与现有的搜索功能完全兼容
- 与导出功能完全兼容
- 排序后的数据会应用到搜索和导出操作中
- 保持原有的表格样式和布局

## 性能优化

- 使用 `useMemo` 缓存排序结果
- 只在数据、搜索条件或排序状态变化时重新计算
- 排序操作不会影响原始数据

## 后续改进建议

1. **记忆排序状态**：在用户会话中保持排序偏好
2. **多列排序**：支持按多个字段进行复合排序
3. **自定义排序**：允许用户自定义排序规则
4. **排序指示器**：在表头显示排序优先级数字
5. **键盘支持**：支持键盘快捷键进行排序操作

## 注意事项

- 中文姓名排序基于Unicode编码，可能与拼音排序有差异
- 数值为0或null的记录会排在数值排序的最前面（升序）或最后面（降序）
- 排序状态在组件重新挂载时会重置

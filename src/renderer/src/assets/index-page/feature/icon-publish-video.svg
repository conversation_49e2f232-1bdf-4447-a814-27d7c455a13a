<?xml version="1.0" encoding="UTF-8"?>
<svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>视频</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#A881FF" offset="0%"></stop>
            <stop stop-color="#DCCCFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="3.0993801%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#ECE5FF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <path d="M22.685009,16.3687343 C22.5347876,17.1343739 21.7091818,17.701758 20.0581231,18.8368321 L17.3376166,20.7069509 C15.2524151,22.1403262 14.2097378,22.8571668 13.3535829,22.6229621 C13.0853261,22.5495342 12.8362981,22.4185876 12.6238771,22.238995 C11.9458615,21.6661038 11.9458615,20.401001 11.9458615,17.8704894 L11.9458615,14.1297929 C11.9458615,11.599113 11.9458615,10.3337807 12.623923,9.76087424 C12.8363746,9.58137343 13.0854178,9.45036568 13.35369,9.3770142 C14.2098908,9.14287074 15.2525681,9.85977254 17.3379226,11.2935762 L20.0584291,13.164215 C21.7094877,14.2992892 22.5349406,14.8668262 22.685009,15.6324658 C22.7327372,15.875543 22.7327372,16.1256571 22.685009,16.3687343 Z" id="path-3"></path>
        <filter x="-92.8%" y="-60.0%" width="285.6%" height="250.0%" filterUnits="objectBoundingBox" id="filter-4">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.705882353   0 0 0 0 0.568627451   0 0 0 0 1  0 0 0 0.801027098 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <filter x="-62.6%" y="-35.6%" width="225.3%" height="201.3%" filterUnits="objectBoundingBox" id="filter-5">
            <feOffset dx="0" dy="1.5" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
    </defs>
    <g id="新版首页1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="新版首页-普通用户" transform="translate(-240.000000, -60.000000)">
            <g id="编组-34" transform="translate(200.000000, 20.000000)">
                <g id="视频" transform="translate(40.000000, 40.000000)">
                    <rect id="矩形" fill="url(#linearGradient-1)" x="0" y="0" width="32" height="32" rx="8"></rect>
                    <g id="路径">
                        <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                        <use fill="url(#linearGradient-2)" fill-rule="evenodd" xlink:href="#path-3"></use>
                        <use fill="black" fill-opacity="1" filter="url(#filter-5)" xlink:href="#path-3"></use>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>
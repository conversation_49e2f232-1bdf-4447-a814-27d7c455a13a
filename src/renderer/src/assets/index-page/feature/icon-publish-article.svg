<?xml version="1.0" encoding="UTF-8"?>
<svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>发文章</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#5398FF" offset="0%"></stop>
            <stop stop-color="#9DC5FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0.464379371%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#FF70A3" offset="0%"></stop>
            <stop stop-color="#FFB1C0" offset="100%"></stop>
        </linearGradient>
        <rect id="path-3" x="0" y="0" width="32" height="32" rx="8"></rect>
        <filter x="-88.9%" y="-93.6%" width="277.8%" height="287.1%" filterUnits="objectBoundingBox" id="filter-5">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.423529412   0 0 0 0 0.596078431  0 0 0 0.7 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-6">
            <stop stop-color="#FFD7E1" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <path d="M14.9981411,15.6779309 C14.9981411,16.463318 15.6348232,17.1 16.4202103,17.1 L16.4,17.099 L2.84413821,17.1 C1.27336405,17.1 -4.4408921e-16,15.826636 0,14.2558618 L0,2.84413821 C-4.4408921e-16,1.27336405 1.27336405,0 2.84413821,0 L12.1540029,0 C13.7247771,-4.4408921e-16 14.9981411,1.27336405 14.9981411,2.84413821 Z" id="path-7"></path>
        <filter x="-3.0%" y="-2.9%" width="106.1%" height="105.8%" filterUnits="objectBoundingBox" id="filter-8">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.9 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
    </defs>
    <g id="新版首页1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="新版首页-普通用户" transform="translate(-630.000000, -60.000000)">
            <g id="编组-34" transform="translate(200.000000, 20.000000)">
                <g id="发文章" transform="translate(430.000000, 40.000000)">
                    <circle id="椭圆形备份-6" fill="url(#linearGradient-1)" cx="16" cy="16" r="16"></circle>
                    <mask id="mask-4" fill="white">
                        <use xlink:href="#path-3"></use>
                    </mask>
                    <use id="矩形" fill="url(#linearGradient-2)" xlink:href="#path-3"></use>
                    <g id="编组-36" filter="url(#filter-5)" mask="url(#mask-4)">
                        <g transform="translate(7.000000, 7.450000)">
                            <g id="形状结合" fill="none">
                                <use fill="url(#linearGradient-6)" fill-rule="evenodd" xlink:href="#path-7"></use>
                                <use fill="black" fill-opacity="1" filter="url(#filter-8)" xlink:href="#path-7"></use>
                            </g>
                            <rect id="矩形" fill="#FF93B3" fill-rule="evenodd" x="2.5888199" y="3.66919759" width="9.83871493" height="1.96901242" rx="0.984506208"></rect>
                            <rect id="矩形备份-26" fill="#FF93B3" fill-rule="evenodd" x="2.5888199" y="11.46179" width="7.85019574" height="1.96901242" rx="0.984506208"></rect>
                            <rect id="矩形备份-25" fill="#FF93B3" fill-rule="evenodd" x="2.5888199" y="7.56549379" width="5.81721355" height="1.96901242" rx="0.984506208"></rect>
                            <path d="M14.9981411,6.95715389 L16.5779309,6.95715389 C17.363318,6.95715389 18,7.59383591 18,8.37922299 L18,15.6779309 C18,16.463318 17.363318,17.1 16.5779309,17.1 L16.4202103,17.1 C15.6348232,17.1 14.9981411,16.463318 14.9981411,15.6779309 L14.9981411,6.95715389 L14.9981411,6.95715389 Z" id="矩形备份-24" fill="#FF6C98" fill-rule="evenodd"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>
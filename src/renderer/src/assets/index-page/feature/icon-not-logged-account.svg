<?xml version="1.0" encoding="UTF-8"?>
<svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>失效账号</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#5B65F4" stop-opacity="0.85079764" offset="0%"></stop>
            <stop stop-color="#5B65F4" stop-opacity="0.307992788" offset="100%"></stop>
        </linearGradient>
        <rect id="path-2" x="0" y="0" width="32" height="32" rx="8"></rect>
        <linearGradient x1="50%" y1="3.0993801%" x2="50%" y2="100%" id="linearGradient-4">
            <stop stop-color="#ECE5FF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <path d="M8.7929036,0 C6.7218236,0 5.0428836,1.67893 5.0428836,3.75 C5.0428836,5.82107 6.7218236,7.5 8.7929036,7.5 C10.8640036,7.5 12.5429036,5.82107 12.5429036,3.75 C12.5429036,1.67893 10.8640036,0 8.7929036,0 Z" id="path-5"></path>
        <filter x="-133.3%" y="-106.7%" width="366.7%" height="366.7%" filterUnits="objectBoundingBox" id="filter-6">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.356862745   0 0 0 0 0.396078431   0 0 0 0 0.956862745  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="3.0993801%" x2="50%" y2="100%" id="linearGradient-7">
            <stop stop-color="#ECE5FF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <path d="M10.2494036,9 C10.7831036,9 11.3077036,9.056 11.8163036,9.1634 C10.1247036,10.2244 9,12.1059 9,14.25 C9,16.509 10.2484036,18.4765 12.0930036,19.5 L4.2147836,19.5 C1.2139736,19.5 -0.823586395,16.4504 0.324943605,13.6781 C1.4981036,10.8463 4.2614836,9 7.3266436,9 Z M15.0000036,9.75 C17.4853036,9.75 19.5000036,11.7647 19.5000036,14.25 C19.5000036,16.7353 17.4853036,18.75 15.0000036,18.75 C12.5147036,18.75 10.5000036,16.7353 10.5000036,14.25 C10.5000036,11.7647 12.5147036,9.75 15.0000036,9.75 Z M15.0000036,15.75 C14.5858036,15.75 14.2500036,16.0858 14.2500036,16.5 C14.2500036,16.9142 14.5858036,17.25 15.0000036,17.25 C15.4142036,17.25 15.7500036,16.9142 15.7500036,16.5 C15.7500036,16.0858 15.4142036,15.75 15.0000036,15.75 Z M15.0000036,11.25 C14.5858036,11.25 14.2500036,11.5858 14.2500036,12 L14.2500036,14.25 C14.2500036,14.6642 14.5858036,15 15.0000036,15 C15.4142036,15 15.7500036,14.6642 15.7500036,14.25 L15.7500036,12 C15.7500036,11.5858 15.4142036,11.25 15.0000036,11.25 Z" id="path-8"></path>
        <filter x="-51.3%" y="-76.2%" width="202.6%" height="290.5%" filterUnits="objectBoundingBox" id="filter-9">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.356862745   0 0 0 0 0.396078431   0 0 0 0 0.956862745  0 0 0 0.50161167 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <filter x="-34.6%" y="-45.2%" width="169.2%" height="228.6%" filterUnits="objectBoundingBox" id="filter-10">
            <feOffset dx="0" dy="1.5" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
    </defs>
    <g id="新版首页1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="新版首页-普通用户" transform="translate(-240.000000, -243.000000)">
            <g id="编组-34" transform="translate(200.000000, 20.000000)">
                <g id="失效账号" transform="translate(40.000000, 223.000000)">
                    <mask id="mask-3" fill="white">
                        <use xlink:href="#path-2"></use>
                    </mask>
                    <use id="矩形" fill="url(#linearGradient-1)" xlink:href="#path-2"></use>
                    <g id="编组-23" mask="url(#mask-3)">
                        <g transform="translate(6.249998, 6.250000)">
                            <g id="路径">
                                <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
                                <use fill="url(#linearGradient-4)" fill-rule="evenodd" xlink:href="#path-5"></use>
                            </g>
                            <g id="形状结合">
                                <use fill="black" fill-opacity="1" filter="url(#filter-9)" xlink:href="#path-8"></use>
                                <use fill="url(#linearGradient-7)" fill-rule="evenodd" xlink:href="#path-8"></use>
                                <use fill="black" fill-opacity="1" filter="url(#filter-10)" xlink:href="#path-8"></use>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>
<?xml version="1.0" encoding="UTF-8"?>
<svg width="32px" height="33px" viewBox="0 0 32 33" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>公众号</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#A881FF" offset="0%"></stop>
            <stop stop-color="#DCCCFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="3.0993801%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#ECE5FF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <path d="M14.4273775,11.8532143 C14.4780233,11.8181739 14.5286691,11.7802194 14.5793149,11.7422594 C14.6805524,11.6663448 14.781844,11.5904303 14.8831357,11.5378724 C14.8831357,11.5378724 14.9084315,11.5378724 14.9084315,11.5115962 C15.0132981,11.4390237 15.1181648,11.37898 15.2313731,11.3141642 L15.2313731,11.3141588 C15.2821814,11.2850984 15.3346147,11.2550738 15.3894855,11.2225305 C15.4021064,11.2225305 15.4084439,11.2159601 15.4147814,11.2093951 C15.4211188,11.2028247 15.4274563,11.1962542 15.4400772,11.1962542 C15.7692479,11.0123043 16.0983645,10.8809123 16.452831,10.7495203 C16.4654519,10.7495203 16.4781269,10.7429499 16.4908019,10.7363849 C16.5034227,10.7298145 16.5160977,10.7232441 16.5287727,10.7232441 C16.706006,10.6706862 16.8831851,10.6181283 17.0351225,10.5655758 C17.0604184,10.5655758 17.0857684,10.5655758 17.0857684,10.5392942 C17.2376516,10.4867363 17.4148849,10.46046 17.5921182,10.4341838 C17.617414,10.4341838 17.642764,10.4341838 17.6680598,10.4079022 L18.1997055,10.329068 L18.3009972,10.329068 C18.4782304,10.3027918 18.6807595,10.3027918 18.8579928,10.3027918 C19.0605219,10.3027918 19.263051,10.3027918 19.4656343,10.329068 L19.5668718,10.329068 C19.6681634,10.3422089 19.7631176,10.3553443 19.8580175,10.3684851 C19.9529717,10.3816259 20.0479258,10.3947613 20.1491633,10.4079022 C20.1745133,10.4079022 20.2251591,10.4079022 20.2504549,10.4341838 C20.4276882,10.46046 20.6302173,10.5130179 20.8074506,10.5655758 C20.8201256,10.5655758 20.8327464,10.5721408 20.8454214,10.5787112 C20.8580964,10.5852816 20.8707172,10.5918521 20.8833922,10.5918521 C21.0859213,10.6444099 21.2631546,10.6969624 21.4403879,10.775802 C21.4066421,10.7144799 21.3784754,10.6619166 21.3550754,10.6181175 L21.3550213,10.6181012 C21.3081671,10.5305246 21.2800546,10.4779775 21.2631546,10.46046 C20.0985716,8.48957982 18.0984681,7.33333333 15.9211312,7.33333333 C14.6046108,7.33333333 12.6045289,7.80634405 11.110786,9.67210795 C10.1233983,10.9334702 9.76894803,12.4050586 9.99681088,13.8503925 C10.1487158,14.8489544 10.6803832,16.1891454 11.4652309,17.0300283 C11.7184112,14.9014961 12.8323863,13.061997 14.4273775,11.8532143 Z M23.4911373,13.9818007 C21.9214419,11.8795176 19.2124594,11.2751155 16.7819476,12.2211358 L17.0097725,12.2999699 C20.6302173,13.5876301 22.5796751,17.6607446 21.3644462,21.3922844 C21.0353297,22.3908464 20.5289257,23.2580542 19.8959884,23.967583 C20.6808631,23.7310373 21.4656837,23.4157333 22.1492668,22.8638836 C24.9088952,20.6827556 25.4658909,16.6359118 23.4911373,13.9818007 Z M14.30079,20.5250765 C14.8071399,20.6564848 15.3388397,20.7090264 15.8958354,20.7090264 C17.5921182,20.7090264 18.9086386,20.3148558 20.1745133,19.3425646 C20.1491633,19.8944143 19.99728,20.5250765 19.8453967,20.9192471 C18.6807595,24.0726663 15.2628981,25.4391281 11.6930883,24.2303454 C8.57901505,23.1791876 6.68019228,19.3951062 7.54099352,16.241687 C7.76885204,15.4008041 7.99671272,14.8226836 8.45242759,14.086884 C8.70560793,16.1103329 9.92085298,18.1074569 11.7184058,19.3162938 C11.8449932,19.4213771 11.9462632,19.5790561 11.9462632,19.7630061 C11.9462632,19.8418185 11.9462632,19.8944143 11.9209457,19.946956 C11.7943583,20.4725349 11.6171358,21.3134177 11.6171358,21.3660136 C11.6171358,21.4053927 11.6108037,21.4382718 11.6044771,21.4710968 C11.598145,21.5039218 11.5918183,21.536801 11.5918183,21.5762343 C11.5918183,21.7338592 11.7184058,21.8389966 11.8703107,21.8389966 C11.9209457,21.8389966 11.9715807,21.8127258 12.0222157,21.786455 L13.6932027,20.6564848 C13.8197902,20.5513473 13.9463776,20.4988057 14.0982609,20.4988057 C14.1742026,20.4988057 14.2501442,20.4988057 14.30079,20.5250765 Z" id="path-3"></path>
        <filter x="-57.7%" y="-46.2%" width="215.4%" height="215.4%" filterUnits="objectBoundingBox" id="filter-4">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.705882353   0 0 0 0 0.568627451   0 0 0 0 1  0 0 0 0.801027098 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <filter x="-38.9%" y="-27.4%" width="177.9%" height="177.9%" filterUnits="objectBoundingBox" id="filter-5">
            <feOffset dx="0" dy="1.5" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
    </defs>
    <g id="新版首页1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="新版首页-普通用户" transform="translate(-826.000000, -60.000000)">
            <g id="编组-34" transform="translate(200.000000, 20.000000)">
                <g id="公众号" transform="translate(626.000000, 40.000000)">
                    <rect id="矩形" fill="url(#linearGradient-1)" x="0" y="0" width="32" height="32" rx="8"></rect>
                    <g id="形状">
                        <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                        <use fill="url(#linearGradient-2)" fill-rule="evenodd" xlink:href="#path-3"></use>
                        <use fill="black" fill-opacity="1" filter="url(#filter-5)" xlink:href="#path-3"></use>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>
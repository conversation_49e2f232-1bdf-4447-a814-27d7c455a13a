<?xml version="1.0" encoding="UTF-8"?>
<svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>发图文</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#5398FF" offset="0%"></stop>
            <stop stop-color="#9DC5FF" offset="100%"></stop>
        </linearGradient>
        <rect id="path-2" x="0" y="0" width="32" height="32" rx="8"></rect>
        <filter x="-84.2%" y="-100.0%" width="268.4%" height="300.0%" filterUnits="objectBoundingBox" id="filter-4">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.341176471   0 0 0 0 0.603921569   0 0 0 0 1  0 0 0 0.7 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <linearGradient x1="74.3000651%" y1="43.9087079%" x2="37.4588%" y2="64.5228077%" id="linearGradient-5">
            <stop stop-color="#D8E6FC" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <path d="M18.3201466,16 L0.675055028,16 C0.118641588,16 -0.19349278,15.4112 0.132212648,14.9888 L4.95401009,9.80352 C5.21186022,9.53472 5.65970518,9.53472 5.93112637,9.79072 L8.14320907,12.16 L12.2959533,6.04032 C12.5809455,5.65632 13.1780721,5.66912 13.4359223,6.06592 L18.9172732,15.04 C19.1615523,15.4752 18.8358468,16 18.3201466,16 Z" id="path-6"></path>
        <filter x="-2.6%" y="-4.9%" width="105.3%" height="109.8%" filterUnits="objectBoundingBox" id="filter-7">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
    </defs>
    <g id="新版首页改" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="新版首页-普通用户" transform="translate(-435.000000, -60.000000)">
            <g id="发图文" transform="translate(435.000000, 60.000000)">
                <circle id="椭圆形备份-6" fill="url(#linearGradient-1)" cx="16" cy="16" r="16"></circle>
                <mask id="mask-3" fill="white">
                    <use xlink:href="#path-2"></use>
                </mask>
                <use id="矩形" fill="url(#linearGradient-1)" xlink:href="#path-2"></use>
                <g id="编组-18" filter="url(#filter-4)" mask="url(#mask-3)">
                    <g transform="translate(6.500000, 8.000000)" id="路径">
                        <path d="M6.10755014,0 C7.60036669,0 8.82176204,1.152 8.82176204,2.56 C8.82176204,3.968 7.60036669,5.12 6.10755014,5.12 C4.6147336,5.12 3.39333825,3.968 3.39333825,2.56 C3.39333825,1.152 4.6147336,0 6.10755014,0 Z" fill="#FFFFFF" fill-rule="evenodd"></path>
                        <g fill="none">
                            <use fill="url(#linearGradient-5)" fill-rule="evenodd" xlink:href="#path-6"></use>
                            <use fill="black" fill-opacity="1" filter="url(#filter-7)" xlink:href="#path-6"></use>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>
<?xml version="1.0" encoding="UTF-8"?>
<svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <g id="新增微信授权类型" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="选择授权帐号" transform="translate(-307.000000, -95.000000)">
            <g id="refresh" transform="translate(307.000000, 95.000000)">
                <rect id="矩形" x="0" y="0" width="20" height="20"></rect>
                <path d="M16.7490423,5.49975279 L16.7490423,8.49975279 C16.7490423,8.91396636 16.4132558,9.24975279 15.9990423,9.24975279 L12.9990423,9.24975279 C12.5848287,9.24975279 12.2490423,8.91396636 12.2490423,8.49975279 C12.2490423,8.08553923 12.5848287,7.74975279 12.9990423,7.74975279 L14.0677923,7.74975279 L12.8802923,6.66350279 C12.8721673,6.65600279 12.8640423,6.64850279 12.8565423,6.64037779 C11.3414683,5.12622925 9.00042844,4.81342697 7.14097011,5.87668126 C5.28151177,6.93993556 4.36372165,9.11616488 4.90021555,11.1898739 C5.43670946,13.263583 7.29474242,14.7216729 9.43654226,14.7497528 L9.49904226,14.7497528 C10.7114905,14.752675 11.8785786,14.2889729 12.7584173,13.4547528 C13.059585,13.1701536 13.534443,13.183585 13.8190423,13.4847528 C14.1036415,13.7859206 14.09021,14.2607786 13.7890423,14.5453778 C12.630848,15.6431367 11.0948102,16.2533895 9.49904226,16.249769 L9.41341726,16.249769 C6.59844519,16.210075 4.15743898,14.2932876 3.45130205,11.568033 C2.74516513,8.84277835 3.94820765,5.98178659 6.38971248,4.58011035 C8.8312173,3.17843412 11.9084981,3.58207771 13.9059173,5.56600279 L15.2490423,6.79475279 L15.2490423,5.49975279 C15.2490423,5.08553923 15.5848287,4.74975279 15.9990423,4.74975279 C16.4132558,4.74975279 16.7490423,5.08553923 16.7490423,5.49975279 Z" id="路径" fill="#969499" fill-rule="nonzero"></path>
            </g>
        </g>
    </g>
</svg>

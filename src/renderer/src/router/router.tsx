import type { RouteObject } from 'react-router-dom'
import { createBrowserRouter, Navigate } from 'react-router-dom'
import { Tryout } from '@renderer/pages/tryouts/Tryout'
import { Login } from '@renderer/pages/Login/Login'
import { RouteGuard } from '@renderer/router/RouteGuard'
import { MainProvider } from '@renderer/pages/MainProvider'
import { WechatAuth } from '@renderer/pages/wechatAuth'
import { IndexPage } from '@renderer/pages/IndexPage/IndexPage'
import { PublishRecords } from '@renderer/pages/Publish/PublishRecords'
import { Accounts } from '@renderer/pages/PlatformAuthorization/Accounts'
import { Overview } from '@renderer/pages/Overview'
import { TeamManager } from '@renderer/pages/TeamManager/TeamManager'
import { AssetLibrary } from '@renderer/components/AssetLibrary'

const routes: RouteObject[] = [
  {
    element: <RouteGuard />,
    children: [
      {
        path: '/login',
        element: <Login />,
      },
      {
        path: '/tryout',
        element: <Tryout />,
      },
      {
        element: <MainProvider />,
        path: '/',
        children: [
          {
            index: true, // 默认子路由，访问 /newMain 时显示
            element: <IndexPage />,
          },
          {
            path: 'publish',
            element: <PublishRecords />,
          },
          {
            path: 'accounts',
            element: <Accounts />,
          },
          {
            path: 'overview',
            element: <Overview />,
          },
          {
            path: 'team',
            element: <TeamManager />,
          },
          {
            path: 'asset-library',
            element: <AssetLibrary />,
          },
          {
            path: 'order',
            element: <Navigate to="/team?tab=orders" replace />,
          },
          {
            path: 'tasks',
            element: null,
          },
        ],
      },
      {
        path: '*',
        element: <Navigate to="/" replace />,
      },
    ],
  },
  {
    path: '/wechat-callback',
    element: <WechatAuth />,
  },
]

export const router: ReturnType<typeof createBrowserRouter> = createBrowserRouter(routes, {
  basename: import.meta.env.BASE_URL.replace(/\/$/, ''),
})

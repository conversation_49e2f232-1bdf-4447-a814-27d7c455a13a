import type { ReactElement } from 'react'
import { useEffect } from 'react'
import './app.less'
import { ThemeProvider } from '@renderer/shadcn-components/theme-provider'
import { useConfirm } from '@renderer/hooks/useConfirm'
import { RouterProvider } from 'react-router-dom'
import { router } from '@renderer/router/router'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { notifyService, useNotify } from '@renderer/hooks/use-notify'
import { useSystemHandler } from '@renderer/hooks/preload/use-system-handler'
import { AlertBaseWrapper } from './components/alertBase'
import { ApiErrorSilentContext } from '@renderer/context/api-error-silent-context'
import { eventBus } from '@renderer/infrastructure/event-bus/buses'
import { authenticationEvents } from '@renderer/infrastructure/event-bus/business-events'
import { useContextStore } from './store/contextStore'
import { systemChannel } from '@common/events/system-channel'
import { uiEvents } from '@common/events/ui-events'

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: false,
    },
  },
})

export default function App(): ReactElement {
  useSystemHandler()

  const { ConfirmDialog } = useConfirm()
  const { Notification, ModalNotification } = useNotify()
  const token = useContextStore((state) => state.token)
  // const setToken = useContextStore((state) => state.setToken)

  useEffect(() => {
    return eventBus.on(authenticationEvents.serverError, async (message: string) => {
      notifyService.error(message)
    })
  }, [])

  useEffect(() => {
    return eventBus.on(authenticationEvents.unauthorized, async () => {
      await router.navigate('/login')
    })
  }, [])

  useEffect(() => {
    return eventBus.on(authenticationEvents.businessError, async (message: string) => {
      notifyService.error(message)
    })
  }, [])

  useEffect(() => {
    window.api.send(systemChannel.authorizationTokenChanged, token)
  }, [token])

  useEffect(() => {
    window.api.invoke(uiEvents.removeVideoFile, '', Date.now())
  }, [])

  return (
    <ThemeProvider defaultTheme="light" storageKey="vite-ui-theme">
      <ApiErrorSilentContext.Provider value={false}>
        <QueryClientProvider client={queryClient}>
          <RouterProvider router={router} />
          <ConfirmDialog />
          <Notification />
          <ModalNotification />
          <AlertBaseWrapper />
        </QueryClientProvider>
      </ApiErrorSilentContext.Provider>
    </ThemeProvider>
  )
}

import { useState, useRef, useEffect, useCallback } from 'react'
import { useNotify } from '@renderer/hooks/use-notify'
import { useLoginService } from '@renderer/infrastructure/services'

// 阿里云验证码相关类型定义
interface CaptchaVerifyResult {
  captchaResult: boolean
  bizResult?: boolean
}

interface AliyunCaptchaInstance {
  reset(): void
  destroy(): void
}

interface AliyunCaptchaInitConfig {
  SceneId: string
  mode: 'popup' | 'embed'
  element: string
  button: string
  captchaVerifyCallback: (param: string) => Promise<CaptchaVerifyResult>
  onBizResultCallback: (result: boolean) => void
  getInstance: (instance: AliyunCaptchaInstance) => void
  slideStyle: {
    width: number
    height: number
  }
  language: 'cn' | 'tw' | 'en'
}

declare global {
  interface Window {
    initAliyunCaptcha?: (config: AliyunCaptchaInitConfig) => void
    AliyunCaptchaConfig?: {
      region: string
      prefix: string
    }
  }
}

interface UseAliyunCaptchaOptions {
  buttonId: string
  phone: string
  sence?:
    | 'auth'
    | 'resetPassword'
    | 'changePhone'
    | 'checkPhone'
    | 'bindPhone'
    | 'bindAccount'
    | 'wxBindAccount'
  onSuccess?: (result: string) => void
  onCountdownStart?: (seconds: number) => void
  enabled?: boolean // 是否启用验证码初始化
}

export function useAliyunCaptcha({
  buttonId,
  phone,
  sence = 'auth',
  onSuccess,
  onCountdownStart,
  enabled = true,
}: UseAliyunCaptchaOptions) {
  const [captchaInstance, setCaptchaInstance] = useState<AliyunCaptchaInstance | null>(null)
  const [isSending, setIsSending] = useState(false)
  const { notifyService } = useNotify()
  const loginService = useLoginService()

  // 使用 useRef 来避免依赖项问题
  const initRef = useRef(false)
  const phoneRef = useRef(phone)
  const senceRef = useRef(sence)
  const loginServiceRef = useRef(loginService)
  const notifyServiceRef = useRef(notifyService)
  const onSuccessRef = useRef(onSuccess)
  const onCountdownStartRef = useRef(onCountdownStart)

  // 更新 ref 值
  phoneRef.current = phone
  senceRef.current = sence
  loginServiceRef.current = loginService
  notifyServiceRef.current = notifyService
  onSuccessRef.current = onSuccess
  onCountdownStartRef.current = onCountdownStart

  // 初始化阿里云验证码
  useEffect(() => {
    // 如果未启用，跳过初始化
    if (!enabled) {
      return
    }

    // 避免重复初始化
    if (initRef.current) {
      return
    }

    // 阿里云验证码场景ID
    const sceneId = '5aun0gpl'

    if (!window.initAliyunCaptcha) {
      console.error('阿里云验证码JS未加载')
      return
    }

    // 检查按钮元素是否存在并初始化
    const initCaptcha = () => {
      const buttonElement = document.getElementById(buttonId)
      if (!buttonElement) {
        console.warn('按钮元素未找到，ID:', buttonId)
        return false
      }

      try {
        console.log('初始化阿里云验证码，按钮ID:', buttonId)
        initRef.current = true // 标记为已初始化

        if (window.initAliyunCaptcha) {
          window.initAliyunCaptcha({
            SceneId: sceneId,
            mode: 'popup',
            element: '#captcha-element',
            button: `#${buttonId}`,
            captchaVerifyCallback: async (
              captchaVerifyParam: string,
            ): Promise<CaptchaVerifyResult> => {
              console.log('验证码验证回调被调用，参数:', captchaVerifyParam)

              try {
                setIsSending(true)
                console.log('调用 loginService.sendVerifyCode，手机号:', phoneRef.current)
                const result = await loginServiceRef.current.sendVerifyCode(
                  phoneRef.current,
                  captchaVerifyParam,
                  senceRef.current,
                )
                console.log('验证码发送结果:', result)

                // 调用成功回调
                if (onSuccessRef.current && result) {
                  onSuccessRef.current(result)
                }

                // 调用倒计时回调
                if (onCountdownStartRef.current) {
                  onCountdownStartRef.current(60)
                }

                notifyServiceRef.current.success('验证码已发送')

                // 验证码验证成功，业务也成功
                return { captchaResult: true, bizResult: true }
              } catch (error) {
                console.error('发送验证码接口调用失败:', error)

                // 验证码验证成功，但业务失败（接口报错）
                return { captchaResult: false, bizResult: false }
              } finally {
                setIsSending(false)
              }
            },
            onBizResultCallback: (bizResult: boolean) => {
              console.log('业务结果回调，bizResult:', bizResult)
              if (!bizResult) {
                // 业务失败时不需要额外的错误提示，因为在 captchaVerifyCallback 中已经处理了
                console.log('业务处理失败，但验证码验证成功')
              }
            },
            getInstance: (instance: AliyunCaptchaInstance) => {
              console.log('获取到验证码实例:', instance)
              setCaptchaInstance(instance)
            },
            slideStyle: {
              width: 360,
              height: 40,
            },
            language: 'cn',
          })
        }
        return true
      } catch (error) {
        console.error('验证码初始化异常:', error)
        notifyServiceRef.current.error('验证码初始化失败')
        initRef.current = false // 重置标记以便重试
        return false
      }
    }

    // 延迟初始化确保DOM已渲染，并添加重试机制
    const timer = setTimeout(() => {
      if (!initCaptcha()) {
        // 如果第一次失败，再尝试一次
        setTimeout(() => {
          if (!initCaptcha()) {
            console.error('验证码初始化失败，已重试')
          }
        }, 500)
      }
    }, 100)

    return () => {
      clearTimeout(timer)
    }
  }, [buttonId, enabled])

  // 重置初始化状态的方法
  const resetInitialization = useCallback(() => {
    initRef.current = false
    setCaptchaInstance(null)
  }, [])

  // 触发验证码的方法
  const triggerCaptcha = useCallback(() => {
    console.log('触发验证码，captchaInstance:', !!captchaInstance)

    if (!window.initAliyunCaptcha) {
      console.error('验证码服务未加载')
      notifyService.error('验证码服务未加载，请刷新页面重试')
      return false
    }

    if (!captchaInstance) {
      console.error('验证码实例未初始化')
      notifyService.error('验证码正在初始化，请稍后重试')
      return false
    }

    console.log('验证码实例存在，准备触发验证码弹窗')
    return true
  }, [captchaInstance, notifyService])

  return {
    isSending,
    isReady: !!captchaInstance,
    resetInitialization,
    triggerCaptcha,
  }
}

export type { CaptchaVerifyResult, AliyunCaptchaInstance, AliyunCaptchaInitConfig }

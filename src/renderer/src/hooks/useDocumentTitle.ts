import { useEffect } from 'react'
import { useFeatureManager } from '@renderer/infrastructure/services'

/**
 * 自定义Hook用于管理页面标题
 * 根据当前活跃的feature实例动态更新document.title
 */
export function useDocumentTitle() {
  const { activeInstance, fixedInstance } = useFeatureManager()

  useEffect(() => {
    // 获取当前活跃的实例
    const currentInstance = activeInstance || fixedInstance

    if (currentInstance) {
      // 如果实例有自定义名称，使用实例名称
      if (currentInstance.name) {
        document.title = `${currentInstance.name} ｜ 蚁小二`
      } else {
        // 否则使用feature的名称
        document.title = `${currentInstance.feature.name} ｜ 蚁小二`
      }
    } else {
      // 没有活跃实例时，显示默认标题
      document.title = '首页 ｜ 蚁小二'
    }
  }, [activeInstance, fixedInstance, activeInstance?.name, fixedInstance?.name])
}

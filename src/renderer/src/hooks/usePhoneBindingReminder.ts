import { useState, useEffect } from 'react'
import { useContextStore } from '@renderer/store/contextStore'

// 用于管理手机号码绑定提醒的状态
export function usePhoneBindingReminder() {
  const [showReminder, setShowReminder] = useState(false)
  const [hasChecked, setHasChecked] = useState(false)
  const userInfo = useContextStore((state) => state.userInfo)

  useEffect(() => {
    // 只在用户信息加载完成且还未检查过时进行检查
    if (userInfo && !hasChecked) {
      setHasChecked(true)

      // 检查用户是否没有绑定手机号码
      const hasPhone = userInfo.phone && userInfo.phone.trim() !== ''

      if (!hasPhone) {
        // 检查是否已经显示过提醒（基于用户ID，避免不同用户间的干扰）
        const reminderKey = `phone-binding-reminder-shown-${userInfo.id}`
        const hasShownReminder = localStorage.getItem(reminderKey)

        if (!hasShownReminder) {
          setShowReminder(true)
          // 标记已经显示过提醒
          localStorage.setItem(reminderKey, 'true')
        }
      }
    }
  }, [userInfo, hasChecked])

  const hideReminder = () => {
    setShowReminder(false)
  }

  return {
    showReminder,
    hideReminder,
  }
}

import { useEffect, useState } from 'react'
import { useContextStore } from '@renderer/store/contextStore'
import { useUserApi } from '@renderer/infrastructure/services/entity-service'

export function useUserInfoPreload() {
  const userApi = useUserApi()
  const { setUserInfo, userInfo } = useContextStore((state) => ({
    setUserInfo: state.setUserInfo,
    userInfo: state.userInfo,
  }))
  const [error, setError] = useState(false)

  useEffect(() => {
    async function init() {
      try {
        const userInfo = await userApi.getUserInfo()
        setUserInfo(userInfo)
      } catch {
        setUserInfo(null)
        setError(true)
      }
    }

    void init()

    return () => {
      setUserInfo(null)
    }
  }, [userApi, setUserInfo])

  useEffect(() => {
    console.debug('useUserInfoPreload - setUserInfo 更新', setUserInfo)
  }, [setUserInfo])

  useEffect(() => {
    console.debug('useUserInfoPreload - userApi 更新', userApi)
  }, [userApi])

  const ready = !!userInfo

  return { ready, error }
}

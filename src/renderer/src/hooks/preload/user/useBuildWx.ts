import { useEffect } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { useProfileService } from '@renderer/infrastructure/services'
import { useUserApi } from '@renderer/infrastructure/services/entity-service'
import { useContextStore } from '@renderer/store/contextStore'

export function useBuildWx() {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const userApi = useUserApi()
  const profileService = useProfileService()

  const { setUserInfo } = useContextStore((state) => ({
    setUserInfo: state.setUserInfo,
  }))

  const code = searchParams.get('code') || ''
  const state = searchParams.get('state') || ''

  useEffect(() => {
    if (!code || !state) return
    const buildWx = async () => {
      try {
        await profileService.WxOpenIdAction(code, state, 'bind')
        const userInfo = await userApi.getUserInfo()
        setUserInfo(userInfo)
      } catch (e) {
        /* empty */
      }
      // 清空URL中的code和state参数
      navigate('/', { replace: true })
    }
    void buildWx()
  }, [code, state, navigate])
}

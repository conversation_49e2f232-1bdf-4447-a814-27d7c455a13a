import { useEffect } from 'react'
import {
  SocketClient,
  socketLocalClient,
} from '@renderer/infrastructure/services/network-service/socket/socket-client'
import type { SocketMessage } from '@renderer/infrastructure/services/network-service/socket/socket-message'
import { eventBus } from '@renderer/infrastructure/event-bus/buses'
import {
  advertiseEvents,
  authorizeEvents,
  businessEvents,
  loginEvents,
  notificationEvents,
  pushEvents,
  spaceEvents,
  systemEvents,
  teamEvents,
  websocketEvents,
} from '@renderer/infrastructure/event-bus/business-events'
import { useToken } from '@renderer/hooks/preload/use-token'
import type { PushingTaskSetIdentifier } from '@renderer/infrastructure/model'
import { updateDialogManager } from '@renderer/pages/Update'
import { deviceId } from '@renderer/infrastructure/utils/version'

type AccountEventParam = {
  teamId: string
  platformAccountId: string
}

export type VipUpgradeEventParam = {
  isVip: boolean
  accountCountLimit: number
  memberCountLimit: number
}

export type AppTaskPushingEventParam = {
  taskSetId: PushingTaskSetIdentifier
  version: number
}

export type ScriptUpdatedEventParam = {
  spider: {
    version: string
    url: string
  }
  rpa: {
    version: string
    url: string
  }
}

export type DeviceLogDownloadEventParam = {
  id: string
  deviceId: string
}

export function useSocketPreload() {
  const { token } = useToken()

  useEffect(() => {
    const client = new SocketClient<SocketMessage>(token, (data) => {
      switch (data.event) {
        case 'notice_create':
          eventBus.emit(notificationEvents.newNotification)
          break
        case 'account_create': {
          const param = data.body as AccountEventParam
          eventBus.emit(authorizeEvents.accountAdded, param.platformAccountId)
          break
        }
        case 'account_delete': {
          const param = data.body as AccountEventParam
          eventBus.emit(authorizeEvents.accountDeleted, param.platformAccountId)
          break
        }
        case 'account_update': {
          const param = data.body as AccountEventParam
          eventBus.emit(websocketEvents.accountUpdated, param.platformAccountId)
          break
        }
        case 'team_exit': {
          eventBus.emit(teamEvents.beenRemoved)
          break
        }
        case 'team_role_change': {
          eventBus.emit(teamEvents.roleChanged)
          break
        }
        case 'team_joined': {
          eventBus.emit(teamEvents.teamJoined)
          break
        }
        case 'team_version_upgrade': {
          eventBus.emit(teamEvents.teamVersionUpgrade, data.body as VipUpgradeEventParam)
          break
        }
        case 'team_version_downgrade': {
          eventBus.emit(teamEvents.teamVersionDowngrade)
          break
        }
        case 'app_task_pushing': {
          const body = data.body as {
            taskIdentityId: string
            version: number
          }[]
          eventBus.emit(
            pushEvents.appTaskPushing,
            body.map(
              (x) =>
                ({
                  taskSetId: x.taskIdentityId as PushingTaskSetIdentifier,
                  version: x.version,
                }) satisfies AppTaskPushingEventParam,
            ),
          )
          break
        }
        case 'online_scripts_updated': {
          eventBus.emit(businessEvents.scriptUpdated, data.body as ScriptUpdatedEventParam)
          break
        }
        case 'account_space_dump_updated': {
          const body = data.body as { spaceId: string; accountId: string }
          eventBus.emit(spaceEvents.accountSpaceDumpUpdated, {
            spaceId: body.spaceId,
            accountId: body.accountId,
          })
          break
        }
        case 'web_space_dump_updated': {
          const body = data.body as { spaceId: string }
          eventBus.emit(spaceEvents.webSpaceDumpUpdated, body.spaceId)
          break
        }
        case 'banner_status_updated': {
          eventBus.emit(advertiseEvents.advertiseUpdated)
          break
        }
        case 'system_message_updated': {
          eventBus.emit(notificationEvents.systemNotifyUpdated)
          break
        }
        case 'desktop_apps_updated': {
          updateDialogManager.checkUpdate()
          break
        }
        case 'login_on_another_device': {
          eventBus.emit(loginEvents.loginOnAnotherDevice)
          break
        }
        case 'device_log_download': {
          const body = data.body as DeviceLogDownloadEventParam
          if (body.deviceId === deviceId) {
            eventBus.emit(systemEvents.deviceLogDownload, body.id)
          }
          break
        }
      }
    })
    client.connect()

    return () => {
      client.disconnect()
    }
  }, [token])
}

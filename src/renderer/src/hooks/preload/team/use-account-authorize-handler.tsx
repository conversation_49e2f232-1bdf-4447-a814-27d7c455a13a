import { useEffect } from 'react'
import { browserAuthorizeChannel } from '@common/events/browser-events'
import type { <PERSON><PERSON> } from 'electron'
import type { AuthorizingAccountInfoStructure } from '@common/model'
import { electronService, useAuthorizeService } from '@renderer/infrastructure/services'
import type { BrowserContextIdentifier } from '@common/structure/browser/contextIdentifier'
import type { AccountSession } from '@common/structure'
import { useNotify } from '@renderer/hooks/use-notify'
import { useConfirm } from '@renderer/hooks/useConfirm'
import { features } from '@renderer/infrastructure/model/features/features'
import { useFeatureManager } from '@renderer/infrastructure/services'
import { useBrowserService } from '@renderer/infrastructure/services/browser-service/browser'
import type { AccountInfoStructure } from '@common/model/account-info'
import { eventBus } from '@renderer/infrastructure/event-bus/buses'
import { authorizeEvents } from '@renderer/infrastructure/event-bus/business-events'

export function useAccountAuthorizeHandler(): void {
  const authorizeService = useAuthorizeService()
  const browserService = useBrowserService()
  const { notifyService, modelNotify } = useNotify()
  const { confirm } = useConfirm()

  const { openFeature } = useFeatureManager()

  useEffect(() => {
    return window.api.on(
      browserAuthorizeChannel.accountAuthorizing,
      (
        _event,
        contextIdentifier: BrowserContextIdentifier,
        account: AccountInfoStructure | null,
      ) => {
        const isNewAuthorizeTab = account === null
        if (isNewAuthorizeTab) {
          void electronService.focusWindow()
          notifyService.info('正在添加账号，请稍候...')
        }
      },
    )
  }, [notifyService])

  useEffect(() => {
    return eventBus.on(
      authorizeEvents.accountAuthorizeSuccessV2,
      async ({ accountId, accountInfo, cookies, localStorage }) => {
        const account = await authorizeService.addAccount(accountInfo, cookies, localStorage, '')
        notifyService.success(`${accountInfo.nickName}添加成功`)

        const isNewAuthorizeTab = accountId === null || accountId === undefined
        if (isNewAuthorizeTab) {
          openFeature(features.账号)
        } else if (account.accountId !== accountId) {
          const fromAccount = await authorizeService.getSpiderAccount(accountId)
          void authorizeService.resetSpaceContext(fromAccount.accountId)
          await modelNotify({
            title: '授权异常提醒',
            description: (
              <div>
                <div>预期登录账号：{account.nickName} </div>
                <div>实际登录账号：{fromAccount.nickName}</div>
              </div>
            ),
            OKText: '我知道了',
          })
        }
      },
    )
  }, [authorizeService, browserService, confirm, modelNotify, notifyService, openFeature])

  useEffect(() => {
    return window.api.on(
      browserAuthorizeChannel.accountAuthorizeError,
      (_event, contextIdentifier: BrowserContextIdentifier) => {
        void authorizeService.closeTabs(contextIdentifier)
        void electronService.focusWindow()
        notifyService.error('授权失败！请重试')
      },
    )
  }, [authorizeService, notifyService])

  useEffect(() => {
    return window.api.on(
      browserAuthorizeChannel.accountSessionFailed,
      async (
        _event,
        contextIdentifier: BrowserContextIdentifier,
        account: AccountInfoStructure | null,
        accountSession: AccountSession,
      ) => {
        // 没有fromAccountId表示是新的授权页面
        if (account !== null) {
          const updatedAccount = await authorizeService.sessionDetectFinished(
            account.accountId,
            '已失效',
            '浏览器检测到失效',
            accountSession,
            'fixedChecksum',
          )

          // 浏览器检测失效后，更新Context中的账号信息
          await browserService.updateAccountContext(contextIdentifier.contextId, updatedAccount)
        }
      },
    )
  }, [authorizeService, browserService])

  useEffect(() => {
    return window.api.on(
      browserAuthorizeChannel.accountIdentityVerified,
      async (
        _event,
        _contextIdentifier: BrowserContextIdentifier,
        contextAccount: AccountInfoStructure | null,
        identityVerified: boolean,
      ) => {
        // 没有fromAccountId表示是新的授权页面
        if (contextAccount !== null)
          await authorizeService.setIdentityVerified(contextAccount.accountId, identityVerified)
      },
    )
  })
}

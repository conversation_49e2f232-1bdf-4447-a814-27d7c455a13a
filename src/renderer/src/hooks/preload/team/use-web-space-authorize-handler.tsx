import { useEffect } from 'react'
import { useWebSpaceManageService } from '@renderer/infrastructure/services'

import { useNotify } from '@renderer/hooks/use-notify'
import { useConfirm } from '@renderer/hooks/useConfirm'
import { features, tabFeaturesMap } from '@renderer/infrastructure/model/features/features'

import { authorizeEvents, eventBus } from '@renderer/infrastructure/event-bus'
import { useNavigate } from 'react-router-dom'
import { useSpaceApi } from '@renderer/infrastructure/services/entity-service/cloud/space-api'
import { useQueryClient } from '@tanstack/react-query'

export function useWebSpaceAuthorizeHandler(): void {
  const spaceApi = useSpaceApi()
  const queryClient = useQueryClient()
  const webSpaceManageService = useWebSpaceManageService()
  const { notifyService, modelNotify } = useNotify()
  const { confirm } = useConfirm()
  const navigate = useNavigate()

  useEffect(() => {
    return eventBus.on(
      authorizeEvents.webSpaceAuthorizeSuccess,
      async ({ accountId, url, spaceName, color, unsaved, cookies, localStorage }) => {
        const accountSession = {
          cookies,
          localStorage,
        }

        if (unsaved) {
          await webSpaceManageService.createSpace(url, spaceName, color, accountSession)

          navigate(tabFeaturesMap[features.账号.name])

          notifyService.success(`${spaceName}添加成功`)
        } else {
          await webSpaceManageService.updateSpaceSession(accountId, accountSession)

          navigate(tabFeaturesMap[features.账号.name])

          notifyService.success(`${spaceName}更新成功`)
        }
      },
    )
  }, [webSpaceManageService, confirm, modelNotify, notifyService, navigate])

  useEffect(() => {
    return eventBus.on(authorizeEvents.cancelCollect, async ({ accountId, id }) => {
      spaceApi.cancelCollect(accountId, id)
    })
  }, [spaceApi])

  useEffect(() => {
    return eventBus.on(authorizeEvents.addCollect, async ({ accountId, name, url }) => {
      await spaceApi.saveFavorite(accountId, name, url)
      await queryClient.invalidateQueries({
        queryKey: ['cloudAccounts'],
      })
    })
  }, [queryClient, spaceApi])
}

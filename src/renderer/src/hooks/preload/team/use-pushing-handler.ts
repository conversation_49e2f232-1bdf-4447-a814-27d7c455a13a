import { useEffect } from 'react'
import { usePushingService } from '@renderer/infrastructure/services'
import { uiEvents } from '@common/events/ui-events'
import { publishChannels } from '@common/events/publish-chennels'
import type { PushingTaskSetIdentifier } from '@renderer/infrastructure/model'
import { useWechat3rdPartyService } from '@renderer/infrastructure/services/application-service/account/wechat-3rd-party-service'

export function usePushingHandler() {
  const { auditStateQuery } = usePushingService()

  const { unlockWechatAccount, keepTokenAlive } = useWechat3rdPartyService()

  useEffect(() => {
    return window.api.on(
      uiEvents.stateQueryStart,
      async (_event: Electron.IpcRendererEvent, taskSetId: PushingTaskSetIdentifier) => {
        console.debug('开始查询作品状态：', taskSetId)
        await auditStateQuery(taskSetId)
      },
    )
  }, [auditStateQuery])

  useEffect(() => {
    return window.api.on(publishChannels.wechatKeepAlive, async (_event, accountId, token) => {
      void keepTokenAlive(accountId, token)
    })
  })

  useEffect(() => {
    return window.api.on(publishChannels.wechatRelease, async (_event, accountId, token) => {
      void unlockWechatAccount(accountId, token)
    })
  })
}

import { EAssetLibraryFileType } from '@renderer/components/AssetLibrary/AssetLibraryMenuTypes'
import { useSystem } from '@renderer/pages/context'
import { useCallback } from 'react'
import { electronService } from '@renderer/infrastructure/services'
import { VideoFileInfo, type IAssetLibraryItem } from '@renderer/infrastructure/model'
import { useLocalFileService } from '@renderer/infrastructure/services/application-service/infrastructure-service/LocalFileService'

export function useAssetLibrary() {
  const { onSetDialog } = useSystem()
  const localFileService = useLocalFileService()

  const onOpenAssetLibrary = useCallback(
    (type: EAssetLibraryFileType, isMultiple = false) => {
      return new Promise<IAssetLibraryItem[] | null>((resolve) => {
        onSetDialog((dialogMap) => ({
          ...dialogMap.assetLibrary,
          elementProps: {
            isMultiple,
            type,
            onSelect: (value) => {
              resolve(value)
            },
            onClose: () => {
              resolve(null)
            },
          },
        }))
      })
    },
    [onSetDialog],
  )

  const onSelectNotDownloadAsset = useCallback(
    async ({ type, isMultiple }: { type: EAssetLibraryFileType; isMultiple?: boolean }) => {
      return new Promise<IAssetLibraryItem[] | null>((resolve) => {
        onSetDialog((dialogMap) => ({
          ...dialogMap.assetLibrary,
          elementProps: {
            isMultiple: isMultiple ?? true,
            isNotDownload: true,
            type,
            onSelect: (value) => {
              resolve(value)
            },
            onClose: () => {
              resolve(null)
            },
          },
        }))
      })
    },
    [onSetDialog],
  )

  const selectSingleVideoAsset = useCallback(async () => {
    const result = await onOpenAssetLibrary(EAssetLibraryFileType.Video)
    if (result && result.length > 0) {
      return await localFileService.getVideoFileInfo(result[0].filePath)
    }
    return null
  }, [localFileService, onOpenAssetLibrary])

  const selectMultipleVideoAsset = useCallback(async () => {
    const result = await onOpenAssetLibrary(EAssetLibraryFileType.Video, true)

    return result?.map((item) =>
      VideoFileInfo.fromObject({
        filePath: item.filePath,
        fileSize: item.size.bytes,
        fileDuration: 0,
        fileFormat: item.format,
        videoWidth: item.width,
        videoHeight: item.height,
        videoEncoding: '',
        videoDepth: 0,
        hasAudioTrack: false,
        fileName: item.fileName,
      }),
    )
  }, [onOpenAssetLibrary])

  const selectSingleImageAsset = useCallback(async () => {
    const result = await onOpenAssetLibrary(EAssetLibraryFileType.Image)
    if (result && result.length > 0) {
      return await localFileService.getImageFileInfo(result[0].filePath)
    }
    return null
  }, [localFileService, onOpenAssetLibrary])

  const selectMultipleImageAsset = useCallback(async () => {
    const result = await onOpenAssetLibrary(EAssetLibraryFileType.Image, true)
    if (result && result.length > 0) {
      return Promise.all(result.map((item) => electronService.getImageFileInfo(item.filePath)))
    }
    return null
  }, [onOpenAssetLibrary])

  return {
    selectSingleVideoAsset,
    selectMultipleVideoAsset,
    selectSingleImageAsset,
    selectMultipleImageAsset,
    onSelectNotDownloadAsset,
  }
}

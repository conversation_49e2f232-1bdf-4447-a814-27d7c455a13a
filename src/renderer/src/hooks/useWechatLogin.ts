import { useAnonymousApiMutation } from './useApiQuery'

// 微信登录URL请求参数类型
export interface WechatLoginUrlRequest {
  redirectUri: string
}

// 微信登录认证请求参数类型
export interface WechatAuthRequest {
  code: string
  state: string
}

// 微信登录认证响应类型
export interface WechatAuthResponse {
  authorization: string
  isMobileVerified: boolean
}

export interface WechatBindPhoneRequest {
  phone?: string
  code?: string
  authorization: string
}

/**
 * 获取微信登录URL的hook
 */
export function useWechatLoginQuery() {
  return useAnonymousApiMutation<WechatLoginUrlRequest, string>((params) => ({
    url: '/wx/third/login-url',
    method: 'GET',
    params: {
      redirectUri: params.redirectUri,
    },
  }))
}

/**
 * 微信登录认证的hook
 */
export function useWechatAuth() {
  return useAnonymousApiMutation<WechatAuthRequest, WechatAuthResponse>((params) => ({
    url: '/users/auth/wx',
    method: 'POST',
    data: {
      code: params.code,
      state: params.state,
    },
  }))
}

/**
 * 微信登录绑定手机号
 */
export function useWechatBindPhone() {
  return useAnonymousApiMutation<WechatBindPhoneRequest, WechatAuthResponse>((params) => ({
    url: '/users/wx/bind',
    method: 'POST',
    data: {
      phone: params.phone,
      code: params.code,
      authorization: params.authorization,
    },
  }))
}

import { uiEvents } from '@common/events/ui-events'
import { isElectron } from '@common/isElectron'
import { ImageFileInfo, VideoFileInfo } from '@renderer/infrastructure/model'
import { mediaInfoFactory, type ReadChunkFunc } from 'mediainfo.js'

export async function getVideoInfo(file: File): Promise<VideoFileInfo> {
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      // 创建文件读取函数
      const getSize = () => file.size

      const readChunk = (chunkSize: number, offset = 0) => {
        return new Promise((resolve, reject) => {
          const slice = file.slice(offset, offset + chunkSize)
          const reader = new FileReader()

          reader.onload = () => {
            resolve(new Uint8Array(reader.result as ArrayBuffer))
          }
          reader.onerror = reject
          reader.readAsArrayBuffer(slice)
        })
      }

      const mediainfo = await mediaInfoFactory()

      const analyzeResult = await mediainfo.analyzeData(getSize, readChunk as ReadChunkFunc)

      const generalInfo = analyzeResult.media?.track.find((x) => x['@type'] === 'General')
      const videoInfo = analyzeResult.media?.track.find((x) => x['@type'] === 'Video')
      const audioInfo = analyzeResult.media?.track.find((x) => x['@type'] === 'Audio')

      if (
        !generalInfo?.Format ||
        !generalInfo?.Duration ||
        !generalInfo?.FileSize ||
        !videoInfo?.Width ||
        !videoInfo?.Height ||
        !videoInfo?.Format ||
        !videoInfo?.BitDepth
      ) {
        reject('文件信息缺失:' + JSON.stringify(analyzeResult.media?.track))
        return
      }

      // 获取文件名（去掉扩展名）
      const fileName = file.name.substring(0, file.name.lastIndexOf('.')) || file.name

      const result = {
        filePath: file, // 使用临时URL作为文件路径
        fileSize: parseInt(generalInfo.FileSize),
        fileFormat: generalInfo.Format,
        fileDuration: generalInfo.Duration,
        videoWidth: videoInfo.Width,
        videoHeight: videoInfo.Height,
        videoEncoding: videoInfo.Format,
        videoDepth: videoInfo.BitDepth,
        fileName: generalInfo.FileName ?? fileName,
        hasAudioTrack: !!audioInfo,
      }

      resolve(VideoFileInfo.fromObject(result))
    } catch (e) {
      console.error(e)
      reject(e)
    }
  })
}

// Base64 转 ArrayBuffer 的函数
export function base64ToArrayBuffer(base64: string): ArrayBuffer {
  const binaryString = atob(base64)
  const buffer = new ArrayBuffer(binaryString.length)
  const view = new Uint8Array(buffer)

  for (let i = 0; i < binaryString.length; i++) {
    view[i] = binaryString.charCodeAt(i)
  }

  return buffer
}

export function dataURLToArrayBuffer(dataURL: string): ArrayBuffer {
  const base64 = dataURL.split(',')[1]
  const arrayBuffer = base64ToArrayBuffer(base64)
  return arrayBuffer
}

const videoValidTypes = ['video/mp4', 'video/quicktime', 'video/x-msvideo', 'video/x-matroska']
const videoValidExtensions = ['mp4', 'mov', 'avi', 'mkv']
const imageValidExtensions = ['jpg', 'png', 'jpeg', 'webp']
const imageValidTypes = ['image/jpeg', 'image/png', 'image/webp']

// imageValidTypes转换为这种类型'image/png': ['.png'],'text/html': ['.html', '.htm'],
export const typeToExtensions = (types: string[]) => {
  const result: { [key: string]: string[] } = {}
  for (const type of types) {
    result[type] = []
  }
  return result
}
export const imageAccept = typeToExtensions(imageValidTypes)

export const videoAccept = typeToExtensions(videoValidTypes)

// 文件校验
export const validateFile = (file: File, type: 'image' | 'video') => {
  const fileExtension = file.name.split('.').pop()?.toLowerCase() || ''
  if (type === 'image') {
    return imageValidTypes.includes(file.type) || imageValidExtensions.includes(fileExtension)
  } else {
    return videoValidTypes.includes(file.type) || videoValidExtensions.includes(fileExtension)
  }
}

type FileType = 'image' | 'video'
type ValidateFilesOptions = {
  type: FileType | FileType[]
}

type ValidateFilesReturn<T extends ValidateFilesOptions> =
  T['type'] extends Array<FileType> ? { [K in FileType]?: File[] } : File[]
export const filterValidFiles = <T extends ValidateFilesOptions>(
  files: File[] | FileList,
  options?: T,
): ValidateFilesReturn<T> => {
  const types = options?.type ?? 'image'
  const typeArray = Array.isArray(types) ? types : [types]
  const result: { image?: File[]; video?: File[] } = {}
  for (const file of files) {
    typeArray.forEach((type) => {
      if (validateFile(file, type)) {
        if (!result[type]) result[type] = []
        result[type]!.push(file)
      }
    })
  }
  return (typeArray.length > 1 ? result : result[typeArray[0]] || []) as ValidateFilesReturn<T>
}
// file文件转换为videoFileInfo
export const fileToVideoFileInfo = async (file: File) => {
  try {
    if (isElectron()) {
      const video = VideoFileInfo.fromObject(
        await window.api.invoke(uiEvents.getVideoInfo, file.path),
      )
      video.fileName = file.name
      // 存储发布的视频文件
      const filePath = await window.api.invoke<string>(uiEvents.saveVideoFile, file.path)
      video.filePath = filePath

      return video
    }

    const video = await getVideoInfo(file)
    video.fileName = file.name
    return video
  } catch (e) {
    console.error(e)
  }
  return null
}

// file文件转换为imageFileInfo
export const fileToImageFileInfo = async (file: File) => {
  try {
    return ImageFileInfo.fromObject(await window.api.invoke(uiEvents.getImageInfo, file.path))
  } catch (e) {
    console.error(e)
  }
  return null
}

/**
 * 从文件路径(path)中获取文件扩展名
 * @param filePath
 * @returns
 */
export const getFileExtension = (filePath: string) => {
  const parts = filePath.split('.')
  return parts.length > 1 ? parts.pop()!.toLowerCase() : ''
}

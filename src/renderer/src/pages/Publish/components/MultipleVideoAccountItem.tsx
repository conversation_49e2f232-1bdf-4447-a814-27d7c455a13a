import type {
  MultipleVideoAccountViewModelItem,
  ImageFileInfo,
} from '@renderer/infrastructure/model'
import {
  type Account,
  type SpiderAccount,
  type VideoFileInfo,
} from '@renderer/infrastructure/model'
import type { DraftFunction } from 'use-immer'
import { useEffect, useMemo, useRef, useState } from 'react'

import { mediaService } from '@renderer/infrastructure/services'

import { useFormStateContext } from '@renderer/infrastructure/validation/use-form-state-context'
import {
  CommonSummaryContext,
  FormStateContext,
  PlatformSummaryContext,
} from '@renderer/pages/Publish/context/summary-context'
import { useSummaryContext } from '@renderer/infrastructure/validation/use-summary-context'
import { useEagerFormState, useValidation } from '@renderer/hooks/validation/validation'
import {
  useCoverValidator,
  useVideoValidator,
} from '@renderer/pages/Publish/validation/useVideoContentValidation'
import { VideoCard } from '@renderer/pages/Publish/video/beforeDialog/VideoCard'
import { localPath2Url } from '@common/protocol'
import noThumb from '@renderer/assets/publish/no-thumb.png'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@renderer/shadcn-components/dropdown-menu'
import { Button } from '@renderer/shadcn-components/ui/button'
import { MoreHorizontal, Plus, X } from 'lucide-react'
import CapturingIcon from '@renderer/assets/publish/menu/capturing.svg?react'
import ClippingIcon from '@renderer/assets/publish/menu/clipping.svg?react'

import { AccountSelector } from '@renderer/pages/Publish/components/AccountSelector'
import { getVideoSpecification } from '@renderer/pages/Publish/specification/content-type/video/video-specification'
import { videoPlatforms } from '@renderer/pages/Publish/specification/content-type/supports'
import UserBase from '@renderer/components/UserBase'
import { ValidationMessage } from '@renderer/components/ValidationMessage'
import CaptureVideo from '@renderer/pages/Publish/components/CaptureVideo'
import { validators } from './MultipleVideoAccount'
import { CoverCropperDialog } from '@renderer/pages/Publish/components/CoverCropperDialog'
import { useDrag, useDrop } from 'react-dnd'
import { cn } from '@renderer/lib/utils'
import { useScenarioContext } from '@renderer/context/scenario-context'
import { useWechat3rdPartyService } from '@renderer/infrastructure/services/application-service/account/wechat-3rd-party-service'
import { useUnmountGuard } from '@renderer/hooks/useUnmountGuard'

export function MultipleVideoAccountItem({
  item,
  onChange,
  onRemove,
  onDropAccount,
  index,
}: {
  item: MultipleVideoAccountViewModelItem
  onChange: (func: DraftFunction<MultipleVideoAccountViewModelItem>) => void
  onRemove: () => void
  onDropAccount: (fromIndex: number, toIndex: number) => void
  index: number
}) {
  const { setAccounts, accountsWithTokens } = useScenarioContext()
  const { updateAccountLocks } = useWechat3rdPartyService()

  const platform = useMemo(() => (item.account ? [item.account.platform] : []), [item.account])

  const [capturingCover, setCapturingCover] = useState(false)

  const [cropperDialogOpen, setCropperDialogOpen] = useState(false)
  const coverImagePath = useMemo(() => {
    if (item.cover) {
      return localPath2Url(item.cover!.path)
    }
    return null
  }, [item.cover])

  const isUnmounted = useUnmountGuard()

  const lastVideo = useRef<VideoFileInfo | null>(null)

  useEffect(() => {
    void (async () => {
      if (item.video && item.video !== lastVideo.current) {
        lastVideo.current = item.video
        console.debug('try to capture video', lastVideo.current)
        const cover = await tryToCaptureCover(lastVideo.current)
        if (!isUnmounted()) {
          onChange((draft) => {
            draft.cover = cover
          })
        }
      }
    })()
  }, [item.video, onChange, isUnmounted])

  const tryToCaptureCover = async (video: VideoFileInfo) => {
    try {
      const coverFileInfo = await mediaService.captureVideo(video)
      return coverFileInfo
    } catch (e) {
      console.error(e)
      return null
    }
  }

  function captured(image: ImageFileInfo) {
    if (!isUnmounted()) {
      onChange((draft) => {
        draft.cover = image
      })
    }
    setCapturingCover(false)
  }

  const formState = useFormStateContext(FormStateContext)
  const eagerFormState = useEagerFormState()

  const commonSummary = useSummaryContext(CommonSummaryContext)
  const platformSummary = useSummaryContext(PlatformSummaryContext)

  const { conclusion: coverConclusion } = useValidation(item.cover, validators.cover, commonSummary)

  const { conclusion: videoConclusion } = useValidation(item.video, validators.video, commonSummary)

  const { conclusion: accountConclusion } = useValidation(
    item.account,
    validators.account,
    commonSummary,
  )

  useValidation(item.video, useVideoValidator(platform), platformSummary)

  useValidation(item.cover, useCoverValidator(platform), platformSummary)

  const [{ isOver }, dropRef] = useDrop({
    accept: 'platform-account',
    drop: (item: { fromIndex: number }) => {
      onDropAccount(item.fromIndex, index)
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  })

  const [{ isDragging }, dragRef] = useDrag({
    type: 'platform-account',
    item: { fromIndex: index },
    canDrag: !!item.account,
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  })

  return (
    <div className={cn('relative flex flex-col gap-1')} ref={dropRef}>
      <VideoCard
        className="h-[180px] w-[135px]"
        item={item.video}
        onRemove={onRemove}
        cover={item.cover ? localPath2Url(item.cover.path) : noThumb}
        onChange={(item) => onChange((draft) => (draft.video = item))}
        menus={
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                className="absolute right-2 top-2 h-6 w-6 bg-black/50 text-background opacity-0 hover:bg-black/80 group-hover/card:opacity-100 data-[state=open]:opacity-100"
                aria-haspopup="true"
                size="icon"
                variant="secondary"
              >
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent side="right" align="start" className="w-36">
              <DropdownMenuGroup>
                <DropdownMenuLabel>封面</DropdownMenuLabel>
                <DropdownMenuItem
                  disabled={!item.cover}
                  onClick={() => {
                    setCropperDialogOpen(true)
                  }}
                >
                  <ClippingIcon /> 剪裁封面
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setCapturingCover(true)}>
                  <CapturingIcon />
                  视频截取
                </DropdownMenuItem>
                {/* <DropdownMenuItem onClick={selectImageFromAssetLibrary}>
                  <LibraryIcon />
                  素材库选择
                </DropdownMenuItem>
                <DropdownMenuItem onClick={selectImageFromLocalFile}>
                  <LocalIcon />
                  本地选择
                </DropdownMenuItem> */}
              </DropdownMenuGroup>
              {/* <DropdownMenuGroup>
                <DropdownMenuSeparator />
                <DropdownMenuLabel>视频</DropdownMenuLabel>
                <DropdownMenuItem onClick={selectVideoFromAssetLibrary}>
                  <LibraryIcon />
                  素材库
                </DropdownMenuItem>
                <DropdownMenuItem onClick={selectVideoFromLocalFile}>
                  <LocalIcon />
                  本地选择
                </DropdownMenuItem>
              </DropdownMenuGroup> */}
            </DropdownMenuContent>
          </DropdownMenu>
        }
        footerChildren={
          <AccountSelector
            className="mb-1 flex-grow"
            selectedAccount={item.account}
            onChange={async (account) => {
              if (account !== null) {
                const result = await updateAccountLocks(
                  [
                    ...accountsWithTokens.accounts.filter((x) => x.accountId !== account.accountId),
                    account,
                  ] as SpiderAccount[],
                  accountsWithTokens,
                )
                setAccounts(result)
              }

              onChange((draft) => {
                draft.account = account as SpiderAccount //这里目前选出来的必然是SpiderAccount
              })
            }}
            selectable={(account: Account) =>
              getVideoSpecification(account.platform).contentTypeSupport
            }
            platforms={videoPlatforms}
          >
            {(showSelector, removeAccount) => (
              <div
                className={cn('mt-1 flex flex-1 overflow-hidden', { 'opacity-50': isDragging })}
                ref={dragRef}
              >
                {item.account !== null ? (
                  <div
                    key={index}
                    className="group/account relative flex flex-1 flex-grow cursor-move items-center overflow-hidden rounded-md"
                  >
                    <UserBase
                      id={item.account.accountId}
                      size="sm"
                      name={item.account.displayName}
                      avatar={item.account.avatar}
                      platformComponent={
                        <img
                          alt=""
                          src={item.account.platform.icon}
                          className="absolute -bottom-0 -right-1 h-3 w-3 rounded-full"
                        />
                      }
                      title={
                        <div className="truncate text-sm text-background">
                          {item.account.displayName}
                        </div>
                      }
                    />
                    <Button
                      size="icon"
                      variant="link"
                      className="hidden h-4 w-4 items-center justify-center rounded-full text-background group-hover/account:flex"
                      onClick={async () => {
                        removeAccount()

                        const result = await updateAccountLocks(
                          accountsWithTokens.accounts.filter(
                            (x) => x.accountId !== item.account!.accountId,
                          ),
                          accountsWithTokens,
                        )

                        setAccounts(result)
                      }}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ) : (
                  <Button
                    variant="ghost"
                    onClick={showSelector}
                    className="h-7 gap-1 text-background hover:bg-transparent hover:text-background"
                  >
                    <Plus className="h-4 w-4" strokeWidth={2} />
                    选择账号
                  </Button>
                )}
              </div>
            )}
          </AccountSelector>
        }
      />
      <ValidationMessage
        className="p-0"
        subject={item.account}
        conclusion={accountConclusion}
        formState={eagerFormState}
      />
      {accountConclusion.valid && (
        <>
          <ValidationMessage
            className="p-0"
            subject={item.video}
            conclusion={videoConclusion}
            formState={formState}
          />
          <ValidationMessage
            className="p-0"
            subject={item.cover}
            conclusion={coverConclusion}
            formState={formState}
          />
        </>
      )}
      {coverImagePath && (
        <CoverCropperDialog
          path={coverImagePath}
          open={cropperDialogOpen}
          setOpen={setCropperDialogOpen}
          onChange={(value) => onChange((draft) => (draft.cover = value))}
        />
      )}

      {item.video && (
        <CaptureVideo
          visible={capturingCover}
          video={item.video}
          onCaptured={captured}
          onClose={() => setCapturingCover(false)}
        />
      )}

      <div
        className={cn(
          'absolute left-0 top-0 -z-20 h-[180px] w-full rounded-md bg-accent/0 duration-100',
          {
            'z-10 bg-accent/50': isOver,
          },
        )}
      ></div>
    </div>
  )
}

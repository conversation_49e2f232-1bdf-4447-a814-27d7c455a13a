import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>rigger,
} from '@renderer/shadcn-components/ui/sheet'
import { Button } from '@renderer/shadcn-components/ui/button'
import { useQuickFill } from '../hooks/use-quick-fill'
import { VideoCategory } from './VideoCategory'
import { DateTimePicker } from '@renderer/components/DateTimePicker'
import type { TimeStamp } from '@renderer/infrastructure/types/brand'
import { APlatformFormViewModel, BPlatformFormViewModel } from '@renderer/infrastructure/model'
import { VideoCategories, type Platform } from '@renderer/infrastructure/model'
import FastIcon from '@renderer/assets/publish/fast.svg?react'
import { AdvancedDescriptionForm } from '../video/advanced-description-form'
import { useImmer } from 'use-immer'
import type { VideoVisualHint } from '../visual-hint/video'
import { FormItem as PublishFormItem } from '@renderer/pages/Publish/components/FormItem'

interface LocalQuickFillData {
  aContentConfig: APlatformFormViewModel
  bContentConfig: BPlatformFormViewModel
  categories: VideoCategories
  scheduledTime: TimeStamp | null
}

interface QuickFillSheetProps {
  selectedPlatforms: Platform[]
  visualHint: VideoVisualHint
  firstFileName?: string | null
}

export function QuickFillSheet({
  selectedPlatforms,
  visualHint,
  firstFileName,
}: QuickFillSheetProps) {
  const [open, setOpen] = useState(false)
  const [quickFillData, setQuickFillData] = useImmer<LocalQuickFillData>({
    aContentConfig: new APlatformFormViewModel(),
    bContentConfig: new BPlatformFormViewModel(),
    categories: new VideoCategories(),
    scheduledTime: null,
  })

  // 当firstFileName变化时，更新ab类平台的标题默认值
  useEffect(() => {
    if (firstFileName) {
      setQuickFillData((draft) => {
        // 只在标题为空时设置默认值，避免覆盖用户已输入的内容
        if (!draft.aContentConfig.title) {
          draft.aContentConfig.title = firstFileName
        }
        if (!draft.bContentConfig.title) {
          draft.bContentConfig.title = firstFileName
        }
      })
    }
  }, [firstFileName, setQuickFillData])

  const { triggerFill } = useQuickFill()

  const handleApplyToAll = () => {
    // 使用 Context 触发一键填写，广播给所有平台
    triggerFill({
      aContentConfig: quickFillData.aContentConfig,
      bContentConfig: quickFillData.bContentConfig,
      categories: quickFillData.categories,
      scheduledTime: quickFillData.scheduledTime || undefined,
    })

    // 只关闭对话框，保持表单数据持久化
    setOpen(false)
  }

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="shrink-0 gap-1 rounded-full bg-[#EFEEFD] text-sm text-primary hover:bg-[#e3e1ff]"
          title="快速填写"
        >
          <FastIcon />
          快速填写
        </Button>
      </SheetTrigger>
      <SheetContent className="flex !w-[520px] !max-w-[520px] flex-col bg-white p-0">
        <SheetHeader className="shrink-0 border-b px-6 pb-4 pt-6">
          <SheetTitle>快速填写</SheetTitle>
        </SheetHeader>

        <div className="min-h-0 flex-1 overflow-y-auto">
          <div className="space-y-6">
            {/* 使用 AdvancedDescriptionForm 组件 */}
            <AdvancedDescriptionForm
              aContentConfig={quickFillData.aContentConfig}
              bContentConfig={quickFillData.bContentConfig}
              visualHint={visualHint}
              aSetContentConfig={(updater) => {
                setQuickFillData((draft) => {
                  updater(draft.aContentConfig)
                })
              }}
              bSetContentConfig={(updater) => {
                setQuickFillData((draft) => {
                  updater(draft.bContentConfig)
                })
              }}
            />

            <div className="space-y-6 px-6">
              <VideoCategory
                selectedPlatforms={selectedPlatforms}
                value={quickFillData.categories}
                onChange={(categories) => {
                  setQuickFillData((draft) => {
                    draft.categories = categories
                  })
                }}
              />

              <PublishFormItem label="定时发布" required={false}>
                <DateTimePicker
                  timestamp={quickFillData.scheduledTime || undefined}
                  onChange={(scheduledTime) => {
                    setQuickFillData((draft) => {
                      draft.scheduledTime = scheduledTime || null
                    })
                  }}
                />
              </PublishFormItem>
            </div>
          </div>
        </div>

        <div className="shrink-0 border-t px-6 py-2">
          <div className="flex h-[52px] items-center justify-between gap-2">
            <div className="flex h-6 items-center gap-1 rounded bg-[#F6F7F9] px-2 text-xs text-gray-500">
              <svg className="h-3 w-3" viewBox="0 0 16 16" fill="currentColor">
                <path
                  fillRule="evenodd"
                  d="M8 1.5a6.5 6.5 0 100 13 6.5 6.5 0 000-13zM0 8a8 8 0 1116 0A8 8 0 010 8zm6.5-.25A.75.75 0 017.25 7h1a.75.75 0 01.75.75v2.75h.25a.75.75 0 010 1.5h-2a.75.75 0 010-1.5H7.5V8.5h-.25a.75.75 0 01-.75-.75zM8 6a1 1 0 100-2 1 1 0 000 2z"
                  clipRule="evenodd"
                />
              </svg>
              <span>点击确定后，将会覆盖已填写信息</span>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  // 只关闭对话框，保持表单数据持久化
                  setOpen(false)
                }}
              >
                取 消
              </Button>
              <Button size="sm" onClick={handleApplyToAll}>
                确 定
              </Button>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  )
}

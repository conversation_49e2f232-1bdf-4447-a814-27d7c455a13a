import { useSummaryContext } from '@renderer/infrastructure/validation/use-summary-context'
import { Button } from '@renderer/shadcn-components/ui/button'
import {
  AccountSummaryContext,
  BalanceSummaryContext,
  PlatformSummaryContext,
} from '../context/summary-context'
import { usePublishValidationPassed } from '@renderer/pages/Publish/hooks/use-publish-validation-passed'
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from '@renderer/shadcn-components/ui/hover-card'
import { cn } from '@renderer/lib/utils'

import DropDownIcon from '@renderer/assets/common/dropdown.svg?react'
import { Fragment, useEffect, useMemo, useState } from 'react'
import type { Platform, SpiderAccount } from '@renderer/infrastructure/model'
import type { RuleResult } from '@renderer/infrastructure/validation/rule'
import type { PublishRuleResultExtra } from '@renderer/pages/Publish/validation/types/publish-rule-result-extra'
import { getVideoSpecification } from '@renderer/pages/Publish/specification/content-type/video/video-specification'
import ValidationErrorIcon from '@renderer/assets/publish/validation-error.svg?react'
import BalanceErrorIcon from '@renderer/assets/publish/balance-error.svg?react'
import LocalPublishIcon from '@renderer/assets/publish/localPublishIcon.svg?react'
import BrowserPublishIcon from '@renderer/assets/publish/browserPublishIcon.svg?react'
import CloudPublishIcon from '@renderer/assets/publish/cloudPublishIcon.svg?react'
import DisadvantagesIcon from '@renderer/assets/publish/disadvantages-icon.png'
import BenefitsIcon from '@renderer/assets/publish/benefits-icon.png'
import { ScrollArea } from '@renderer/shadcn-components/ui/scroll-area'
import { Loading } from '@renderer/components/LoadingContainer'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@renderer/components/Tabs'
import { SetAccountsProxyDialog } from '@renderer/pages/PlatformAuthorization/SetProxyDialog'
import ArrowIcon from '@renderer/assets/vip/arrow.svg?react'
import { useVipDetail } from '@renderer/hooks/preload/use-vip'
import { useVipDialog } from '@renderer/pages/Vip/vipDialogProvider'
import { isElectron } from '@common/isElectron'

export function PublishNowHoverCard({
  publishWithSpider,
  publishWithBrowser,
  publishCloud,
  publishWithBrowserSupported,
  publishCloudDeveloping = true,
  pending,
  showPlatformType = false,
}: {
  publishWithSpider: () => Promise<void>
  publishWithBrowser?: () => Promise<void>
  publishCloud?: () => Promise<void>
  publishCloudDeveloping?: boolean
  pending: boolean
  showPlatformType?: boolean
  publishWithBrowserSupported?: boolean
}) {
  const [openBrowserLoading, setOpenBrowserLoading] = useState(false)
  const {
    spiderValidationPassed,
    browserValidationPassed,
    cloudValidationPassed,
    platformValidationPassed,
    accountValidationPassed,
    balanceValidationPassed,
    platformValidationHasWarning,
  } = usePublishValidationPassed()

  const [hovering, setHovering] = useState(false)

  const anyActionAvailable =
    spiderValidationPassed || browserValidationPassed || cloudValidationPassed
  const open = hovering && anyActionAvailable

  return (
    <HoverCard
      openDelay={100}
      open={open}
      onOpenChange={(isOpen) => {
        setHovering(isOpen)
      }}
    >
      <HoverCardTrigger>
        <Button className="group" disabled={!anyActionAvailable || pending}>
          <span>一键发布</span>
          <DropDownIcon
            className={cn(
              'transform transition-transform duration-300 ease-in-out',
              open ? 'rotate-180' : '',
            )}
          ></DropDownIcon>
        </Button>
      </HoverCardTrigger>
      <HoverCardContent align="end" sideOffset={8} className="w-auto rounded-lg p-0">
        <div className="m-4 flex flex-col items-center space-y-3">
          {isElectron() && (
            <HoverCardItem
              icon={<LocalPublishIcon />}
              disabled={!spiderValidationPassed || pending}
              onClick={() => {
                void publishWithSpider()
              }}
              title={'本机发布'}
              description={'适合快速发布'}
              benefits={['省时间, 操作简单']}
              disadvantages={['支持表单项少', '无法针对单个账号单独设置', '需要等待本机发布完成']}
              errorMessage={
                !spiderValidationPassed && (
                  <div className="mr-4 flex shrink-0 items-center text-sm text-destructive">
                    <ValidationErrorIcon className="mx-1" />
                  </div>
                )
              }
              errorHoverCard={
                (!spiderValidationPassed || platformValidationHasWarning) && (
                  <PublishNowPlatformSummaryHoverCard showPlatformType={showPlatformType} />
                )
              }
            />
          )}
          {publishWithBrowser && isElectron() && (
            <HoverCardItem
              loading={openBrowserLoading}
              icon={<BrowserPublishIcon />}
              disabled={
                !browserValidationPassed ||
                pending ||
                openBrowserLoading ||
                !publishWithBrowserSupported
              }
              onClick={async () => {
                setOpenBrowserLoading(true)

                await publishWithBrowser()

                setOpenBrowserLoading(false)
              }}
              title={'浏览器发布'}
              description={'适合精细化发布'}
              benefits={['支持平台全部表单', '支持单个账号设置不同表单']}
              disadvantages={['操作繁琐']}
              errorMessage={
                !publishWithBrowserSupported && (
                  <div className="mr-4 flex shrink-0 items-center text-sm text-destructive">
                    <ValidationErrorIcon className="mx-1" />
                  </div>
                )
              }
              errorHoverCard={
                !publishWithBrowserSupported && (
                  <div className="mr-4 flex shrink-0 items-center p-2 text-sm">
                    不支持同1个微信下发布多个视频号
                  </div>
                )
              }
            />
          )}
          <HoverCardItem
            icon={<CloudPublishIcon />}
            disabled={!publishCloud || !cloudValidationPassed || pending}
            onClick={async () => {
              await publishCloud?.()
            }}
            developing={publishCloudDeveloping}
            title={'云端发布'}
            description={'适合异地、户外场景发布'}
            benefits={[
              '快速发布无需等待本机发布完成',
              '支持 app 网页端发布',
              '支持城市设置',
              '无发布账号视频数量限制',
            ]}
            disadvantages={['需要消耗权益包中的流量']}
            errorMessage={
              // 优先流量错误，其次是表单错误，最后是代理错误
              !balanceValidationPassed ? (
                <div className="mr-4 flex shrink-0 items-center text-sm text-destructive">
                  <BalanceErrorIcon className="mx-1" />
                </div>
              ) : !platformValidationPassed ? (
                <div className="mr-4 flex shrink-0 items-center text-sm text-destructive">
                  <ValidationErrorIcon className="mx-1" />
                </div>
              ) : (
                !accountValidationPassed && (
                  <div className="mr-4 flex shrink-0 items-center text-sm text-destructive">
                    <ValidationErrorIcon className="mx-1" />
                  </div>
                )
              )
            }
            errorHoverCard={
              !balanceValidationPassed ? (
                <BalanceSummaryHoverCard />
              ) : (
                (!cloudValidationPassed || platformValidationHasWarning) && (
                  <PublishCloudPlatformSummaryHoverCard showPlatformType={showPlatformType} />
                )
              )
            }
            errorCardAlign={!balanceValidationPassed ? 'bottom' : 'top'}
          />
        </div>
      </HoverCardContent>
    </HoverCard>
  )
}

function HoverCardItem({
  loading,
  icon,
  onClick,
  disabled,
  title,
  description,
  benefits,
  disadvantages,
  developing = false,
  errorMessage,
  errorHoverCard,
  errorCardAlign = 'top',
}: {
  loading?: boolean
  icon: React.ReactNode
  disabled: boolean
  onClick: () => void
  title: string
  description: string
  benefits: string[]
  disadvantages: string[]
  developing?: boolean
  errorMessage?: React.ReactNode
  errorHoverCard?: React.ReactNode
  errorCardAlign?: 'top' | 'bottom'
}) {
  return (
    <div
      className={cn(
        'group relative flex h-[62px] w-[268px] items-center rounded-lg border border-transparent bg-secondary bg-cover bg-center',
        !disabled && !developing
          ? 'cursor-pointer hover:border-primary hover:bg-accent'
          : 'cursor-not-allowed bg-secondary/50',
      )}
      onClick={() => {
        if (!disabled) {
          onClick()
        }
      }}
    >
      {/* {disabled && (
        <div className="absolute inset-0 w-full h-full bg-white rounded-lg opacity-50 border-background" />
      )} */}
      {developing && (
        <div className="absolute -right-0 -top-0 flex items-center justify-center rounded-bl-lg rounded-tr-lg bg-[#C7D2FE] px-1.5 py-0.5 text-[10px] text-primary opacity-40">
          近期上线
        </div>
      )}
      <div className="ml-[18px] flex grow items-center">
        <div className={cn('flex grow items-center', { 'opacity-40': disabled || developing })}>
          <div className="flex h-8 w-8 items-center justify-center rounded-lg border bg-white">
            {icon}
          </div>
          <div className="ml-2.5 flex flex-grow flex-col justify-center gap-0.5">
            <span className="text-sm font-semibold">{title}</span>
            <span className="text-xs text-gray-500">{description}</span>
          </div>
        </div>
        {loading && (
          <div className="mr-4 flex shrink-0 items-center text-sm text-destructive">
            <Loading />
          </div>
        )}
        {!developing && errorMessage}
      </div>

      {!developing && errorHoverCard ? (
        <div
          className={cn(
            'absolute -left-[332px] hidden w-[340px] cursor-default group-hover:flex',
            errorCardAlign === 'top' ? '-top-[18px]' : '-bottom-[18px]',
          )}
        >
          <div className="w-[308px] rounded-lg border bg-white shadow-md">{errorHoverCard}</div>
        </div>
      ) : (
        <div className="absolute -left-[332px] -top-[18px] hidden w-[308px] flex-col items-center justify-center gap-4 rounded-lg border bg-white p-4 shadow-md group-hover:flex">
          <div className="flex w-full flex-col">
            <div className="flex flex-col gap-1.5">
              {benefits.map((benefit, index) => (
                <div key={index}>
                  <span className="inline-flex items-center gap-1 rounded-sm bg-[#D7EFDB]/40 px-2 py-1 text-xs">
                    <img src={BenefitsIcon} className="h-4 w-4" />
                    {benefit}
                  </span>
                </div>
              ))}
              {disadvantages.map((disadvantage, index) => (
                <div key={index}>
                  <span className="inline-flex items-center gap-1 rounded-sm bg-[#FFE5E6]/40 px-2 py-1 text-xs">
                    <img src={DisadvantagesIcon} className="h-4 w-4" />
                    {disadvantage}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

function ValidationMessageItems({
  results,
  showPlatformType,
}: {
  results: RuleResult<PublishRuleResultExtra>[]
  showPlatformType: boolean
}) {
  //根据 result.extra.platformName进行分组
  const groupedResults = useMemo(() => {
    const map = new Map<Platform, RuleResult<PublishRuleResultExtra>[]>()
    results.forEach((result) => {
      const platformName = result.extra.platform
      if (!map.has(platformName)) {
        map.set(platformName, [])
      }
      map.get(platformName)!.push(result)
    })
    return map
  }, [results])

  return (
    <>
      {[...groupedResults.entries()].map(([platform, results], index) => (
        <Fragment key={platform.name}>
          <div className="mx-4 my-3">
            <span className="mb-2 text-xs font-bold">
              {platform.name}
              {showPlatformType
                ? `-${getVideoSpecification(platform).isModern ? 'A' : 'B'}类平台`
                : ''}
              ({results.length})
            </span>
            {results.map((result, index) => (
              <div key={index} className="my-1 flex min-h-5 items-center text-xs">
                <div className="mr-2 h-1 w-1 rounded-full bg-destructive"></div>
                <div>
                  {result.extra.fieldName}-{result.message}
                </div>
              </div>
            ))}
          </div>
          {index < Object.entries(groupedResults).length - 1 && (
            <div className="mx-4 border-t border-[#E5E5E5]"></div>
          )}
        </Fragment>
      ))}
    </>
  )
}

export function PublishNowPlatformSummaryHoverCard({
  showPlatformType = false,
}: {
  showPlatformType?: boolean
}) {
  const [tab, setTab] = useState<string>('error')
  const platformSummary = useSummaryContext(PlatformSummaryContext)

  const invalidResults = platformSummary.invalidResults?.filter((result) => result)
  const warningResults = platformSummary.warningResults?.filter((result) => result)

  useEffect(() => {
    const allTabs: string[] = []
    if (invalidResults.length > 0) allTabs.push('error')
    if (warningResults.length > 0) allTabs.push('warning')
    if (allTabs.length > 0 && !allTabs.includes(tab)) {
      setTab(allTabs[0])
    }
  }, [invalidResults.length, warningResults.length, tab])

  return (
    <div className="h-full max-h-[330px] w-full overflow-hidden p-0">
      <Tabs value={tab} onValueChange={setTab} className="flex h-full flex-col overflow-hidden">
        <TabsList className="shrink-0 px-5" onClick={(e) => e.stopPropagation()}>
          {invalidResults.length > 0 && <TabsTrigger value="error">错误</TabsTrigger>}
          {warningResults.length > 0 && <TabsTrigger value="warning">警告</TabsTrigger>}
        </TabsList>

        <TabsContent value="error" className="mt-0 grow overflow-hidden data-[state=active]:pt-0">
          <ScrollArea className="h-full">
            <div className="flex flex-col justify-between">
              <div className="flex items-center bg-[#FEF6F6] p-2.5 text-sm font-bold">
                <ValidationErrorIcon className="mx-1" />
                {invalidResults.length}项配置错误
              </div>

              <ValidationMessageItems
                results={invalidResults}
                showPlatformType={showPlatformType}
              />
            </div>
          </ScrollArea>
        </TabsContent>
        {WarningTabsContent(warningResults, showPlatformType)}
      </Tabs>
    </div>
  )
}

export function PublishCloudPlatformSummaryHoverCard({
  showPlatformType = false,
}: {
  showPlatformType?: boolean
}) {
  const accountSummary = useSummaryContext(AccountSummaryContext)

  const platformSummary = useSummaryContext(PlatformSummaryContext)

  const platformResults = platformSummary.invalidResults

  const platformWarningResults = platformSummary.warningResults?.filter((result) => result)

  const accountResults = accountSummary.invalidResults

  const [open, setOpen] = useState(false)

  const [tab, setTab] = useState<string>(platformResults.length === 0 ? 'proxy' : 'error')

  useEffect(() => {
    const allTabs: string[] = []
    if (platformResults.length > 0) allTabs.push('error')
    if (accountResults.length > 0) allTabs.push('proxy')
    if (platformWarningResults.length > 0) allTabs.push('warning')
    if (allTabs.length > 0 && !allTabs.includes(tab)) {
      setTab(allTabs[0])
    }
  }, [accountResults.length, platformResults.length, platformWarningResults.length, tab])

  return (
    <div className="flex h-full max-h-[330px] flex-col overflow-hidden p-0">
      <Tabs value={tab} onValueChange={setTab} className="flex h-full flex-col overflow-hidden">
        <TabsList className="shrink-0 px-5">
          {platformResults.length > 0 && (
            <TabsTrigger value="error">
              <span>错误</span>
            </TabsTrigger>
          )}
          {accountResults.length > 0 && (
            <TabsTrigger value="proxy">
              <span>代理</span>
            </TabsTrigger>
          )}
          {platformWarningResults.length > 0 && <TabsTrigger value="warning">警告</TabsTrigger>}
        </TabsList>
        <TabsContent value="error" className="mt-0 grow overflow-hidden data-[state=active]:pt-0">
          <ScrollArea className="h-full">
            <ValidationMessageItems results={platformResults} showPlatformType={showPlatformType} />
          </ScrollArea>
        </TabsContent>
        <TabsContent value="proxy" className="mt-0 grow overflow-hidden data-[state=active]:pt-0">
          <ScrollArea className="h-full px-5 py-4">
            <div className="mb-2 flex shrink-0 items-center text-xs text-gray-500">
              以下账号未配置代理
              <div
                onClick={() => {
                  setOpen(true)
                }}
                className="mx-2 cursor-pointer text-[#4F46E5]"
              >
                批量配置
              </div>
            </div>
            {accountResults.map((result) => (
              <div key={result.extra.account.accountId} className="my-2 flex items-center gap-2">
                <img alt="" src={result.extra.platform.icon} className="h-5 w-5 rounded-full"></img>
                <div className="text-xs">{result.extra.account.displayName}</div>
              </div>
            ))}
          </ScrollArea>
        </TabsContent>
        {WarningTabsContent(platformWarningResults, showPlatformType)}
      </Tabs>
      <SetAccountsProxyDialog
        accounts={accountResults.map((result) => result.extra.account) as SpiderAccount[]}
        open={open}
        openChange={setOpen}
      ></SetAccountsProxyDialog>
    </div>
  )
}

function WarningTabsContent(
  results: RuleResult<PublishRuleResultExtra>[],
  showPlatformType: boolean,
) {
  return (
    <TabsContent value="warning" className="mt-0 grow overflow-hidden data-[state=active]:pt-0">
      <ScrollArea className="h-full">
        <ValidationMessageItems results={results} showPlatformType={showPlatformType} />
      </ScrollArea>
    </TabsContent>
  )
}

export function BalanceSummaryHoverCard() {
  const { isVip } = useVipDetail()
  const { show, upgrade } = useVipDialog()

  const balanceSummary = useSummaryContext(BalanceSummaryContext)
  const balanceReuslts = balanceSummary.invalidResults

  return (
    <div className="flex items-center px-3">
      {balanceReuslts.map((x) => (
        <div key={x.extra.fieldName} className="my-2 flex items-center gap-2 text-sm">
          <div className="shrink-0">{x.message}</div>
          <div className="flex shrink-0">
            <div className="shrink-0 text-destructive">{x.extra.aboutToUse}</div>/
            <div className="shrink-0">{x.extra.balance}</div>
          </div>
          {isVip ? (
            <div onClick={upgrade} className="flex shrink-0 cursor-pointer text-[#4F46E5]">
              去升级 <ArrowIcon />
            </div>
          ) : (
            <div onClick={show} className="flex shrink-0 cursor-pointer text-[#4F46E5]">
              去开通 <ArrowIcon />
            </div>
          )}
        </div>
      ))}
    </div>
  )
}

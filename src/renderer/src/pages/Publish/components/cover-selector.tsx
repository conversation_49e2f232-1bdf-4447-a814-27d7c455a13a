import { useMemo, useState } from 'react'

import CaptureVideo from './CaptureVideo'
import AddIcon from '@renderer/assets/publish/add.svg?react'
import type { ImageFileInfo, VideoFileInfo } from '@renderer/infrastructure/model'
import { cn } from '@renderer/lib/utils'

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@renderer/shadcn-components/ui/dropdown-menu'
import { SelectCoverDialog } from './SelectCoverDialog'

import { localPath2Url } from '@common/protocol'
import { CoverCropperDialog } from './CoverCropperDialog'
import CapturingIcon from '@renderer/assets/publish/menu/capturing.svg?react'
import ClippingIcon from '@renderer/assets/publish/menu/clipping.svg?react'
// import LibraryIcon from '@renderer/assets/publish/menu/library.svg?react'
// import LocalIcon from '@renderer/assets/publish/menu/local.svg?react'

const CoverSelector = ({
  video,
  cover,
  onChange,
  boxClassName,
  imageList,
  imageListLabel = '选择封面',
}: {
  video: VideoFileInfo | null
  cover: ImageFileInfo | null
  onChange: (cover: ImageFileInfo | null) => void
  boxClassName?: string
  imageList?: string[]
  imageListLabel?: string
}) => {
  const [capturingCover, setCapturingCover] = useState(false)

  const [selectDialogOpen, setSelectDialogOpen] = useState(false)

  function captured(image: ImageFileInfo) {
    onChange(image)
    setCapturingCover(false)
  }

  const [cropperDialogOpen, setCropperDialogOpen] = useState(false)

  const hasVideo = !!video
  const hasCover = !!cover

  const coverImagePath = useMemo(() => {
    if (cover) {
      return localPath2Url(cover.path)
    }
    return null
  }, [cover])

  return (
    <div className="flex flex-col">
      <div className={cn('group relative h-[180px] w-[135px]', boxClassName)}>
        <DropdownMenu>
          <DropdownMenuTrigger className="flex h-full w-full cursor-pointer flex-col items-center justify-center rounded-md bg-secondary">
            {hasCover ? (
              <img
                crossOrigin="anonymous"
                className={cn(
                  'h-full w-full rounded bg-black',
                  cover?.height / cover?.width > 1.5 ? 'object-contain' : 'object-contain',
                )}
                alt=""
                src={localPath2Url(cover.path)}
              />
            ) : (
              <AddIcon />
            )}
          </DropdownMenuTrigger>
          <DropdownMenuContent side="right" align="start">
            <DropdownMenuItem disabled={!hasCover} onClick={() => setCropperDialogOpen(true)}>
              <ClippingIcon />
              剪裁封面
            </DropdownMenuItem>
            {hasVideo && (
              <DropdownMenuItem onClick={() => setCapturingCover(true)}>
                <CapturingIcon />
                视频截取
              </DropdownMenuItem>
            )}
            {imageList && imageList.length > 0 && (
              <DropdownMenuItem onClick={() => setSelectDialogOpen(true)}>
                {imageListLabel}
              </DropdownMenuItem>
            )}
            {/* <DropdownMenuItem onClick={selectFromAssetLibrary}>
              <LibraryIcon />
              素材库选择
            </DropdownMenuItem>
            <DropdownMenuItem onClick={selectFromLocalFile}>
              <LocalIcon />
              本地选择
            </DropdownMenuItem> */}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      {hasVideo && (
        <CaptureVideo
          visible={capturingCover}
          video={video}
          onCaptured={captured}
          onClose={() => setCapturingCover(false)}
        />
      )}
      {imageList && imageList.length > 0 && (
        <SelectCoverDialog
          open={selectDialogOpen}
          onOpenChange={setSelectDialogOpen}
          imageList={imageList || []}
          onSelect={(image) => {
            onChange(image)
          }}
        />
      )}
      {coverImagePath && (
        <CoverCropperDialog
          path={coverImagePath}
          open={cropperDialogOpen}
          setOpen={setCropperDialogOpen}
          onChange={(value) => onChange(value)}
        />
      )}
    </div>
  )
}

export default CoverSelector

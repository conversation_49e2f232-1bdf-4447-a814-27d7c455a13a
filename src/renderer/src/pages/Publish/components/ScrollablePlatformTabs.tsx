import { useRef, useEffect, useState } from 'react'
import { TabsList } from '@renderer/components/Tabs'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import type { Platform } from '@renderer/infrastructure/model'
import { PlatformTabTriggerWithErrorIndicator } from './PlatformTabTriggerWithErrorIndicator'

interface ScrollablePlatformTabsProps {
  platforms: Platform[]
  value?: string
  className?: string
}

export function ScrollablePlatformTabs({
  platforms = [],
  value,
  className,
}: ScrollablePlatformTabsProps) {
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const [showLeftArrow, setShowLeftArrow] = useState(false)
  const [showRightArrow, setShowRightArrow] = useState(false)

  // 检查是否需要显示滚动箭头
  const checkScrollArrows = () => {
    const container = scrollContainerRef.current
    if (!container) return

    const { scrollLeft, scrollWidth, clientWidth } = container
    setShowLeftArrow(scrollLeft > 0)
    setShowRightArrow(scrollLeft < scrollWidth - clientWidth)
  }

  // 监听滚动容器大小变化
  useEffect(() => {
    const container = scrollContainerRef.current
    if (!container) return

    const resizeObserver = new ResizeObserver(() => {
      checkScrollArrows()
    })

    resizeObserver.observe(container)
    checkScrollArrows()

    return () => {
      resizeObserver.disconnect()
    }
  }, [platforms])

  // 监听value变化，自动滚动到对应标签页
  useEffect(() => {
    if (value) {
      scrollToTab(value)
    }
  }, [value])

  // 处理滚动事件
  const handleScroll = () => {
    checkScrollArrows()
  }

  // 滚动到指定选项卡
  const scrollToTab = (platformName: string) => {
    const container = scrollContainerRef.current
    if (!container) return

    const tabElement = container.querySelector(`[data-value="${platformName}"]`) as HTMLElement
    if (!tabElement) return

    const containerWidth = container.clientWidth
    const tabWidth = tabElement.offsetWidth
    const tabLeft = tabElement.offsetLeft

    // 计算目标滚动位置，使选项卡尽量居中
    const targetScroll = tabLeft - (containerWidth - tabWidth) / 2

    container.scrollTo({
      left: targetScroll,
      behavior: 'smooth',
    })
  }

  // 箭头点击处理
  const handleArrowClick = (direction: 'left' | 'right') => {
    const container = scrollContainerRef.current
    if (!container) return

    const scrollAmount = container.clientWidth / 2
    const targetScroll =
      direction === 'left'
        ? container.scrollLeft - scrollAmount
        : container.scrollLeft + scrollAmount

    container.scrollTo({
      left: targetScroll,
      behavior: 'smooth',
    })
  }

  // 如果没有平台，不渲染组件
  if (platforms.length === 0) {
    return null
  }

  return (
    <div className={`relative w-full overflow-hidden ${className || ''}`}>
      {/* 左箭头 */}
      {showLeftArrow && (
        <button
          onClick={() => handleArrowClick('left')}
          className="absolute left-0 top-1/2 z-10 -translate-y-1/2 rounded-full bg-background p-1 shadow-md hover:bg-muted"
          aria-label="向左滚动"
        >
          <ChevronLeft className="h-5 w-5" />
        </button>
      )}

      {/* 右箭头 */}
      {showRightArrow && (
        <button
          onClick={() => handleArrowClick('right')}
          className="absolute right-0 top-1/2 z-10 -translate-y-1/2 rounded-full bg-background p-1 shadow-md hover:bg-muted"
          aria-label="向右滚动"
        >
          <ChevronRight className="h-5 w-5" />
        </button>
      )}

      {/* Tabs 容器 */}
      <div
        ref={scrollContainerRef}
        className="scrollbar-hide overflow-x-auto"
        style={{
          paddingLeft: showLeftArrow ? '40px' : '0',
          paddingRight: showRightArrow ? '40px' : '0',
        }}
        onScroll={handleScroll}
      >
        <TabsList className="electron-drag-region w-max justify-start border-none">
          {platforms.map((platform) => (
            <PlatformTabTriggerWithErrorIndicator
              key={platform.name}
              platform={platform}
              value={platform.name}
              data-value={platform.name}
            >
              <span className="text-base">{platform.name}</span>
            </PlatformTabTriggerWithErrorIndicator>
          ))}
        </TabsList>
      </div>
    </div>
  )
}

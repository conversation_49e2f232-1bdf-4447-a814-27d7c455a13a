import type { DraftFunction } from 'use-immer'
import { useFormStateContext } from '@renderer/infrastructure/validation/use-form-state-context'
import {
  FormStateContext,
  PlatformSummaryContext,
} from '@renderer/pages/Publish/context/summary-context'
import { useSummaryContext } from '@renderer/infrastructure/validation/use-summary-context'
import { useValidation } from '@renderer/hooks/validation/validation'
import { FormItem as PublishFormItem } from '@renderer/pages/Publish/components/FormItem'
import { PublishInput } from '@renderer/components/PublishInput'
import { platforms } from '@renderer/infrastructure/model'
import { platformNames } from '@common/model/platform-name'
import type { QiEHaoPlatformFormViewModel } from '@renderer/infrastructure/model/config/view-model/video'
import { TimeSpan } from '@renderer/infrastructure/model/utils/time-span'
import { PlatformValidatorFactory } from './validators/platform-validator-factory'
import { DescriptionEditor } from '@renderer/pages/Publish/components/DescriptionEditor'
import { SelectField } from '@renderer/pages/Publish/components/SelectField'
import { Option } from '@renderer/infrastructure/model/option'
import { TagsInput } from '@renderer/pages/Publish/components/tags-input'
import { VideoCategorySelector } from '@renderer/pages/Publish/components/VideoCategory'
import { qiEHaoPlatformService } from '@renderer/infrastructure/services'
import type { CascadingPlatformDataItem } from '@common/structure'
import { DateTimePicker } from '@renderer/components/DateTimePicker'
import { HelpTooltop } from '@renderer/components/helpTooltop'
import { useEffect } from 'react'
import { useEffectEvent } from 'use-effect-event'
import { useQuickFill } from '@renderer/pages/Publish/hooks/use-quick-fill'

// 企鹅号平台验证器 - 迁移原规范文件中的验证配置
const titleValidator = PlatformValidatorFactory.createTitleValidator(platforms.QiEHao, {
  required: true,
  minLength: 5,
  maxLength: 60,
})

const descriptionValidator = PlatformValidatorFactory.createDescriptionValidator(platforms.QiEHao, {
  maxLength: 200,
})

const tagsValidator = PlatformValidatorFactory.createTagsValidator(platforms.QiEHao, {
  required: true,
  maxCount: 9,
  minCount: 2,
  maxLength: 8,
})

// 创建分类验证器
const categoryValidator = PlatformValidatorFactory.createCategoryValidator(platforms.QiEHao, {
  required: true,
})

const scheduledTimeValidator = PlatformValidatorFactory.createScheduledTimeValidator(
  platforms.QiEHao,
  {
    minTimeSpan: TimeSpan.fromMinutes(0),
    maxTimeSpan: TimeSpan.fromDays(7),
  },
)

// 声明选项
const declarationOptions = [
  Option.of('无需声明', { value: 0 }),
  Option.of('该内容由AI生成', { value: 1 }),
  Option.of('剧情演绎,仅供娱乐', { value: 3 }),
  Option.of('取材网络,谨慎甄别', { value: 4 }),
  Option.of('个人观点,仅供参考', { value: 2 }),
  Option.of('旧闻', { value: 5 }),
]

export function QiEHaoPlatformForm({
  model,
  onChange,
}: {
  model: QiEHaoPlatformFormViewModel
  onChange: (updater: DraftFunction<QiEHaoPlatformFormViewModel>) => void
}) {
  const formState = useFormStateContext(FormStateContext)
  const platformSummary = useSummaryContext(PlatformSummaryContext)

  // 标题验证
  useValidation(model.title, titleValidator, platformSummary)

  // 描述验证
  useValidation(model.description, descriptionValidator, platformSummary)

  // 标签验证
  useValidation(model.tags, tagsValidator, platformSummary)

  // 分类验证
  useValidation(model.category, categoryValidator, platformSummary)

  // 定时发布时间验证
  useValidation(model.scheduledTime, scheduledTimeValidator, platformSummary)

  // 监听快速填写 Context - 企鹅号平台自定义响应逻辑
  const { data: quickFillData } = useQuickFill()

  const handleQuickFill = useEffectEvent(() => {
    if (quickFillData) {
      formState.setDirty(true)
      onChange((draft) => {
        // 企鹅号是B类平台，使用bContentConfig
        if (quickFillData.bContentConfig) {
          if (quickFillData.bContentConfig.title) {
            draft.title = quickFillData.bContentConfig.title
          }
          if (quickFillData.bContentConfig.description) {
            draft.description = quickFillData.bContentConfig.description
          }
          if (quickFillData.bContentConfig.tags && quickFillData.bContentConfig.tags.length > 0) {
            draft.tags = [...quickFillData.bContentConfig.tags]
          }
        }
        if (quickFillData.scheduledTime) {
          draft.scheduledTime = quickFillData.scheduledTime
        }
        // 处理分类字段
        if (quickFillData.categories && quickFillData.categories[platformNames.QiEHao]) {
          draft.category = quickFillData.categories[
            platformNames.QiEHao
          ] as CascadingPlatformDataItem[]
        }
        // 企鹅号不处理 type 字段
      })
    }
  })

  useEffect(() => {
    handleQuickFill()
  }, [handleQuickFill, quickFillData])

  return (
    <div className="space-y-6">
      <PublishFormItem label="标题" required>
        <PublishInput
          type="text"
          placeholder="请输入企鹅号标题"
          value={model.title}
          maxLength={50}
          onChange={(e) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.title = e.target.value
            })
          }}
        />
      </PublishFormItem>

      <PublishFormItem label="描述" required>
        <DescriptionEditor
          description={model.description}
          onChange={(description) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.description = description
            })
          }}
          isSupportTopic={true}
        />
      </PublishFormItem>

      <PublishFormItem label="标签" required>
        <TagsInput
          value={model.tags}
          onChange={(tags) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.tags = tags
            })
          }}
        />
      </PublishFormItem>

      <PublishFormItem label="分类" required>
        <div className="w-[266px]">
          <VideoCategorySelector
            value={model.category}
            onChange={(category) => {
              formState.setDirty(true)
              onChange((draft) => {
                draft.category = category
              })
            }}
            categoriesGetter={() => qiEHaoPlatformService.getCategories()}
            platform={platforms.QiEHao}
          />
        </div>
      </PublishFormItem>

      <PublishFormItem label="声明" required={false}>
        <SelectField
          value={model.declaration}
          onChange={(value) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.declaration = value
            })
          }}
          options={declarationOptions}
          placeholder="请选择声明"
        />
      </PublishFormItem>

      <PublishFormItem
        label={
          <div className="flex items-center gap-1">
            <span>定时发布</span>
            <HelpTooltop title="可设置2小时后至14天内的发布时间" />
          </div>
        }
        required={false}
      >
        <DateTimePicker
          timestamp={model.scheduledTime}
          onChange={(timing) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.scheduledTime = timing
            })
          }}
        />
      </PublishFormItem>
    </div>
  )
}

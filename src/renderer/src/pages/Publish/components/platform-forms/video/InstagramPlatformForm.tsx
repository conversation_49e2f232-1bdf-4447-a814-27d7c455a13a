import type { InstagramPlatformFormViewModel } from '@renderer/infrastructure/model/config/view-model/video/instagram'
import type { DraftFunction } from 'use-immer'
import { FormItem as PublishFormItem } from '@renderer/pages/Publish/components/FormItem'
import { useFormStateContext } from '@renderer/infrastructure/validation/use-form-state-context'
import {
  FormStateContext,
  PlatformSummaryContext,
} from '@renderer/pages/Publish/context/summary-context'
import { useValidation } from '@renderer/hooks/validation/validation'
import { useSummaryContext } from '@renderer/infrastructure/validation/use-summary-context'
import { useEffect } from 'react'
import { useEffectEvent } from 'use-effect-event'
import { useQuickFill } from '@renderer/pages/Publish/hooks/use-quick-fill'
import { type ImageFileInfo, platforms, type VideoFileInfo } from '@renderer/infrastructure/model'
import { DescriptionEditor } from '@renderer/pages/Publish/components/DescriptionEditor'
import { PlatformValidatorFactory } from './validators/platform-validator-factory'
import { HelpTooltop } from '@renderer/components/helpTooltop'
import { RadioGroupField } from '@renderer/pages/Publish/components/RadioGroupField'
import { Option } from '@renderer/infrastructure/model/option'
import { PlatformCoverSelector } from '@renderer/pages/Publish/components/PlatformCoverSelector'

const descriptionValidator = PlatformValidatorFactory.createDescriptionValidator(
  platforms.Instagram,
  {
    maxLength: 2200, // Instagram 描述长度限制
  },
)

// 分享到动态选项
const shareToStoryOptions = [
  Option.of('允许', { value: 'yes' }),
  Option.of('不允许', { value: 'no' }),
]

export function InstagramPlatformForm({
  model,
  onChange,
  videoInfo,
}: {
  model: InstagramPlatformFormViewModel
  onChange: (updater: DraftFunction<InstagramPlatformFormViewModel>) => void
  videoInfo?: {
    video: VideoFileInfo | null
    cover: ImageFileInfo | null
  }
}) {
  const formState = useFormStateContext(FormStateContext)
  const platformSummary = useSummaryContext(PlatformSummaryContext)

  // 描述验证
  useValidation(model.description, descriptionValidator, platformSummary)

  const { data: quickFillData } = useQuickFill()

  const handleQuickFill = useEffectEvent(() => {
    if (quickFillData) {
      formState.setDirty(true)
      onChange((draft) => {
        if (quickFillData?.aContentConfig?.description) {
          draft.description = quickFillData.aContentConfig.description
        }
      })
    }
  })

  useEffect(() => {
    handleQuickFill()
  }, [handleQuickFill, quickFillData])

  useEffect(() => {
    void (async () => {
      if (videoInfo?.cover || videoInfo?.video) {
        onChange((draft) => {
          draft.thumbnail = videoInfo?.cover as ImageFileInfo | null
        })
      }
    })()
  }, [onChange, videoInfo?.cover, videoInfo?.video])

  return (
    <div className="space-y-6">
      {/* 描述 */}
      <PublishFormItem label="描述" required>
        <DescriptionEditor
          description={model.description}
          onChange={(description) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.description = description
            })
          }}
          isSupportTopic={true}
        />
      </PublishFormItem>

      {videoInfo && (
        <PlatformCoverSelector
          label="封面"
          video={videoInfo.video}
          cover={model.thumbnail || videoInfo.cover}
          onChange={(cover) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.thumbnail = cover
            })
          }}
          required={false}
          cropOnly={false}
          boxClassName="w-[160px] h-[90px]"
        />
      )}

      {/* 是否允许分享到动态 */}
      <PublishFormItem
        label={
          <div className="flex items-center gap-1">
            <span>是否允许分享到动态</span>
            <HelpTooltop title="如果允许，表示 Reels 可以同时在动态和 Reels 选项卡中显示。如果不允许，则表示 Reels 只可在 Reels 选项卡中显示。" />
          </div>
        }
        required
      >
        <RadioGroupField
          value={model.share_to_feed ? 'yes' : 'no'}
          onChange={(value) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.share_to_feed = value === 'yes'
            })
          }}
          options={shareToStoryOptions}
        />
      </PublishFormItem>
    </div>
  )
}

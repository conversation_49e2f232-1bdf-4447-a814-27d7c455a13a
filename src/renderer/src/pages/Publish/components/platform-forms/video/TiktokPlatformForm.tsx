import type {
  TiktokPlatformFormViewModel,
  TikTokVisibleType,
} from '@renderer/infrastructure/model/config/view-model/video/tiktok'
import type { DraftFunction } from 'use-immer'
import type { ImageFileInfo, VideoFileInfo } from '@renderer/infrastructure/model'
import { FormItem as PublishFormItem } from '@renderer/pages/Publish/components/FormItem'
import { useFormStateContext } from '@renderer/infrastructure/validation/use-form-state-context'
import {
  FormStateContext,
  PlatformSummaryContext,
} from '@renderer/pages/Publish/context/summary-context'
import { useValidation } from '@renderer/hooks/validation/validation'
import { useSummaryContext } from '@renderer/infrastructure/validation/use-summary-context'
import { useEffect } from 'react'
import { useEffectEvent } from 'use-effect-event'
import { useQuickFill } from '@renderer/pages/Publish/hooks/use-quick-fill'
import { platforms } from '@renderer/infrastructure/model'
import { DescriptionEditor } from '@renderer/pages/Publish/components/DescriptionEditor'
import { PlatformValidatorFactory } from '@renderer/pages/Publish/components/platform-forms/video/validators/platform-validator-factory'
import { RadioGroup, RadioGroupItem } from '@renderer/shadcn-components/radio-group'
import { Label } from '@renderer/shadcn-components/ui/label'
import { Switch } from '@renderer/shadcn-components/ui/switch'
import { Checkbox } from '@renderer/shadcn-components/ui/checkbox'
import { HelpTooltop } from '@renderer/components/helpTooltop'
import { alertBaseManager } from '@renderer/components/alertBase'
import { PlatformCoverSelector } from '@renderer/pages/Publish/components/PlatformCoverSelector'


const descriptionValidator = PlatformValidatorFactory.createDescriptionValidator(platforms.Tiktok, {
  maxLength: 2200, // Tiktok 描述长度限制
  required: true,
})

export function TiktokPlatformForm({
  model,
  onChange,
  videoInfo,
}: {
  model: TiktokPlatformFormViewModel
  onChange: (updater: DraftFunction<TiktokPlatformFormViewModel>) => void
  videoInfo?: {
    video: VideoFileInfo | null
    cover: ImageFileInfo | null
  }
}) {
  const formState = useFormStateContext(FormStateContext)
  const platformSummary = useSummaryContext(PlatformSummaryContext)

  // 描述验证
  useValidation(model.description, descriptionValidator, platformSummary)

  // 监听快速填写 Context - Tiktok 平台自定义响应逻辑
  const { data: quickFillData } = useQuickFill()

  const handleQuickFill = useEffectEvent(() => {
    if (quickFillData) {
      formState.setDirty(true)
      onChange((draft) => {
        if (quickFillData.aContentConfig?.description) {
          draft.description = quickFillData.aContentConfig?.description
        }
      })
    }
  })

  useEffect(() => {
    handleQuickFill()
  }, [handleQuickFill, quickFillData])

  useEffect(() => {
    void (async () => {
      if (videoInfo?.cover || videoInfo?.video) {
        onChange((draft) => {
          draft.thumbnail = videoInfo?.cover as ImageFileInfo | null
        })
      }
    })()
  }, [onChange, videoInfo?.cover, videoInfo?.video])

  return (
    <div className="space-y-6">

      <PublishFormItem label="描述" required>
        <DescriptionEditor
          description={model.description}
          onChange={(description) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.description = description
            })
          }}
          isSupportTopic={true}
        />
      </PublishFormItem>

      {/* TikTok 封面设置 - 使用现有 CoverSelector，获取帧率信息 */}
      {videoInfo && (
        <PlatformCoverSelector
          label="封面"
          video={videoInfo.video}
          cover={model.thumbnail || videoInfo.cover}
          onChange={(cover) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.thumbnail = cover
            })
          }}
          onFrameTimeChange={(frameTime) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.fps = frameTime
            })
          }}
          required={false}
          cropOnly={true}
          boxClassName="w-[120px] h-[180px]"
          platformName={platforms.Tiktok.name}
        />
      )}

      <PublishFormItem label="谁可以看到">
        <div className="flex min-h-10 flex-col justify-center">
          <div className="flex items-center space-x-3">
            <RadioGroup
              defaultValue="public"
              value={model.visible.toString()}
              onValueChange={(value) => {
                formState.setDirty(true)
                onChange((x) => {
                  x.visible = value as TikTokVisibleType
                })
              }}
              className="grid-cols-3"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="public" id="r1" />
                <Label htmlFor="r1" className="cursor-pointer">
                  全部
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="friends" id="r2" />
                <Label htmlFor="r2" className="cursor-pointer">
                  好友
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="private" id="r3" />
                <Label htmlFor="r3" className="cursor-pointer">
                  自己
                </Label>
              </div>
            </RadioGroup>
          </div>
        </div>
      </PublishFormItem>
      <PublishFormItem label="高级设置">
        <div className="flex min-h-10 flex-col justify-center">
          <div className="flex items-center space-x-2">
            <div className="flex items-center gap-2">
              <Switch
                id="comment"
                checked={model.comment}
                onCheckedChange={(checked) => {
                  onChange((x) => {
                    x.comment = checked
                  })
                }}
              />
              <Label htmlFor="comment">允许评论</Label>
            </div>

            <div className="flex items-center gap-2">
              <Switch
                id="stitch"
                checked={model.stitch}
                onCheckedChange={(checked) => {
                  onChange((x) => {
                    x.stitch = checked
                  })
                }}
              />
              <Label htmlFor="stitch">允许合拍</Label>
            </div>

            <div className="flex items-center gap-2">
              <Switch
                id="duet"
                checked={model.duet}
                onCheckedChange={(checked) => {
                  onChange((x) => {
                    x.duet = checked
                  })
                }}
              />
              <Label htmlFor="duet">允许拼接</Label>
            </div>

            <div className="flex items-center gap-2">
              <Switch
                id="aigc"
                checked={model.aigc}
                onCheckedChange={(checked) => {
                  onChange((x) => {
                    x.aigc = checked
                  })
                }}
              />
              <Label htmlFor="aigc">AI生成</Label>
            </div>
          </div>
        </div>
      </PublishFormItem>

      <PublishFormItem label={'品牌与商业合作披露'}>
        <div className="flex min-h-10 flex-col justify-center">
          <div className="flex items-center space-x-2">
            <div className="flex items-center gap-2">
              <Switch
                checked={model.business}
                onCheckedChange={(checked) => {
                  onChange((x) => {
                    x.business = checked
                  })
                }}
              />
            </div>
          </div>
        </div>
      </PublishFormItem>

      {model.business && (
        <PublishFormItem label={<></>}>
          <div className="flex min-h-10 flex-col justify-center">
            <div className="flex items-start space-x-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="your"
                  checked={model.yourOwn}
                  onCheckedChange={(checked) => {
                    onChange((x) => {
                      typeof checked === 'boolean' && (x.collaborative = checked)
                    })
                  }}
                />
                <label
                  htmlFor="your"
                  className="flex cursor-pointer items-center whitespace-nowrap text-sm"
                >
                  你的品牌
                  <HelpTooltop title="你正在推广自己或自己的业务" />
                </label>
              </div>
              <div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="coad"
                    checked={model.collaborative}
                    onCheckedChange={(checked) => {
                      onChange((x) => {
                        typeof checked === 'boolean' && (x.collaborative = checked)
                      })
                    }}
                  />
                  <label
                    htmlFor="coad"
                    className="flex cursor-pointer items-center whitespace-nowrap text-sm"
                  >
                    合作品牌
                    <HelpTooltop title="你与某个品牌是合作伙伴赞助商关系。发布视频后，请打开TikTok移动应用并在视频的【广告设置】下链接相关活动。" />
                  </label>
                </div>
                {model.collaborative && (
                  <div className={'mt-1 text-sm text-gray-500'}>
                    发布作品即表示同意TikTok官方的
                    <span
                      className={'cursor-pointer text-blue-500 hover:underline'}
                      onClick={() =>
                        window.open('https://www.tiktok.com/legal/page/global/bc-policy/zh-Hant')
                      }
                    >
                      品牌内容政策
                    </span>
                    和
                    <span
                      className={'cursor-pointer text-blue-500 hover:underline'}
                      onClick={() =>
                        alertBaseManager.open({
                          title: '音乐使用权确认',
                          description:
                            '你确认 (a) 视频中不包含任何受版权保护的音乐，或 (b) 你 已取得所有必要授权（包括与音乐作品以及内含音乐作品的\n' +
                            '母带相关的授权）并已支付相关费用，可在 TikTok 平台上 将音乐用于该作品。如果存在无法确认上述 (a) 或 (b) 的情\n' +
                            '形，请勿接受此授权书。请注意，如果执意发布视频，则作品可能会被下架。',
                          okText: '确定',
                          buttons: [],
                        })
                      }
                    >
                      音乐使用权限确认
                    </span>
                    。
                  </div>
                )}
              </div>
            </div>
          </div>
        </PublishFormItem>
      )}
    </div>
  )
}

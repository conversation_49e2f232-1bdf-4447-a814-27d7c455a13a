import type { TengXunWeiShiPlatformFormViewModel } from '@renderer/infrastructure/model/config/view-model/video/tengxunweishi'
import type { DraftFunction } from 'use-immer'
import { FormItem as PublishFormItem } from '@renderer/pages/Publish/components/FormItem'
import { useFormStateContext } from '@renderer/infrastructure/validation/use-form-state-context'
import {
  FormStateContext,
  PlatformSummaryContext,
} from '@renderer/pages/Publish/context/summary-context'
import { PublishInput } from '@renderer/components/PublishInput'
import { useValidation } from '@renderer/hooks/validation/validation'
import { useSummaryContext } from '@renderer/infrastructure/validation/use-summary-context'
import { platforms } from '@renderer/infrastructure/model'
import { TimeSpan } from '@renderer/infrastructure/model/utils/time-span'
import { PlatformValidatorFactory } from './validators/platform-validator-factory'
import { DescriptionEditor } from '@renderer/pages/Publish/components/DescriptionEditor'
import { DateTimePicker } from '@renderer/components/DateTimePicker'
import { HelpTooltop } from '@renderer/components/helpTooltop'
import { useEffect } from 'react'
import { useEffectEvent } from 'use-effect-event'
import { useQuickFill } from '@renderer/pages/Publish/hooks/use-quick-fill'

// 腾讯微视平台验证器 - 使用工厂模式
const descriptionValidator = PlatformValidatorFactory.createDescriptionValidator(
  platforms.TengXunWeiShi,
  {
    maxLength: 200,
  },
)

const scheduledTimeValidator = PlatformValidatorFactory.createScheduledTimeValidator(
  platforms.TengXunWeiShi,
  {
    minTimeSpan: TimeSpan.fromHours(1),
    maxTimeSpan: TimeSpan.fromDays(14),
  },
)

export function TengXunWeiShiPlatformForm({
  model,
  onChange,
}: {
  model: TengXunWeiShiPlatformFormViewModel
  onChange: (updater: DraftFunction<TengXunWeiShiPlatformFormViewModel>) => void
}) {
  const formState = useFormStateContext(FormStateContext)
  const platformSummary = useSummaryContext(PlatformSummaryContext)

  // 描述验证
  useValidation(model.description, descriptionValidator, platformSummary)

  // 定时发布时间验证
  useValidation(model.scheduledTime, scheduledTimeValidator, platformSummary)

  // 监听快速填写 Context - 腾讯微视平台自定义响应逻辑
  const { data: quickFillData } = useQuickFill()

  const handleQuickFill = useEffectEvent(() => {
    if (quickFillData) {
      formState.setDirty(true)
      onChange((draft) => {
        // 腾讯微视是A类平台，使用aContentConfig
        if (quickFillData.aContentConfig) {
          if (quickFillData.aContentConfig.title) {
            draft.title = quickFillData.aContentConfig.title
          }
          if (quickFillData.aContentConfig.description) {
            draft.description = quickFillData.aContentConfig.description
          }
        }
        if (quickFillData.scheduledTime) {
          draft.scheduledTime = quickFillData.scheduledTime
        }
        // 腾讯微视不处理 type、tags 和 category 字段
      })
    }
  })

  useEffect(() => {
    handleQuickFill()
  }, [handleQuickFill, quickFillData])

  return (
    <div className="space-y-6">
      <PublishFormItem label="标题">
        <PublishInput
          type="text"
          placeholder="请输入腾讯微视标题"
          value={model.title}
          onChange={(e) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.title = e.target.value
            })
          }}
        />
      </PublishFormItem>

      <PublishFormItem label="描述">
        <DescriptionEditor
          description={model.description}
          onChange={(description) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.description = description
            })
          }}
          isSupportTopic={true}
        />
      </PublishFormItem>

      {/* 定时发布字段 */}
      <PublishFormItem
        label={
          <div className="flex items-center gap-1">
            <span>定时发布</span>
            <HelpTooltop title="设置后作品将推送至平台以设定的时间发布" />
          </div>
        }
        required={false}
      >
        <DateTimePicker
          timestamp={model.scheduledTime}
          onChange={(timing) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.scheduledTime = timing
            })
          }}
        />
      </PublishFormItem>
    </div>
  )
}

import type {
  TwitterPlatformFormViewModel,
  TwitterReplyPermissionType,
} from '@renderer/infrastructure/model/config/view-model/video/twitter'
import type { DraftFunction } from 'use-immer'
import type { ImageFileInfo, VideoFileInfo } from '@renderer/infrastructure/model'
import { FormItem as PublishFormItem } from '@renderer/pages/Publish/components/FormItem'
import { useFormStateContext } from '@renderer/infrastructure/validation/use-form-state-context'
import {
  FormStateContext,
  PlatformSummaryContext,
} from '@renderer/pages/Publish/context/summary-context'
import { useValidation } from '@renderer/hooks/validation/validation'
import { useSummaryContext } from '@renderer/infrastructure/validation/use-summary-context'
import { useEffect } from 'react'
import { useEffectEvent } from 'use-effect-event'
import { useQuickFill } from '@renderer/pages/Publish/hooks/use-quick-fill'
import { platforms } from '@renderer/infrastructure/model'
import { DescriptionEditor } from '@renderer/pages/Publish/components/DescriptionEditor'
import { PlatformValidatorFactory } from './validators/platform-validator-factory'
import { HelpTooltop } from '@renderer/components/helpTooltop'
import { RadioGroupField } from '@renderer/pages/Publish/components/RadioGroupField'
import { Option } from '@renderer/infrastructure/model/option'
import { PlatformCoverSelector } from '@renderer/pages/Publish/components/PlatformCoverSelector'

const descriptionValidator = PlatformValidatorFactory.createDescriptionValidator(
  platforms.Twitter,
  {
    maxLength: 2800, // Twitter 描述长度限制
    required: true,
  },
)

// 评论权限选项
const replyPermissionOptions = [
  Option.of('全部', { value: 'public' }),
  Option.of('我关注的', { value: 'iFollow' }),
]

export function TwitterPlatformForm({
  model,
  onChange,
  videoInfo,
}: {
  model: TwitterPlatformFormViewModel
  onChange: (updater: DraftFunction<TwitterPlatformFormViewModel>) => void
  videoInfo?: {
    video: VideoFileInfo | null
    cover: ImageFileInfo | null
  }
}) {
  const formState = useFormStateContext(FormStateContext)
  const platformSummary = useSummaryContext(PlatformSummaryContext)

  // 描述验证
  useValidation(model.description, descriptionValidator, platformSummary)

  const { data: quickFillData } = useQuickFill()

  const handleQuickFill = useEffectEvent(() => {
    if (quickFillData) {
      formState.setDirty(true)
      onChange((draft) => {
        if (quickFillData?.aContentConfig?.description) {
          draft.description = quickFillData.aContentConfig.description
        }
      })
    }
  })

  useEffect(() => {
    handleQuickFill()
  }, [handleQuickFill, quickFillData])

  useEffect(() => {
    void (async () => {
      if (videoInfo?.cover || videoInfo?.video) {
        onChange((draft) => {
          draft.thumbnail = videoInfo?.cover as ImageFileInfo | null
        })
      }
    })()
  }, [onChange, videoInfo?.cover, videoInfo?.video])

  return (
    <div className="space-y-6">
      {/* 描述 */}
      <PublishFormItem label="描述" required>
        <DescriptionEditor
          description={model.description}
          onChange={(description) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.description = description
            })
          }}
          isSupportTopic={true}
        />
      </PublishFormItem>

      {/* Twitter 独立封面设置 */}
      {videoInfo && (
        <PlatformCoverSelector
          label="封面"
          video={videoInfo.video}
          cover={model.thumbnail || videoInfo.cover}
          onChange={(cover) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.thumbnail = cover
            })
          }}
          onFrameTimeChange={(frameTime) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.fps = frameTime
            })
          }}
          required={false}
          cropOnly={true}
          platformName={platforms.Twitter.name}
          boxClassName="w-[120px] h-[180px]"
        />
      )}

      {/* 谁可以评论 */}
      <PublishFormItem
        label={
          <div className="inline-flex items-center gap-1">
            <span>谁可以评论</span>
            <HelpTooltop title="选择谁可以对您的推文进行评论。这个设置只影响新的评论，不会影响已有的评论。" />
          </div>
        }
        required
      >
        <RadioGroupField
          value={model.whoComment}
          onChange={(value) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.whoComment = value as TwitterReplyPermissionType
            })
          }}
          options={replyPermissionOptions}
        />
      </PublishFormItem>
    </div>
  )
}

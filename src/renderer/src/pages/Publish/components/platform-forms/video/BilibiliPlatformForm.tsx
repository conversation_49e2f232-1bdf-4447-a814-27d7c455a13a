import type { DraftFunction } from 'use-immer'
import { useFormStateContext } from '@renderer/infrastructure/validation/use-form-state-context'
import {
  FormStateContext,
  PlatformSummaryContext,
} from '@renderer/pages/Publish/context/summary-context'
import { useSummaryContext } from '@renderer/infrastructure/validation/use-summary-context'
import { useValidation } from '@renderer/hooks/validation/validation'
import { FormItem as PublishFormItem } from '@renderer/pages/Publish/components/FormItem'
import { PublishInput } from '@renderer/components/PublishInput'
import { platforms } from '@renderer/infrastructure/model'
import { platformNames } from '@common/model/platform-name'
import { DescriptionEditor } from '@renderer/pages/Publish/components/DescriptionEditor'
import { TimeSpan } from '@renderer/infrastructure/model/utils/time-span'
import { PlatformValidatorFactory } from './validators/platform-validator-factory'
import { SelectField } from '@renderer/pages/Publish/components/SelectField'
import { RadioGroupField } from '@renderer/pages/Publish/components/RadioGroupField'
import { Option } from '@renderer/infrastructure/model/option'
import { DateTimePicker } from '@renderer/components/DateTimePicker'
import { HelpTooltop } from '@renderer/components/helpTooltop'

import { VideoCategorySelector } from '@renderer/pages/Publish/components/VideoCategory'
import { bilibiliPlatformService } from '@renderer/infrastructure/services'
import type { CascadingPlatformDataItem } from '@common/structure'
import { TagsInput } from '@renderer/pages/Publish/components/tags-input'
import type { BilibiliPlatformFormViewModel } from '@renderer/infrastructure/model/config/view-model/video/bilibili'
import { useEffect } from 'react'
import { useEffectEvent } from 'use-effect-event'
import { useQuickFill } from '@renderer/pages/Publish/hooks/use-quick-fill'

// 哔哩哔哩平台验证器 - 使用工厂模式，迁移原规范文件中的验证配置
const titleValidator = PlatformValidatorFactory.createTitleValidator(platforms.Bilibili, {
  required: true,
  maxLength: 80,
})

const descriptionValidator = PlatformValidatorFactory.createDescriptionValidator(
  platforms.Bilibili,
  {
    maxLength: 2000,
  },
)

// 创建标签验证器（针对string[]类型）
const tagsValidator = PlatformValidatorFactory.createTagsValidator(platforms.Bilibili, {
  maxCount: 10,
  minCount: 1,
  maxLength: 20,
  required: true,
})

// 创建分类验证器
const categoryValidator = PlatformValidatorFactory.createCategoryValidator(platforms.Bilibili, {
  required: true,
})

const scheduledTimeValidator = PlatformValidatorFactory.createScheduledTimeValidator(
  platforms.Bilibili,
  {
    minTimeSpan: TimeSpan.fromHours(2),
    maxTimeSpan: TimeSpan.fromDays(15),
  },
)

// 声明选项
const declarationOptions = [
  Option.of('无需声明', { value: 0 }),
  Option.of('作者声明:该视频使用人工智能合成技术', { value: 1 }),
  Option.of('作者声明:视频内含有危险行为,请勿轻易模仿', { value: 2 }),
  Option.of('作者声明:该内容仅供娱乐,请勿过分解读', { value: 3 }),
  Option.of('作者声明:该内容可能引人不适,请谨慎选择观看', { value: 4 }),
  Option.of('作者声明:请理性适度消费', { value: 5 }),
  Option.of('作者声明:个人观点,仅供参考', { value: 6 }),
]

// 类型选项
const typeOptions = [Option.of('自制', { value: 1 }), Option.of('转载', { value: 2 })]

export function BilibiliPlatformForm({
  model,
  onChange,
}: {
  model: BilibiliPlatformFormViewModel
  onChange: (updater: DraftFunction<BilibiliPlatformFormViewModel>) => void
}) {
  const formState = useFormStateContext(FormStateContext)
  const platformSummary = useSummaryContext(PlatformSummaryContext)

  // 标题验证
  useValidation(model.title, titleValidator, platformSummary)

  // 描述验证
  useValidation(model.description, descriptionValidator, platformSummary)

  // 标签验证
  useValidation(model.tags, tagsValidator, platformSummary)

  // 分类验证
  useValidation(model.category, categoryValidator, platformSummary)

  // 定时发布时间验证
  useValidation(model.scheduledTime, scheduledTimeValidator, platformSummary)

  // 监听快速填写 Context - 哔哩哔哩平台自定义响应逻辑
  const { data: quickFillData } = useQuickFill()

  const handleQuickFill = useEffectEvent(() => {
    if (quickFillData) {
      formState.setDirty(true)
      onChange((draft) => {
        // 哔哩哔哩是B类平台，使用bContentConfig
        if (quickFillData.bContentConfig) {
          if (quickFillData.bContentConfig.title) {
            draft.title = quickFillData.bContentConfig.title
          }
          if (quickFillData.bContentConfig.description) {
            draft.description = quickFillData.bContentConfig.description
          }
          if (quickFillData.bContentConfig.tags && quickFillData.bContentConfig.tags.length > 0) {
            draft.tags = [...quickFillData.bContentConfig.tags]
          }
        }
        if (quickFillData.scheduledTime) {
          draft.scheduledTime = quickFillData.scheduledTime
        }
        // 处理分类字段
        if (quickFillData.categories && quickFillData.categories[platformNames.BiliBili]) {
          draft.category = quickFillData.categories[
            platformNames.BiliBili
          ] as CascadingPlatformDataItem[]
        }
      })
    }
  })

  useEffect(() => {
    handleQuickFill()
  }, [handleQuickFill, quickFillData])

  return (
    <div className="space-y-6">
      <PublishFormItem label="标题" required>
        <PublishInput
          type="text"
          placeholder="请输入哔哩哔哩视频标题"
          value={model.title}
          maxLength={80}
          onChange={(e) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.title = e.target.value
            })
          }}
        />
      </PublishFormItem>

      <PublishFormItem label="描述" required>
        <DescriptionEditor
          description={model.description}
          onChange={(description) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.description = description
            })
          }}
          isSupportTopic={false}
        />
      </PublishFormItem>

      <PublishFormItem label="标签" required>
        <TagsInput
          value={model.tags}
          onChange={(tags) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.tags = tags
            })
          }}
        />
      </PublishFormItem>

      <PublishFormItem label="分类" required>
        <div className="w-[266px]">
          <VideoCategorySelector
            value={model.category}
            onChange={(category) => {
              formState.setDirty(true)
              onChange((draft) => {
                draft.category = category
              })
            }}
            categoriesGetter={() => bilibiliPlatformService.getCategories()}
            platform={platforms.Bilibili}
          />
        </div>
      </PublishFormItem>

      <PublishFormItem label="声明" required={false}>
        <SelectField
          value={model.declaration}
          onChange={(value) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.declaration = value
            })
          }}
          options={declarationOptions}
          placeholder="请选择声明"
        />
      </PublishFormItem>

      <PublishFormItem label="类型" required={false}>
        <RadioGroupField
          value={model.type}
          onChange={(value) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.type = value as number
            })
          }}
          options={typeOptions}
        />
      </PublishFormItem>

      <PublishFormItem
        label={
          <div className="flex items-center gap-1">
            <span>定时发布</span>
            <HelpTooltop title="设置后作品将推送至平台以设定的时间发布" />
          </div>
        }
        required={false}
      >
        <DateTimePicker
          timestamp={model.scheduledTime}
          onChange={(timing) => {
            formState.setDirty(true)
            onChange((draft) => {
              draft.scheduledTime = timing
            })
          }}
        />
      </PublishFormItem>
    </div>
  )
}

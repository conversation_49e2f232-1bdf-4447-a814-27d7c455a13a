import UploadIcon from '@renderer/assets/publish/upload.svg?react'
import CloseIcon from '@renderer/assets/common/close.svg?react'
import PlayIcon from '@renderer/assets/common/play.svg?react'
import type { VideoFileInfo } from '@renderer/infrastructure/model'
import { electronService } from '@renderer/infrastructure/services'
import { cn } from '@renderer/lib/utils'
import { useAssetLibrary } from '@renderer/hooks/useAssetLibrary'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@renderer/shadcn-components/dropdown-menu'
import { localPath2Url } from '@common/protocol'
import { useMemo } from 'react'

export function VideoSelector({
  video,
  onChange,
  className,
}: {
  video: VideoFileInfo | null
  onChange: (videoInfo: VideoFileInfo | null) => void
  className?: string
}) {
  const { selectSingleVideoAsset } = useAssetLibrary()

  async function selectFromLocalFile() {
    const videoInfo = await electronService.openVideoFile()
    if (videoInfo === null) return
    onChange(videoInfo)
  }

  async function selectFromAssetLibrary() {
    const videoInfo = await selectSingleVideoAsset()
    if (videoInfo === null) return
    onChange(videoInfo)
  }

  const url = useMemo(() => {
    if (video) {
      return localPath2Url(video.filePath)
    }
    return ''
  }, [video])

  return (
    <div className={`flex flex-col ${className}`}>
      {video ? (
        <div className="group relative h-full w-full">
          <div
            className="absolute right-[-8px] top-[-8px] z-50 hidden h-5 w-5 cursor-pointer group-hover:block"
            onClick={() => {
              // 超级编导 console.log(video)
              onChange(null)
            }}
          >
            <CloseIcon className="h-full w-full" />
          </div>
          <div className="absolute inset-0 flex items-center justify-center">
            <PlayIcon className="h-6 w-6 text-white" />
          </div>
          <video
            crossOrigin="anonymous"
            src={url}
            className={cn(
              'h-full w-full rounded-md bg-black',
              video.videoHeight / video.videoWidth > 1.5 ? 'object-cover' : 'object-contain',
            )}
          ></video>
        </div>
      ) : (
        <DropdownMenu>
          <DropdownMenuTrigger
            className="flex h-full w-full cursor-pointer flex-col items-center justify-center rounded-md bg-secondary"
            onClick={(e) => {
              e.stopPropagation()
            }}
          >
            <div className="mb-2">
              <UploadIcon className={'h-4 w-4'} />
            </div>
            <div className="text-sm text-gray-800">上传</div>
          </DropdownMenuTrigger>
          <DropdownMenuContent side="right" align="start">
            <DropdownMenuItem onClick={selectFromAssetLibrary}>素材库</DropdownMenuItem>
            <DropdownMenuItem onClick={selectFromLocalFile}>本地选择</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </div>
  )
}

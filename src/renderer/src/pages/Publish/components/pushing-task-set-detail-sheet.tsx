import { PlatformResultStage, platforms } from '@renderer/infrastructure/model'
import {
  PlatformResultStageStatus,
  type PushingTaskSetViewModel,
} from '@renderer/infrastructure/model'
import NoDataImage from '@renderer/assets/common/nodata.png'
import type { ReactNode } from 'react'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from '@renderer/shadcn-components/ui/sheet'
import { VisuallyHidden } from '@radix-ui/react-visually-hidden'
import { Button } from '@renderer/shadcn-components/ui/button'
import { usePushingService } from '@renderer/infrastructure/services'
import PendingIcon from '@renderer/assets/publish/states/pending.svg?react'
import SuccessIcon from '@renderer/assets/publish/states/success.svg?react'
import FailedIcon from '@renderer/assets/publish/states/failed.svg?react'
import { useApiQuery } from '@renderer/hooks/useApiQuery'
import { LoadingContainer } from '@renderer/components/LoadingContainer'
import { DateUtils } from '@renderer/utils/date-utils'
import { useQuery } from '@tanstack/react-query'
import { ScrollArea } from '@renderer/shadcn-components/ui/scroll-area'
import { Separator } from '@renderer/shadcn-components/ui/separator'
import { Tabs, TabsList, TabsTrigger } from '@renderer/components/Tabs'
import { eventBus } from '@renderer/infrastructure/event-bus/buses'
import { pushEvents } from '@renderer/infrastructure/event-bus/business-events'
import { platformNames } from '@common/model/platform-name'
import DownIcon from '@renderer/assets/down.svg?react'
import type { PushingTaskSetResponse } from '@renderer/infrastructure/types'
import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@renderer/shadcn-components/dropdown-menu'
import { TaskActions } from './TaskActions'

const getManualStatusObj = (steap: PlatformResultStage, isTimed: number = 0) => {
  const obj: Record<string, string> = {
    upload: '上传',
    push: '推送',
    transcoding: '转码',
    review: '审核',
    success: '发布',
    scheduled: '定时发布',
  }
  if (!isTimed) {
    delete obj.scheduled
  }

  return obj[steap]
}

const manualStatusMap = {
  [PlatformResultStageStatus.DOING]: '中',
  [PlatformResultStageStatus.SUCCESS]: '成功',
  [PlatformResultStageStatus.FAIL]: '失败',
}

function formatNumber(num: number) {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

export function PushingTaskSetDetailSheet({
  taskSet,
  isOpen,
  onOpenChange,
}: {
  taskSet: PushingTaskSetViewModel
  isOpen: boolean
  onOpenChange: (value: boolean) => void
}) {
  const [tab, setTab] = useState('all')
  const queryFinished = useRef(false)
  const [showMore, setShowMore] = useState<Record<string, boolean | undefined>>({})
  const [unfinishedTaskIds, setUnfinishedTaskIds] = useState<string[]>([])

  const { rePushTaskSet, getCloudPushingTaskViewModel, auditStateQuery, getUnfinishedTaskIds } =
    usePushingService()

  const { isLoading, isSuccess, data } = useApiQuery<PushingTaskSetResponse>(
    {
      url: `/taskSets/${taskSet.taskSetId}`,
      method: 'get',
    },
    ['getTaskSetDetail', taskSet.taskSetId],
    {
      enabled: isOpen,
    },
  )

  const [rePushDialogOpen, setRePushDialogOpen] = useState(false)

  const { data: tasks, ...tasksQuery } = useQuery({
    queryKey: ['getCloudPushingTaskViewModel', taskSet.taskSetId],
    queryFn: () => getCloudPushingTaskViewModel(taskSet.taskSetId),
    enabled: isOpen,
  })

  const hasFailedTask = useMemo(() => {
    return !!tasks && tasks.some((x) => x.stageStatus === PlatformResultStageStatus.FAIL)
  }, [tasks])

  useEffect(() => {
    return eventBus.on(pushEvents.auditResultUpdated, async (taskId: string) => {
      if (isOpen && tasks?.findIndex((x) => x.taskId === taskId) !== -1) {
        void tasksQuery.refetch()
      }
    })
  }, [isOpen, tasks, tasksQuery])

  useEffect(() => {
    if (isOpen) {
      queryFinished.current = false
    }
  }, [isOpen])

  const refreshAuditState = useCallback(async () => {
    if (!queryFinished.current && tasks) {
      queryFinished.current = true
      await auditStateQuery(taskSet.taskSetId)
      void tasksQuery.refetch()
    }
  }, [auditStateQuery, taskSet.taskSetId, tasks, tasksQuery])

  useEffect(() => {
    if (!isOpen) return
    void refreshAuditState()
  }, [refreshAuditState, isOpen])

  // 获取未完成的任务列表
  useEffect(() => {
    const fetchUnfinishedTaskIds = async () => {
      if (!isOpen || taskSet.isCloudTaskSet) {
        setUnfinishedTaskIds([])
        return
      }

      try {
        const taskIds = await getUnfinishedTaskIds()
        setUnfinishedTaskIds(taskIds)
      } catch (error) {
        console.error('Failed to fetch unfinished task ids:', error)
        setUnfinishedTaskIds([])
      }
    }

    fetchUnfinishedTaskIds()
  }, [isOpen, taskSet.isCloudTaskSet, getUnfinishedTaskIds])

  const currentTasks = useMemo(() => {
    return (
      tasks?.filter((x) => {
        if (tab === 'all') return true
        return x.stageStatus === PlatformResultStageStatus.FAIL
      }) || []
    )
  }, [tab, tasks])

  const getTextColorClass = (status: PlatformResultStageStatus) => {
    switch (status) {
      case PlatformResultStageStatus.SUCCESS:
        return 'text-green-600 bg-green-100'
      case PlatformResultStageStatus.FAIL:
        return 'text-red-600 bg-red-100'
      case PlatformResultStageStatus.DOING:
        return 'text-blue-600 bg-blue-100'
      default:
        return ''
    }
  }

  const taskElements = useMemo(() => {
    return currentTasks?.map((x) => {
      const {
        message,
        stageStatus: rawStageStatus = PlatformResultStageStatus.DOING,
        stages: rawStages = PlatformResultStage.UPLOAD,
        platform,
      } = x

      const stageStatus = rawStageStatus || PlatformResultStageStatus.DOING
      const stages = rawStages || PlatformResultStage.UPLOAD

      const { isDraft, isTimed = 0 } = data ?? {}

      const beforeList = [
        ['upload', '上传状态:'],
        ['push', '推送状态:'],
        ...(!isDraft ? [['transcoding', '转码状态:']] : []),
        ...(isTimed && !isDraft
          ? [['scheduled', `定时发布 ${DateUtils.formatDate(isTimed)}:`]]
          : []),
        ...(!isDraft ? [['review', '审核状态:']] : []),
        ['success', '发布状态:'],
      ]
      const afterList = [
        ['upload', '上传状态:'],
        ['push', '推送状态:'],
        ...(!isDraft ? [['transcoding', '转码状态:']] : []),
        ...(!isDraft ? [['review', '审核状态:']] : []),
        ...(isTimed && !isDraft
          ? [['scheduled', `定时发布 ${DateUtils.formatDate(isTimed)}:`]]
          : []),
        ['success', '发布状态:'],
      ]
      const otherList = [
        ['upload', '上传状态:'],
        ['push', '推送状态:'],
        ...(!isDraft
          ? [
              ['transcoding', '转码状态:'],
              ['review', '审核状态:'],
            ]
          : []),
        ['success', '发布状态:'],
      ]
      const openList = [
        ['push', '推送状态:'],
        ...(!isDraft ? [['review', '审核状态:']] : []),
        ['success', '发布状态:'],
      ]

      let stageStatusText = '成功'
      let stageStatusType = PlatformResultStageStatus.SUCCESS

      switch (stageStatus) {
        case PlatformResultStageStatus.SUCCESS:
          stageStatusText = '成功'
          stageStatusType = PlatformResultStageStatus.SUCCESS
          break
        case PlatformResultStageStatus.FAIL:
          stageStatusText = '失败'
          stageStatusType = PlatformResultStageStatus.FAIL
          break
        case PlatformResultStageStatus.DOING:
          stageStatusText = '中'
          stageStatusType = PlatformResultStageStatus.DOING
          break
        default:
          break
      }

      let resultList: TaskState[] = []
      let targetList: string[][] = []

      switch (platform.name) {
        case platformNames.BaiJiaHao:
        case platformNames.XiaoHongShu:
        case platformNames.DouYin:
        case platformNames.YiDianHao:
        case platformNames.WangYiHao:
        case platformNames.BiliBili:
        case platformNames.KuaiShou:
        case platformNames.TouTiaoHao:
        case platformNames.WeiXinShiPinHao:
          targetList = afterList
          break
        case platformNames.ZhiHu:
        case platformNames.QiEHao:
        case platformNames.TengXunWeiShi:
        case platformNames.AiQiYi:
        case platformNames.XinLangWeiBo:
          targetList = beforeList
          break
        case platformNames.WeiXinGongZhongHao:
          targetList = openList
          break
        default:
          targetList = otherList
          break
      }

      const currentIndex = targetList.findIndex((item) => item[0] === stages)

      if (currentIndex !== -1) {
        resultList = targetList.slice(0, currentIndex + 1).map((item, i) => {
          if (i < currentIndex) {
            return {
              text: item[1] + ` ${getManualStatusObj(item[0] as PlatformResultStage, isTimed)}成功`,
              status: 'success',
              stage: item[0],
            }
          } else if (i === currentIndex) {
            return {
              text:
                (i === targetList.length - 1 ? '' : item[1]) +
                ` ${getManualStatusObj(item[0] as PlatformResultStage, isTimed)}${stageStatusText}`,
              status: stageStatusType,
              stage: item[0],
            }
          }
          return null as never
        }) as TaskState[]

        if (
          stageStatus === PlatformResultStageStatus.SUCCESS &&
          currentIndex < targetList.length - 1
        ) {
          if (isDraft && stages === PlatformResultStage.PUSH) {
            resultList.push({
              text: `${getManualStatusObj(PlatformResultStage.SUCCESS, isTimed)}成功`,
              status: PlatformResultStageStatus.SUCCESS,
              stage: PlatformResultStage.SUCCESS,
            })
          } else {
            const nextStage = targetList[currentIndex + 1]
            resultList.push({
              text:
                nextStage[1] +
                ` ${getManualStatusObj(nextStage[0] as PlatformResultStage, isTimed)}中`,
              status: PlatformResultStageStatus.DOING,
              stage: nextStage[0],
            })
          }
        }
      } else {
        resultList = targetList.map((item, index) => ({
          text:
            (index === targetList.length - 1 ? '' : item[1]) +
            ` ${getManualStatusObj(item[0] as PlatformResultStage, isTimed)}成功`,
          status: PlatformResultStageStatus.SUCCESS,
          stage: item[0],
        }))
      }

      const getStatusIcon = (status: PlatformResultStageStatus) => {
        switch (status) {
          case PlatformResultStageStatus.SUCCESS:
            return <SuccessIcon className="z-10 h-3.5 w-3.5 bg-white text-green-500" />
          case PlatformResultStageStatus.FAIL:
            return <FailedIcon className="z-10 h-3.5 w-3.5 bg-white text-red-500" />
          case PlatformResultStageStatus.DOING:
            return <PendingIcon className="z-10 h-3.5 w-3.5 text-[#909090]" />
          default:
            return <SuccessIcon className="z-10 h-3.5 w-3.5 bg-white text-green-500" />
        }
      }

      const lastRes = resultList[resultList.length - 1]

      return (
        <div
          key={x.taskId}
          className="flex cursor-pointer flex-col gap-3 rounded-lg bg-white py-4"
          onClick={() => {
            setShowMore((pre) => {
              pre[x.taskId] = !pre[x.taskId]
              return { ...pre }
            })
          }}
        >
          <div className="flex items-center px-3.5">
            <DownIcon
              className="mr-1"
              style={{
                transform: showMore[x.taskId] ? 'rotate(0deg)' : 'rotate(-90deg)',
                transition: 'transform 0.3s ease-in-out',
              }}
            />
            {x.accountAvatarUrl ? (
              <div className="relative mr-3 h-8 w-8 shrink-0 bg-white">
                <div className="h-full w-full overflow-hidden rounded-full">
                  <img src={x.accountAvatarUrl} alt="Platform" className="h-full w-full" />
                </div>
                <img
                  src={x.platform.icon}
                  alt="Platform"
                  className={'absolute -bottom-1 -right-1 h-4 w-4 rounded-full bg-white'}
                />
              </div>
            ) : (
              <div className="mr-3 h-8 w-8 shrink-0 rounded-full">
                <img
                  src={x.platform.icon}
                  alt="Platform"
                  className={'h-full w-full rounded-full bg-white'}
                />
              </div>
            )}
            <span className="text-md truncate">{x.accountName}</span>
            {lastRes.status && lastRes.stage && (
              <div
                className={`${getTextColorClass(lastRes.status)} ml-2 shrink-0 rounded-sm px-2 py-[0.5px] text-[12px]`}
              >
                {getManualStatusObj(lastRes.stage as PlatformResultStage, data?.isTimed)}
                {manualStatusMap[lastRes.status]}
              </div>
            )}
            <div className="ml-auto" onClick={(e) => e.stopPropagation()}>
              <TaskActions
                taskSet={taskSet}
                task={x}
                unfinishedTaskIds={unfinishedTaskIds}
                onUpdated={() => {
                  void tasksQuery.refetch()
                }}
              />
            </div>
          </div>
          {showMore[x.taskId] && (
            <>
              <Separator orientation="horizontal" className="h-0.5 bg-secondary" />
              <div className="flex shrink-0 flex-col px-3.5 pl-[25px]">
                <div className="relative flex flex-col gap-4">
                  {resultList.map((item, index) => {
                    const isLast = index === resultList.length - 1
                    return (
                      <div key={index}>
                        <div className="flex items-center gap-2">
                          <div className="relative">
                            {getStatusIcon(item.status)}
                            {!isLast && (
                              <div
                                className="absolute left-1/2 top-3.5 h-full -translate-x-1/2 border-l border-dashed border-gray-300"
                                style={{ height: 'calc(100% + 12px)' }}
                              ></div>
                            )}
                          </div>
                          <span className="text-[14px]">{item.text}</span>
                        </div>
                        {isLast && message && (
                          <div className="mt-2 rounded-lg bg-[#F8F8FA] p-2 text-[12px] text-[#2D2933]">
                            {message}
                          </div>
                        )}
                      </div>
                    )
                  })}
                </div>
              </div>
              {lastRes.stage === 'success' &&
                lastRes.status === PlatformResultStageStatus.SUCCESS && (
                  <>
                    <Separator orientation="horizontal" className="h-0.5 bg-secondary" />
                    <div className="flex items-center justify-between px-6">
                      <div className="flex flex-col">
                        <span className="text-[12px] text-[#757575]">播放</span>
                        <span className="font-bold text-[#222222]">
                          {formatNumber(x.statistic?.viewCount || 0)}
                        </span>
                      </div>
                      <div className="relative flex flex-col pl-4">
                        <div className="absolute bottom-2 left-0 top-2 w-[1px] bg-[#EBECF0]" />
                        <span className="text-[12px] text-[#757575]">评论</span>
                        <span className="font-bold text-[#222222]">
                          {formatNumber(x.statistic?.commentCount || 0)}
                        </span>
                      </div>
                      <div className="relative flex flex-col pl-4">
                        <div className="absolute bottom-2 left-0 top-2 w-[1px] bg-[#EBECF0]" />
                        <span className="text-[12px] text-[#757575]">点赞</span>
                        <span className="font-bold text-[#222222]">
                          {formatNumber(x.statistic?.greatCount || 0)}
                        </span>
                      </div>
                      <div className="relative flex flex-col pl-4">
                        <div className="absolute bottom-2 left-0 top-2 w-[1px] bg-[#EBECF0]" />
                        <span className="text-[12px] text-[#757575]">收藏</span>
                        <span className="font-bold text-[#222222]">
                          {formatNumber(x.statistic?.collectCount || 0)}
                        </span>
                      </div>
                      <div className="relative flex flex-col pl-4">
                        <div className="absolute bottom-2 left-0 top-2 w-[1px] bg-[#EBECF0]" />
                        <span className="text-[12px] text-[#757575]">分享</span>
                        <span className="font-bold text-[#222222]">
                          {formatNumber(x.statistic?.shareCount || 0)}
                        </span>
                      </div>
                    </div>
                  </>
                )}
            </>
          )}
        </div>
      )
    })
  }, [currentTasks, data, showMore, taskSet, unfinishedTaskIds, tasksQuery])

  const totalTaskStatistic = useMemo(() => {
    return currentTasks.reduce(
      (pre, cur) => {
        return {
          viewCount: pre.viewCount + (cur.statistic?.viewCount || 0),
          commentCount: pre.commentCount + (cur.statistic?.commentCount || 0),
          greatCount: pre.greatCount + (cur.statistic?.greatCount || 0),
          collectCount: pre.collectCount + (cur.statistic?.collectCount || 0),
          shareCount: pre.shareCount + (cur.statistic?.shareCount || 0),
        }
      },
      {
        viewCount: 0,
        commentCount: 0,
        greatCount: 0,
        collectCount: 0,
        shareCount: 0,
      },
    )
  }, [currentTasks])

  const isShowStatistic = useMemo(() => {
    return (
      currentTasks.filter((x) => x.stageStatus === PlatformResultStageStatus.SUCCESS).length > 0
    )
  }, [currentTasks])

  return (
    <>
      <Sheet open={isOpen} onOpenChange={onOpenChange}>
        <SheetContent
          className="flex w-[496px] flex-col gap-0 bg-secondary p-0"
          style={{ maxWidth: 'none' }}
        >
          <SheetHeader className="bg-white px-6 py-4">
            <SheetTitle>发布详情</SheetTitle>
            <VisuallyHidden>
              <SheetDescription>发布详情</SheetDescription>
            </VisuallyHidden>
          </SheetHeader>
          <div className="flex flex-col gap-5 px-5 pt-3">
            <div className="grid h-24 grid-cols-4 rounded-lg bg-white">
              {isLoading && <LoadingContainer />}
              {isSuccess && renderTaskStatistics(data)}
            </div>
          </div>
          <Tabs
            value={tab}
            onValueChange={setTab}
            defaultValue="value"
            className="mx-5 mt-3 flex shrink-0 flex-col overflow-hidden rounded-lg bg-white pl-4"
          >
            <TabsList>
              <TabsTrigger value="all">全部</TabsTrigger>
              <TabsTrigger value="fail">失败</TabsTrigger>
            </TabsList>
          </Tabs>

          {isShowStatistic && (
            <div className="mx-5 mt-3 flex items-center justify-between rounded-lg bg-white px-5 py-4">
              <div className="flex flex-col">
                <span className="text-[12px] text-[#757575]">播放</span>
                <span className="font-bold text-[#222222]">
                  {formatNumber(totalTaskStatistic.viewCount)}
                </span>
              </div>
              <div className="relative flex flex-col pl-4">
                <span className="text-[12px] text-[#757575]">评论</span>
                <span className="font-bold text-[#222222]">
                  {formatNumber(totalTaskStatistic.commentCount)}
                </span>
              </div>
              <div className="relative flex flex-col pl-4">
                <span className="text-[12px] text-[#757575]">点赞</span>
                <span className="font-bold text-[#222222]">
                  {formatNumber(totalTaskStatistic.greatCount)}
                </span>
              </div>
              <div className="relative flex flex-col pl-4">
                <span className="text-[12px] text-[#757575]">收藏</span>
                <span className="font-bold text-[#222222]">
                  {formatNumber(totalTaskStatistic.collectCount)}
                </span>
              </div>
              <div className="relative flex flex-col pl-4">
                <span className="text-[12px] text-[#757575]">分享</span>
                <span className="font-bold text-[#222222]">
                  {formatNumber(totalTaskStatistic.shareCount)}
                </span>
              </div>
            </div>
          )}

          {currentTasks.length === 0 && (
            <div className="mt-10 flex flex-col items-center justify-center">
              <img src={NoDataImage} alt="" className="h-[114px] w-[170px]" />
              <span className="mt-3 text-xs text-[#757575]">暂无数据</span>
            </div>
          )}
          <ScrollArea className="grow overflow-hidden">
            <div className="space-y-3 px-5 py-3">{taskElements}</div>
          </ScrollArea>
          {tasks && !taskSet.platforms.includes(platforms.WeiXinGongZhongHao) && (
            <SheetFooter className="shrink-0 bg-white px-6 py-2">
              <div
                id="rePush"
                onMouseEnter={() => {
                  setRePushDialogOpen(true)
                }}
              >
                <DropdownMenu open={rePushDialogOpen} onOpenChange={setRePushDialogOpen}>
                  <DropdownMenuTrigger asChild>
                    <Button
                      onClick={() => {
                        setRePushDialogOpen(true)
                      }}
                    >
                      重新发布
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem
                      onClick={async () => {
                        setRePushDialogOpen(false)
                        await rePushTaskSet(taskSet, true)
                        onOpenChange(false)
                      }}
                    >
                      全部账号
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={async () => {
                        setRePushDialogOpen(false)
                        await rePushTaskSet(taskSet, false)
                        onOpenChange(false)
                      }}
                      disabled={!hasFailedTask}
                    >
                      <div>仅发布失败</div>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </SheetFooter>
          )}
        </SheetContent>
      </Sheet>
    </>
  )

  function renderTaskStatistics(data: PushingTaskSetResponse): ReactNode {
    const totalTaskDuration = DateUtils.duration.convertTime(data.totalTaskDuration / 1000)
    const avTaskDuration = DateUtils.duration.convertTime(data.avTaskDuration / 1000)
    return (
      <>
        <div className="flex flex-col items-center justify-center">
          <span className="text-sm text-textSecondary">任务数</span>
          <span className="text-xl font-bold">{data.taskCount}</span>
        </div>
        <div className="flex flex-col items-center justify-center">
          <span className="text-sm text-textSecondary">失败</span>
          <span className="text-xl font-bold text-destructive">{data.failedCount}</span>
        </div>
        <div className="flex flex-col items-center justify-center text-sm">
          <span className="text-textSecondary">任务总耗时</span>
          <span>
            <span className="mr-0.5 text-xl font-bold">{totalTaskDuration.value || '-'}</span>
            <span className="text-sm text-textSecondary">{totalTaskDuration.unitName}</span>
          </span>
        </div>
        <div className="flex flex-col items-center justify-center text-sm">
          <span className="text-textSecondary">均耗时</span>
          <span>
            <span className="mr-0.5 text-xl font-bold">{avTaskDuration.value || '-'}</span>
            <span className="text-sm text-textSecondary">{avTaskDuration.unitName}/个</span>
          </span>
        </div>
      </>
    )
  }
}

interface TaskState {
  text: string
  status: PlatformResultStageStatus
  stage: string
}

import type { ImageFileInfo, Platform } from '@renderer/infrastructure/model'
import { electronService } from '@renderer/infrastructure/services'
import AddIcon from '@renderer/assets/publish/add.svg?react'
import { cn } from '@renderer/lib/utils'
import { Button } from '@renderer/shadcn-components/ui/button'
import CloseIcon from '@renderer/assets/common/close.svg?react'
import ChevronUpIcon from '@renderer/assets/chevron-up.svg?react'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import type { XYCoord } from 'react-dnd'
import { useDrag, useDrop } from 'react-dnd'
import { PhotoProvider, PhotoView } from 'react-photo-view'
import { useAssetLibrary } from '@renderer/hooks/useAssetLibrary'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@renderer/shadcn-components/ui/dropdown-menu'
import { notifyService } from '@renderer/hooks/use-notify'
import { useImageValidator } from '@renderer/pages/Publish/validation/useImageTextContentValidation'

import { useValidation } from '@renderer/hooks/validation/validation'
import { localPath2Url } from '@common/protocol'

const MAX_IMAGES = 35

interface ImageSelectorProps {
  images: ImageFileInfo[]
  onChange: (images: ImageFileInfo[]) => void
  setCover: (image: ImageFileInfo) => void
  selectedPlatforms: Platform[]
  showSetCoverButton?: boolean
}

export function ImageSelector({
  images,
  onChange,
  setCover,
  selectedPlatforms,
  showSetCoverButton = true,
}: ImageSelectorProps) {
  const { selectMultipleImageAsset } = useAssetLibrary()
  const imageContainerRef = useRef<HTMLDivElement>(null)
  const [imageContainerWitdth, setImageContainerWitdth] = useState(0)
  const [rowImageMax, setRowImageMax] = useState(0) // 每行最多可展示图片数量
  const [rowCount, setRowCount] = useState(0) // 图片行数
  const [expand, setExpand] = useState(true)

  const selectFromLocalFiles = async () => {
    try {
      const value = await electronService.openImageFiles('jpg', 'png', 'jpeg', 'webp')
      if (value) {
        // 限制最多选择 MAX_IMAGES 张图片
        const newImages = [...images, ...value].slice(0, MAX_IMAGES)
        onChange(newImages)
      }
    } catch (e) {
      console.error(e)
      notifyService.error('解析图片失败！')
    }
  }

  const selectFromAssetLibrary = async () => {
    try {
      const value = await selectMultipleImageAsset()
      if (value) {
        const newImages = [...images, ...value.slice(0, MAX_IMAGES - images.length)].slice(
          0,
          MAX_IMAGES,
        )
        onChange(newImages)
      }
    } catch (e) {
      console.error(e)
      notifyService.error('解析图片失败！')
    }
  }

  const handleRemoveImage = (index: number) => {
    const updatedImages = images.filter((_, i) => i !== index)
    onChange(updatedImages)
  }

  const moveImage = useCallback(
    (dragIndex: number, hoverIndex: number) => {
      console.log(dragIndex, hoverIndex)
      const newImages = [...images]
      const [reorderedItem] = newImages.splice(dragIndex, 1)
      newImages.splice(hoverIndex, 0, reorderedItem)
      onChange(newImages)
    },
    [images, onChange],
  )

  const imageMeta = useMemo(() => {
    if (!expand) {
      const sum = rowImageMax * 2
      const sliceCount = images.length >= MAX_IMAGES ? sum : sum - 1
      return images.slice(0, sliceCount)
    }
    return images
  }, [expand, images, rowImageMax])

  useEffect(() => {
    if (!imageContainerRef.current) return

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        setImageContainerWitdth(entry.contentRect.width)
      }
    })

    resizeObserver.observe(imageContainerRef.current)

    // 监听窗口大小变化
    const handleWindowResize = () => {
      if (imageContainerRef.current) {
        setImageContainerWitdth(imageContainerRef.current.clientWidth)
      }
    }

    window.addEventListener('resize', handleWindowResize)

    return () => {
      resizeObserver.disconnect()
      window.removeEventListener('resize', handleWindowResize)
    }
  }, [])

  useEffect(() => {
    // 计算当前每行最多可展示图片数量，x: 图片数量, imageWidth = 90, gap = 16
    // x * imageWidth + (x - 1) * gap <= imageContainerWitdth
    // 化简：x <= (imageContainerWitdth + gap) / (imageWidth + gap)
    const rowImageMax = Math.floor((imageContainerWitdth + 16) / 106)
    setRowImageMax(rowImageMax)

    const row = Math.ceil(
      (images?.length < MAX_IMAGES ? images?.length + 1 : images?.length) / rowImageMax,
    )
    setRowCount(row)
    setExpand(true)
  }, [imageContainerWitdth, images?.length, rowCount])

  return (
    <div className="flex flex-col gap-4">
      <div className={`flex flex-wrap gap-4`} ref={imageContainerRef}>
        {images.length < MAX_IMAGES && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="secondary" size="icon" className="h-[120px] w-[90px]">
                <AddIcon />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent side="right" align="start">
              <DropdownMenuItem onClick={selectFromAssetLibrary}>素材库</DropdownMenuItem>
              <DropdownMenuItem onClick={selectFromLocalFiles}>本地选择</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
        <PhotoProvider bannerVisible={false}>
          {imageMeta?.map((image, index) => (
            <DraggableImage
              key={index}
              image={image}
              index={index}
              moveImage={moveImage}
              onRemove={() => handleRemoveImage(index)}
              setCover={() => setCover(image)}
              selectedPlatforms={selectedPlatforms}
              showSetCoverButton={showSetCoverButton}
            />
          ))}
        </PhotoProvider>
      </div>

      {rowCount > 2 && (
        <div
          className="flex w-full cursor-pointer items-center justify-center gap-0.5"
          onClick={() => setExpand(!expand)}
        >
          <span className="text-sm text-[#666666]">{expand ? '收起' : '展开'}图片</span>
          <ChevronUpIcon className={cn('h-3.5 w-3.5', expand ? '' : 'rotate-180')} />
        </div>
      )}
    </div>
  )
}

interface DraggableImageProps {
  image: ImageFileInfo
  index: number
  moveImage: (dragIndex: number, hoverIndex: number) => void
  onRemove: () => void
  setCover: () => void
  selectedPlatforms: Platform[]
  showSetCoverButton: boolean
}

interface DragItem {
  index: number
  id: string
  type: string
}

function DraggableImage({
  image,
  index,
  moveImage,
  onRemove,
  setCover,
  selectedPlatforms,
  showSetCoverButton,
}: DraggableImageProps) {
  const { conclusion: imageConclusion } = useValidation(image, useImageValidator(selectedPlatforms))

  const ref = useRef<HTMLDivElement>(null)
  const [{ handlerId }, drop] = useDrop<DragItem, void, { handlerId: string | symbol | null }>({
    accept: 'IMAGE',
    collect(monitor) {
      return {
        handlerId: monitor.getHandlerId(),
      }
    },
    hover(item: DragItem, monitor) {
      if (!ref.current) {
        return
      }
      const dragIndex = item.index
      const hoverIndex = index

      // 不替换自身
      if (dragIndex === hoverIndex) {
        return
      }

      // 获取屏幕上的矩形
      const hoverBoundingRect = ref.current?.getBoundingClientRect()

      // 获取水平中点
      const hoverMiddleX = (hoverBoundingRect.left + hoverBoundingRect.right) / 2

      // 确定鼠标位置
      const clientOffset = monitor.getClientOffset()

      // 获取距离左侧的像素
      const hoverClientX = (clientOffset as XYCoord).x - hoverBoundingRect.left

      // 向右拖动
      if (dragIndex < hoverIndex && hoverClientX > hoverMiddleX) {
        return
      }

      // 向左拖动
      if (dragIndex > hoverIndex && hoverClientX > hoverMiddleX) {
        return
      }

      // 执行移动操作
      moveImage(dragIndex, hoverIndex)

      // 更新拖拽项的索引
      item.index = hoverIndex
    },
  })

  const [{ isDragging }, drag] = useDrag({
    type: 'IMAGE',
    item: { index, ...image },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  })

  drag(drop(ref))

  return (
    <div
      ref={ref}
      className="group relative h-[120px] w-[90px]"
      style={{ opacity: isDragging ? 0.5 : 1 }}
      data-handler-id={handlerId}
    >
      <div className="h-full w-full bg-transparent">
        <PhotoView src={localPath2Url(image.path)}>
          <img
            className={cn('h-full w-full rounded border-destructive object-cover', {
              'border-4': imageConclusion.hasInvalid,
            })}
            alt=""
            src={localPath2Url(image.path)}
          />
        </PhotoView>
      </div>
      {!isDragging && (
        <>
          <Button
            size="icon"
            variant="secondary"
            onClick={onRemove}
            className="absolute -right-1 -top-1 hidden h-3.5 w-3.5 rounded-full group-hover:flex"
          >
            <CloseIcon className="h-3.5 w-3.5" />
          </Button>
          {showSetCoverButton && (
            <Button
              onClick={setCover}
              variant="ghost"
              className="absolute bottom-2 left-2 hidden h-6 w-[74px] bg-black/60 text-xs font-normal text-white hover:bg-black/60 hover:text-white group-hover:flex"
            >
              设为封面
            </Button>
          )}
        </>
      )}
    </div>
  )
}

import { FormItem as PublishFormItem } from './FormItem'
import type {
  Account,
  IAssetLibraryItem,
  ImageFileInfo,
  MultipleVideoAccountViewModel,
  VideoFileInfo,
} from '@renderer/infrastructure/model'
import { MultipleVideoAccountViewModelItem } from '@renderer/infrastructure/model'
import { electronService } from '@renderer/infrastructure/services'
import { useMemo, useState } from 'react'
import UploadIcon from '@renderer/assets/publish/upload.svg?react'
import { ValidationMessage } from '@renderer/components/ValidationMessage'
import { useValidation } from '@renderer/hooks/validation/validation'
import { RuleResult } from '@renderer/infrastructure/validation/rule'
import { Validator } from '@renderer/infrastructure/validation/validator'
import { useAssetLibrary } from '@renderer/hooks/useAssetLibrary'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@renderer/shadcn-components/dropdown-menu'
import { useSummaryContext } from '@renderer/infrastructure/validation/use-summary-context'
import {
  BalanceSummaryContext,
  CommonSummaryContext,
  FormStateContext,
} from '@renderer/pages/Publish/context/summary-context'
import { useFormStateContext } from '@renderer/infrastructure/validation/use-form-state-context'
import { useNotify } from '@renderer/hooks/use-notify'
import { useSystemConfigStore } from '@renderer/store/systemConfigStore'
import type { DraftFunction } from 'use-immer'
import { MultipleVideoAccountItem } from '@renderer/pages/Publish/components/MultipleVideoAccountItem'
import { SessionCheckProgress, SessionValidSummary } from './session-check/session-check-progress'
import { ByteSize } from '@renderer/infrastructure/model/utils/byte-size'
import { useTrafficValidator } from '../validation/useBalanceValidation'
import { platformNames } from '@common/model/platform-name'
import { noop, useSystem } from '@renderer/pages/context'
import { useLocalFileService } from '@renderer/infrastructure/services/application-service/infrastructure-service/LocalFileService'
import { SuperValidationMessage } from '@renderer/components/SuperValidationMessage'
import { useInnerContextStore } from '@renderer/store/contextStore'

export const validators = {
  items: new Validator<MultipleVideoAccountViewModelItem[]>().addRule((subject) => {
    if (subject.length === 0) {
      return new RuleResult('invalid', '请添加至少一个视频')
    }
    return RuleResult.valid
  }),
  account: new Validator<Account | null>()
    .addRule((subject) => {
      if (!subject) {
        return new RuleResult('invalid', '请选择账号')
      }
      return RuleResult.valid
    })
    .addRule((subject) => {
      if (subject && !subject.isSessionValid()) {
        return new RuleResult('invalid', '账号已失效')
      }
      return RuleResult.valid
    }),
  superVideo: new Validator<MultipleVideoAccountViewModelItem[]>().addRule((subject) => {
    const superIdsMap = {}
    subject.forEach((item) => {
      if (item.video?.superId && item.account?.platform.name === platformNames.DouYin) {
        if (!superIdsMap[item.video.superId]) {
          superIdsMap[item.video.superId] = 0
        }
        superIdsMap[item.video.superId]++
      }
    })

    for (const key in superIdsMap) {
      if (superIdsMap[key] > 1) {
        return new RuleResult('invalid', '超级编导视频素材一个视频只能对应一个抖音账号')
      }
    }

    return RuleResult.valid
  }),
  video: new Validator<VideoFileInfo | null>().addRule((subject) => {
    if (!(subject && subject.filePath)) {
      return new RuleResult('invalid', '请选择视频')
    }
    return RuleResult.valid
  }),
  cover: new Validator<ImageFileInfo | null>().addRule((subject) => {
    if (!(subject && subject.path)) {
      return new RuleResult('invalid', '请选择封面')
    }
    return RuleResult.valid
  }),
}

interface MultipleVideoAccountProps {
  config: MultipleVideoAccountViewModel
  setConfig: (func: DraftFunction<MultipleVideoAccountViewModel>) => void
}

export function MultipleVideoAccount({ config, setConfig }: MultipleVideoAccountProps) {
  const currentTeam = useInnerContextStore((state) => state.currentTeam)
  const { maxPushRestrict } = useSystemConfigStore((state) => ({
    maxPushRestrict: state.maxPushRestrict,
  }))
  const { onSetDialog, onSetDialogSub } = useSystem()
  const localFileService = useLocalFileService()

  const { notifyService } = useNotify()

  const formState = useFormStateContext(FormStateContext)

  const commonSummary = useSummaryContext(CommonSummaryContext)
  const balanceSummary = useSummaryContext(BalanceSummaryContext)

  const canAddVideo = useMemo(
    () => config.items.length < maxPushRestrict,
    [config.items, maxPushRestrict],
  )
  const { conclusion: itemsConclusion } = useValidation(
    config.items,
    validators.items,
    commonSummary,
  )

  const { conclusion: superVideoConclusion } = useValidation(
    config.items,
    validators.superVideo,
    commonSummary,
  )

  // 将要使用的流量
  const trafficAboutToUse = useMemo(
    () =>
      config.items.reduce((acc, item) => {
        if (item.video) {
          return acc.add(item.video.fileByteSize)
        }
        return acc
      }, ByteSize.fromB(0)),
    [config.items],
  )

  useValidation(trafficAboutToUse, useTrafficValidator(), balanceSummary)

  const { selectMultipleVideoAsset } = useAssetLibrary()
  const [popoverOpen, setPopoverOpen] = useState(false)

  const selectFromAssetLibrary = async () => {
    setPopoverOpen(false)
    const videoFiles = await selectMultipleVideoAsset()
    if (videoFiles) {
      // 如果视频数量超过限制，截取前面的视频
      if (videoFiles.length > maxPushRestrict - config.items.length) {
        videoFiles.splice(maxPushRestrict - config.items.length)
        notifyService.error(`最多只能添加${maxPushRestrict}个视频`)
      }

      setConfig((draft) => {
        draft.items.push(...videoFiles.map((x) => new MultipleVideoAccountViewModelItem(x)))
      })
      formState.setDirty(true)
    }
  }

  const selectFromLocalFile = async () => {
    setPopoverOpen(false)
    const videoFiles = await electronService.openVideoFiles()
    if (videoFiles) {
      if (videoFiles.length > maxPushRestrict - config.items.length) {
        videoFiles.splice(maxPushRestrict - config.items.length)
        notifyService.error(`最多只能添加${maxPushRestrict}个视频`)
      }

      setConfig((draft) => {
        draft.items.push(...videoFiles.map((x) => new MultipleVideoAccountViewModelItem(x)))
      })
      formState.setDirty(true)
    }
  }

  async function selectMarketAgentFile() {
    onSetDialog((dialogMap) => ({
      ...dialogMap.marketAgent,
      dialogContentProps: {
        className: 'super-dialog w-auto bg-white p-0',
        style: { maxWidth: 'none' },
      },
      elementProps: {
        selectMarketAgentIds: config.items.map((x) => x.video?.mediaId).filter(Boolean),
        onChangeFile: (files: IAssetLibraryItem[]) => {
          onSetDialogSub((dialogMap) => ({
            ...dialogMap.assetLibraryDownload,
            elementProps: {
              list: files,
              async onDone(list) {
                const res = await Promise.all(
                  list.map(async (item) => {
                    const value = await localFileService.getVideoFileInfo(item.filePath)
                    value.mediaId = item.id
                    return value
                  }),
                )

                console.log('multipleVideo', res)

                setConfig((draft) => {
                  draft.items.push(...res.map((x) => new MultipleVideoAccountViewModelItem(x)))
                })
                onSetDialog(noop)
              },
            },
            dialogContentProps: {
              onInteractOutside(event) {
                event.preventDefault()
              },
            },
          }))
        },
      },
    }))
  }

  async function selectSuperFile() {
    onSetDialog((dialogMap) => ({
      ...dialogMap.superDirector,
      dialogContentProps: {
        className: 'super-dialog w-auto bg-white p-0',
        style: { maxWidth: 'none' },
      },
      elementProps: {
        selectSuperIds: config.items.map((x) => x.video?.superId).filter(Boolean),
        onChangeFile: (files: IAssetLibraryItem[]) => {
          onSetDialogSub((dialogMap) => ({
            ...dialogMap.assetLibraryDownload,
            elementProps: {
              list: files,
              async onDone(list) {
                const res = await Promise.all(
                  list.map(async (item) => {
                    const value = await localFileService.getVideoFileInfo(item.filePath)
                    value.superId = item.id
                    return value
                  }),
                )

                console.log('多选的时候出现', res)

                setConfig((draft) => {
                  draft.items.push(...res.map((x) => new MultipleVideoAccountViewModelItem(x)))
                })
                formState.setDirty(true)
                onSetDialog(noop)
              },
            },
            dialogContentProps: {
              onInteractOutside(event) {
                event.preventDefault()
              },
            },
          }))
        },
      },
    }))
  }

  const superdirEnabled = useMemo(() => {
    const superdir = currentTeam?.components?.find((c) => c.name === 'superdir')
    return !!superdir?.enabled
  }, [currentTeam])

  const marketAgentEnabled = useMemo(() => {
    const marketAgent = currentTeam?.components?.find((c) => c.name === 'marketAgent')
    return !!marketAgent?.enabled
  }, [currentTeam])

  return (
    <div className="flex flex-col space-y-9">
      <PublishFormItem label="视频/账号/封面" required={true} contentWidthLimited={false}>
        <div className="flex w-full flex-wrap gap-4">
          {config.items.map((item, index) => (
            <MultipleVideoAccountItem
              key={index}
              item={item}
              onChange={(itemDraft: DraftFunction<MultipleVideoAccountViewModelItem>) => {
                setConfig((draft) => {
                  itemDraft(draft.items[index])
                })
              }}
              onRemove={() => {
                setConfig((draft) => {
                  draft.items.splice(index, 1)
                })
              }}
              index={index}
              onDropAccount={(fromIndex, toIndex) => {
                const formAccount = config.items[fromIndex].account
                const toAccount = config.items[toIndex].account
                if (!toAccount) {
                  setConfig((draft) => {
                    draft.items[toIndex].account = formAccount
                  })
                } else {
                  setConfig((draft) => {
                    draft.items[fromIndex].account = toAccount
                    draft.items[toIndex].account = formAccount
                  })
                }
              }}
            />
          ))}
          {canAddVideo && (
            <DropdownMenu open={popoverOpen} onOpenChange={setPopoverOpen}>
              <DropdownMenuTrigger
                className="flex h-[180px] w-[135px] cursor-pointer flex-col items-center justify-center rounded-md bg-secondary"
                onClick={(e) => {
                  e.stopPropagation()
                }}
              >
                <div className="mb-2">
                  <UploadIcon className={'h-4 w-4'} />
                </div>
                <div className="text-sm text-gray-800">上传</div>
              </DropdownMenuTrigger>
              <DropdownMenuContent side="right" align="start">
                <DropdownMenuItem onClick={selectFromAssetLibrary}>素材库</DropdownMenuItem>
                <DropdownMenuItem onClick={selectFromLocalFile}>本地选择</DropdownMenuItem>
                {superdirEnabled && (
                  <DropdownMenuItem onClick={selectSuperFile}>超级编导</DropdownMenuItem>
                )}
                {marketAgentEnabled && (
                  <DropdownMenuItem onClick={selectMarketAgentFile}>AI营销智能体</DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
        <ValidationMessage
          subject={config.items}
          conclusion={itemsConclusion}
          formState={formState}
        />
        <SuperValidationMessage
          subject={config.items}
          conclusion={superVideoConclusion}
          formState={formState}
        />
        <SessionCheckProgress />
        <SessionValidSummary
          accounts={config.items.map((x) => x.account).filter((x) => x !== null)}
        />
      </PublishFormItem>
    </div>
  )
}

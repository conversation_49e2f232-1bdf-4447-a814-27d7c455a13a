import { Button } from '@renderer/shadcn-components/ui/button'
import { OperatorsFilter } from '@renderer/components/OperatorsFilter'
import type { PublishRecordsFilter } from '@renderer/pages/Publish/types/publish-records-filter'

// import { RotateCw } from 'lucide-react'
// import { LoadingScreen } from '@renderer/components/LoadingScreen'
// import { NewPublishHoverCard } from '@renderer/pages/Publish/new-publish-hover-card'
import { MultipleOperation } from '../MultipleOperation'
import { ManagerRestricted } from '@renderer/components/ManagerRestricted'
import { usePublishOperationStore } from '@renderer/store/publishOperationStore'
// import { ContentTypeFilter } from './ContentTypeFilter'
import { PushStateFilter } from './PushStateFilter'
import { cn } from '@renderer/lib/utils'
import { useSystem } from '@renderer/pages/context'
import { NewPublishHoverCard } from '../new-publish-hover-card'
import { ContentTypeFilter } from './ContentTypeFilter'

interface TopActionBarProps {
  filter: PublishRecordsFilter
  setFilter: (update: (draft: PublishRecordsFilter) => void) => void
  newPublish: () => void
  isLoading: boolean
}

export function CloudPublishRecordsActionBar({
  filter,
  setFilter,
  // newPublish,
  isLoading,
}: TopActionBarProps) {
  const { onSetDialog } = useSystem()
  const [isSelectMode] = usePublishOperationStore((state) => [state.isSelectMode])

  return (
    <div className="flex items-center gap-2">
      <div className="flex flex-grow gap-3">
        <ManagerRestricted>
          <div className="w-40">
            <OperatorsFilter
              value={filter.operators}
              onChange={(value) => {
                setFilter((x) => {
                  x.operators = value
                })
              }}
            />
          </div>
        </ManagerRestricted>
        <div className="w-36">
          <ContentTypeFilter
            value={filter.contentType}
            onChange={(value) => {
              setFilter((x) => {
                x.contentType = value
              })
            }}
          />
        </div>
        <div className="w-36">
          <PushStateFilter
            value={filter.pushState}
            onChange={(value) => {
              setFilter((x) => {
                x.pushState = value
              })
            }}
          />
        </div>
        <div>
          <Button
            disabled={isLoading}
            variant="outline"
            onClick={() => {
              onSetDialog((dialogMap) => {
                return {
                  ...dialogMap.exportTasks,
                }
              })
              // queryClient.invalidateQueries({
              //   queryKey: ['cloudRecords', filter],
              // })
            }}
          >
            导出
            {/* {isLoading ? <LoadingScreen size={'20px'} /> : <RotateCw color={'#757575'} size={20} />} */}
          </Button>
        </div>

        <div
          className={cn('flex flex-shrink-0 gap-1', {
            'ml-auto': isSelectMode,
          })}
        >
          <MultipleOperation />
        </div>
      </div>
      {!isSelectMode && (
        <div className="flex-shrink">
          <NewPublishHoverCard></NewPublishHoverCard>
        </div>
      )}
    </div>
  )
}

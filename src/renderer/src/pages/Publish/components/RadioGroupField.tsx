import { RadioGroup, RadioGroupItem } from '@renderer/shadcn-components/radio-group'
import { Label } from '@renderer/shadcn-components/ui/label'
import type { Option } from '@renderer/infrastructure/model/option'

export interface RadioGroupFieldProps<T> {
  value: T
  onChange: (value: T) => void
  options: Option<T>[]
  /**
   * 自定义className，会与自动布局样式合并
   */
  className?: string
}

/**
 * 统一的单选组件
 * 根据选项数量自动决定布局方式：
 * - 选项数量 ≤ 2：横向排列（flex flex-row space-x-6 pt-2）
 * - 选项数量 > 2：纵向排列（flex flex-col gap-3 pt-2）
 */
export function RadioGroupField<T>({
  value,
  onChange,
  options,
  className = '',
}: RadioGroupFieldProps<T>) {
  // 根据选项数量决定布局方式
  const isHorizontal = options.length <= 2
  const layoutClassName = isHorizontal ? 'flex flex-row space-x-6 pt-2' : 'flex flex-col gap-3 pt-2'

  // 合并自定义className
  const finalClassName = `${layoutClassName} ${className}`.trim()

  // 找到当前值对应的选项key
  const currentOption = options.find((opt) => opt.value === value)
  const displayValue = currentOption?.key || ''

  const handleValueChange = (selectedKey: string) => {
    // 根据key查找对应的选项
    const option = options.find((opt) => opt.key === selectedKey)
    if (option) {
      onChange(option.value)
    }
  }

  return (
    <RadioGroup value={displayValue} onValueChange={handleValueChange} className={finalClassName}>
      {options.map((option) => {
        return (
          <div
            key={option.key}
            className={
              isHorizontal
                ? 'flex items-center space-x-2'
                : 'flex items-center gap-3 rounded-md transition-colors hover:bg-white/80'
            }
          >
            <RadioGroupItem value={option.key} id={option.key} />
            <Label
              className="cursor-pointer text-sm font-medium"
              onClick={() => handleValueChange(option.key)}
            >
              {option.label}
            </Label>
          </div>
        )
      })}
    </RadioGroup>
  )
}

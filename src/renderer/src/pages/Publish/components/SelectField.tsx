import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@renderer/shadcn-components/CustomSelect'
import type { Option } from '@renderer/infrastructure/model/option'

export interface SelectFieldProps<T> {
  value: T
  onChange: (value: T) => void
  options: Option<T>[]
  placeholder?: string
  /**
   * 自定义className
   */
  className?: string
}

/**
 * 统一的下拉选择组件
 * 用于替换 RadioGroupField 组件，提供下拉选择的交互方式
 */
export function SelectField<T>({
  value,
  onChange,
  options,
  placeholder = '请选择',
  className = '',
}: SelectFieldProps<T>) {
  // 找到当前值对应的选项key
  const currentOption = options.find((opt) => opt.value === value)
  const displayValue = currentOption?.key || ''

  const handleValueChange = (selectedKey: string) => {
    // 根据key查找对应的选项
    const option = options.find((opt) => opt.key === selectedKey)
    if (option) {
      onChange(option.value)
    }
  }

  return (
    <Select value={displayValue} onValueChange={handleValueChange}>
      <SelectTrigger className={`w-[266px] ${className}`.trim()}>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {options.map((option) => (
          <SelectItem key={option.key} value={option.key}>
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}

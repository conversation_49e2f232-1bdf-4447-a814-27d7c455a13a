'use client'

import * as React from 'react'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from '@renderer/shadcn-components/ui/command'
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@renderer/shadcn-components/ui/dialog'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useEffect } from 'react'
import { LoadingContainer } from '@renderer/components/LoadingContainer'
import type { SpiderAccount } from '@renderer/infrastructure/model'
import { douYinPlatformService, useAuthorizeService } from '@renderer/infrastructure/services'
import type { AccountSession, PlatformDataItem } from '@common/structure'
import { ButtonTrigger } from '@renderer/components/ButtonTrigger'
import { useEagerFormState, useValidation } from '@renderer/hooks/validation/validation'
import { Validator } from '@renderer/infrastructure/validation/validator'
import { RuleResult } from '@renderer/infrastructure/validation/rule'
import { ValidationMessage } from '@renderer/components/ValidationMessage'
import SearchInput from '../searchInput'
import { Popover, PopoverContent, PopoverTrigger } from '@renderer/shadcn-components/ui/popover'

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@renderer/shadcn-components/ui/select'
import { Avatar, AvatarFallback, AvatarImage } from '@renderer/shadcn-components/ui/avatar'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@renderer/components/Tabs'
import { Store } from 'lucide-react'
import { Button } from '@renderer/shadcn-components/ui/button'
import { RadioGroup, RadioGroupItem } from '@renderer/shadcn-components/radio-group'
import { Label } from '@renderer/shadcn-components/ui/label'

const accountValidator = new Validator<SpiderAccount>().addRule((subject) => {
  if (!subject.isSessionValid()) {
    return new RuleResult('invalid', '请先登录已失效账号')
  }
  return RuleResult.valid
})

export function SelectPositionByDouyinItem({
  account,
  session,
  value,
  onChange,
  selectedAccounts,
}: {
  selectedAccounts: SpiderAccount[]
  account: SpiderAccount
  session: AccountSession
  value: PlatformDataItem<{
    raw: {
      spu_count?: number
      cps_spu_count?: number
      [key: string]: unknown
    }
    isBringLocations?: boolean
    accountId?: string
  }>
  onChange: (value: PlatformDataItem | null) => void
}) {
  const [open, setOpen] = React.useState(false)
  const [searchText, setSearchText] = React.useState('')
  const [bringValue, setBringValue] = React.useState<typeof value | null>(null)
  const [isBringLocations, setIsBringLocations] = React.useState<null | boolean>(null)
  const [isScp, setIsScp] = React.useState(false)

  const currentSelectType = React.useRef<'clock' | 'bring'>('clock')
  const currentSelectAccountId = React.useRef<string | null>(null)

  const locationType = React.useRef({
    clock: 0,
    bring: 0,
  })

  const [currentTab, setCurrentTab] = React.useState('0')

  const [bringOpen, setBringOpen] = React.useState(false)

  const queryClient = useQueryClient()

  const query = useQuery({
    queryFn: async () => {
      return await douYinPlatformService.getLocations(
        account,
        session,
        searchText,
        locationType.current[currentSelectType.current],
      )
    },
    queryKey: ['locations-douyin', searchText, account.platform.name],
    enabled: !!searchText,
  })

  const eagerFormState = useEagerFormState()
  const authorizeService = useAuthorizeService()

  const { conclusion: accountConclusion } = useValidation(account, accountValidator)

  useEffect(() => {
    if (!open) {
      setIsBringLocations(null)
      setBringValue(null)
    } else {
      const init = async () => {
        console.log(value)
        const account = selectedAccounts.find((item) => item.accountId === value?.raw.accountId)
        const { session } = await authorizeService.getAccountSession(account!)
        const res = await douYinPlatformService.checkBringLocations(account!, session)

        setIsBringLocations(res)
        setIsScp(res)
      }
      value && init()
    }
  }, [authorizeService, open, selectedAccounts, value])

  useEffect(() => {
    if (!account.isSessionValid()) {
      setOpen(false)
    }
  }, [account, account.sessionState])

  return (
    <div key={account.platform.name} className="w-[266px]">
      <span className="mb-1 text-sm text-gray">{account.platform.name}</span>
      {open && <div className="fixed inset-0 z-[10] bg-[rgba(0,0,0,0.3)]"></div>}
      <Dialog
        modal={false}
        open={open}
        onOpenChange={(open) => {
          setOpen(open && accountConclusion.valid)
        }}
      >
        <DialogTrigger asChild>
          <div>
            <ButtonTrigger
              value={value?.text ?? ''}
              label="选择位置"
              onClean={() => onChange(null)}
              disabled={!accountConclusion.valid}
            />
          </div>
        </DialogTrigger>
        <DialogContent className="w-[60vw] max-w-[600px] gap-0 bg-[#ffffff] p-0">
          <DialogHeader className="border-b px-6 py-4">
            <DialogTitle>设置地理位置</DialogTitle>
          </DialogHeader>
          <div className="flex flex-col gap-4 p-4">
            <div className="rounded-lg bg-[#F8F8FA] p-4 text-[12px] text-[#757575]">
              1、若选择的账号有‘带货权限’则可设置带货模式、打卡模式，选择了带货模式的地址，则当前有带货权限的账号全部关联该店铺进行带货推广。‘打卡模式’地址则以地图形式挂载，如果只设置了打卡模式，则所有账号已地图形式挂载。未选择打卡模式地址则不关联位置。
              2、若选择的账号没有‘带货权限’则所有账号只可设置普通地理位置形式。
            </div>

            <div className="flex flex-col gap-4">
              <div className="flex items-center gap-4">
                <div className="w-[80px] text-[14px]">
                  关联账号 <span className="text-[#F73C5B]">*</span>
                </div>

                <Select
                  defaultValue={value?.raw.accountId}
                  onValueChange={async (id) => {
                    currentSelectAccountId.current = id
                    const account = selectedAccounts.find((item) => item.accountId === id)
                    const { session } = await authorizeService.getAccountSession(account!)
                    const res = await douYinPlatformService.checkBringLocations(account!, session)

                    setIsBringLocations(res)
                    setIsScp(res)
                  }}
                >
                  <SelectTrigger className="w-[280px]">
                    <SelectValue placeholder="请选择账号" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectLabel>账号</SelectLabel>
                      {selectedAccounts
                        ?.filter((item) => item.platform.name === account.platform.name)
                        .map((item) => (
                          <SelectItem key={item.accountId} value={item.accountId}>
                            <div className="flex items-center gap-2">
                              <Avatar className="h-6 w-6">
                                <AvatarImage src={item.avatar} />
                                <AvatarFallback>{item.displayName[0]}</AvatarFallback>
                              </Avatar>
                              <span>{item.displayName}</span>
                            </div>
                          </SelectItem>
                        ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>

              {isBringLocations !== null && (
                <Popover
                  open={bringOpen}
                  onOpenChange={(val) => {
                    currentSelectType.current = 'bring'
                    setBringOpen(val)
                  }}
                >
                  <div className="flex items-center gap-4">
                    <div className="w-[80px] text-[14px]">地理位置</div>
                    <PopoverTrigger asChild className="flex-1">
                      <div>
                        <ButtonTrigger
                          value={bringValue?.text ?? value?.text ?? ''}
                          label="请选择地理位置"
                          onClean={() => setBringValue(value?.text ? value : null)}
                          disabled={!accountConclusion.valid}
                        />
                      </div>
                    </PopoverTrigger>
                  </div>

                  <PopoverContent className="w-[366px] p-0" side="bottom">
                    <Command shouldFilter={false}>
                      <Tabs
                        onValueChange={(val) => {
                          locationType.current.bring = Number(val)

                          queryClient.setQueriesData({ queryKey: ['locations-douyin'] }, [])
                          setSearchText('')
                          setCurrentTab(val)
                        }}
                        value={currentTab}
                        className="flex flex-1 flex-col overflow-hidden"
                      >
                        <div className="flex items-center justify-between border-b">
                          <TabsList className="w-auto flex-1 flex-shrink-0 gap-2 border-none">
                            <TabsTrigger value="0">
                              <span className="electron-no-drag px-4 text-base">本地</span>
                            </TabsTrigger>
                            {isBringLocations && (
                              <TabsTrigger value="2">
                                <span className="electron-no-drag px-4 text-base">全国</span>
                              </TabsTrigger>
                            )}
                          </TabsList>
                        </div>

                        <TabsContent value="0" className="mt-0 flex-1 overflow-auto !pt-1">
                          <SearchInput
                            placeholder="请输入关键字进行搜索"
                            warpClass="h-9 p-1 pl-3 pb-2"
                            onSearch={setSearchText}
                          ></SearchInput>
                          <div className="h-[1px] bg-[#919eab39]"></div>
                          <CommandList>
                            {query.isLoading ? (
                              <div className="flex py-6">
                                <LoadingContainer className="h-5 w-5" />
                              </div>
                            ) : (
                              <CommandEmpty>没有找到位置</CommandEmpty>
                            )}
                            <CommandGroup>
                              {query.data &&
                                query.data.map((item) => (
                                  <CommandItem
                                    // disabled={item.hasPendingOrder || item.hasVip}
                                    key={item.id}
                                    value={item.id}
                                    className="flex w-full items-center justify-between gap-2"
                                    onSelect={(currentValue) => {
                                      setBringValue(
                                        query.data.find(
                                          (item) => item.id === currentValue,
                                        ) as unknown as typeof bringValue,
                                      )
                                      setBringOpen(false)
                                      // setOpen(false)
                                    }}
                                  >
                                    <div className="flex w-full flex-1 flex-col">
                                      <div className="flex items-center justify-between">
                                        <span className="truncate">{item.text}</span>
                                        {isBringLocations && (
                                          <div className="flex flex-shrink-0">
                                            {!!item.raw?.spu_count && (
                                              <span className="flex items-center gap-1 text-[12px]">
                                                <Store size={12} color="#919EAB" />
                                                {item.raw?.spu_count}
                                                件商品
                                              </span>
                                            )}
                                            {!!item.raw?.cps_spu_count && (
                                              <span className="text-[12px]">
                                                ·{item.raw?.cps_spu_count}
                                                件返佣
                                              </span>
                                            )}
                                          </div>
                                        )}
                                      </div>

                                      <span className="truncate text-[12px] text-[#919EAB]">
                                        {item.raw?.address_info?.address}
                                      </span>
                                    </div>
                                  </CommandItem>
                                ))}
                            </CommandGroup>
                          </CommandList>
                        </TabsContent>
                        <TabsContent value="2" className="mt-0 flex-1 !pt-1">
                          <SearchInput
                            placeholder="请输入关键字进行搜索"
                            warpClass="h-9 p-1 pl-3 pb-2"
                            onSearch={setSearchText}
                          ></SearchInput>
                          <div className="h-[1px] bg-[#919eab39]"></div>
                          <CommandList>
                            {query.isLoading ? (
                              <div className="flex py-6">
                                <LoadingContainer className="h-5 w-5" />
                              </div>
                            ) : (
                              <CommandEmpty>没有找到位置</CommandEmpty>
                            )}
                            <CommandGroup>
                              {query.data &&
                                query.data.map((item) => (
                                  <CommandItem
                                    // disabled={item.hasPendingOrder || item.hasVip}
                                    key={item.id}
                                    value={item.id}
                                    className="flex w-full items-center justify-between gap-2"
                                    onSelect={(currentValue) => {
                                      const data = query.data.find(
                                        (item) => item.id === currentValue,
                                      ) as unknown as typeof bringValue
                                      setBringValue(data)
                                      setBringOpen(false)

                                      // setOpen(false)
                                    }}
                                  >
                                    <div className="flex w-full flex-1 flex-col">
                                      <div className="flex items-center justify-between">
                                        <span className="truncate">{item.text}</span>
                                        <div className="flex flex-shrink-0">
                                          {!!(item.raw as unknown as { spu_count: number })
                                            ?.spu_count && (
                                            <span className="flex items-center gap-1 text-[12px]">
                                              <Store size={12} color="#919EAB" />
                                              {
                                                (item.raw as unknown as { spu_count: number })
                                                  ?.spu_count
                                              }
                                              件商品
                                            </span>
                                          )}
                                          {!!(item.raw as unknown as { cps_spu_count: number })
                                            ?.cps_spu_count && (
                                            <span className="text-[12px]">
                                              ·
                                              {
                                                (
                                                  item.raw as unknown as {
                                                    cps_spu_count: number
                                                  }
                                                )?.cps_spu_count
                                              }
                                              件返佣
                                            </span>
                                          )}
                                        </div>
                                      </div>

                                      <span className="truncate text-[12px] text-[#919EAB]">
                                        {
                                          (item.raw as { address_info: { address: string } })
                                            ?.address_info?.address
                                        }
                                      </span>
                                    </div>
                                  </CommandItem>
                                ))}
                            </CommandGroup>
                          </CommandList>
                        </TabsContent>
                      </Tabs>
                    </Command>
                  </PopoverContent>
                </Popover>
              )}
            </div>

            {isBringLocations && (
              <div className="flex items-center gap-4">
                <div className="w-[80px] text-[14px]">位置类型</div>
                <RadioGroup
                  defaultValue={`${isScp}`}
                  className="flex gap-4"
                  onValueChange={(value) => (value === 'true' ? setIsScp(true) : setIsScp(false))}
                >
                  <div className="flex items-center gap-3">
                    <RadioGroupItem value="true" id="r1" />
                    <Label htmlFor="r1">带货模式</Label>
                  </div>
                  <div className="flex items-center gap-3">
                    <RadioGroupItem value="false" id="r2" />
                    <Label htmlFor="r2">打卡模式</Label>
                  </div>
                </RadioGroup>
              </div>
            )}
          </div>
          <DialogFooter className="p-4">
            <Button variant="outline" onClick={() => setOpen(false)}>
              取消
            </Button>
            <Button
              onClick={() => {
                if (!bringValue) return

                onChange({
                  text: bringValue.text,
                  id: bringValue.id,
                  raw: {
                    ...bringValue.raw,
                    isScp,
                    accountId: currentSelectAccountId.current,
                    isBringLocations: isBringLocations,
                  },
                })

                setOpen(false)
              }}
            >
              确定
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <ValidationMessage
        subject={account}
        conclusion={accountConclusion}
        formState={eagerFormState}
      />
    </div>
  )
}

'use client'

import * as React from 'react'
import { CheckIcon } from '@radix-ui/react-icons'

import { cn } from '@renderer/lib/utils'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from '@renderer/shadcn-components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '@renderer/shadcn-components/ui/popover'
import { useQuery } from '@tanstack/react-query'
import { useEffect } from 'react'
import { LoadingContainer } from '@renderer/components/LoadingContainer'
import type { SpiderAccount } from '@renderer/infrastructure/model'
// import { platformNames } from '@common/model/platform-name'
// import type { PlatformService } from '@renderer/infrastructure/services'
// import {
//   zhiHuPlatformService,
//   qiEHaoPlatformService,
//   aiQiYiPlatformService,
//   wangYiHaoPlatformService,
//   yiDianHaoPlatformService,
//   bilibiliPlatformService,
//   souHuHaoPlatformService,
//   douYinPlatformService,
//   kuaiShouPlatformService,
//   xiaoHongShuPlatformService,
//   weiXinShiPinHaoPlatformService,
//   baiJiaHaoPlatformService,
//   touTiaoHaoPlatformService,
//   xinLangWeiBoPlatformService,
//   tengXunWeiShiPlatformService,
// } from '@renderer/infrastructure/services'
import type { AccountSession, PlatformDataItem } from '@common/structure'
import { ButtonTrigger } from '@renderer/components/ButtonTrigger'
import { useEagerFormState, useValidation } from '@renderer/hooks/validation/validation'
import { Validator } from '@renderer/infrastructure/validation/validator'
import { RuleResult } from '@renderer/infrastructure/validation/rule'
import { ValidationMessage } from '@renderer/components/ValidationMessage'
import SearchInput from '../searchInput'
import { usePushingTaskSetApi } from '@renderer/infrastructure/services/entity-service/cloud/pushing-task-set-api'

// const platformServiceRecord: Record<string, PlatformService> = {
//   [platformNames.DouYin]: douYinPlatformService,
//   [platformNames.KuaiShou]: kuaiShouPlatformService,
//   [platformNames.XiaoHongShu]: xiaoHongShuPlatformService,
//   [platformNames.WeiXinShiPinHao]: weiXinShiPinHaoPlatformService,
//   [platformNames.WangYiHao]: wangYiHaoPlatformService,
//   [platformNames.YiDianHao]: yiDianHaoPlatformService,
//   [platformNames.BiliBili]: bilibiliPlatformService,
//   [platformNames.SouHuHao]: souHuHaoPlatformService,
//   [platformNames.ZhiHu]: zhiHuPlatformService,
//   [platformNames.QiEHao]: qiEHaoPlatformService,
//   [platformNames.AiQiYi]: aiQiYiPlatformService,
//   [platformNames.BaiJiaHao]: baiJiaHaoPlatformService,
//   [platformNames.TouTiaoHao]: touTiaoHaoPlatformService,
//   [platformNames.XinLangWeiBo]: xinLangWeiBoPlatformService,
//   [platformNames.TengXunWeiShi]: tengXunWeiShiPlatformService,
// }

const accountValidator = new Validator<SpiderAccount>().addRule((subject) => {
  if (!subject.isSessionValid()) {
    return new RuleResult('invalid', '请先登录已失效账号')
  }
  return RuleResult.valid
})

export function SelectPositionItem<T extends PlatformDataItem | null>({
  account,
  // session,
  value,
  onChange,
}: {
  account: SpiderAccount
  session: AccountSession
  value: T
  onChange: (value: T) => void
}) {
  const pushingTaskSetApi = usePushingTaskSetApi()
  const [open, setOpen] = React.useState(false)
  const [searchText, setSearchText] = React.useState('')

  const query = useQuery({
    queryFn: async () => {
      // @ts-expect-error any
      return (
        await pushingTaskSetApi.getLocal(account.accountId, {
          keyWord: searchText,
        })
      ).dataList?.map((item) => ({
        id: item.yixiaoerId,
        text: item.yixiaoerName,
        raw: item.raw,
      }))
    },
    queryKey: ['locations', searchText, account.platform.name],
    enabled: !!searchText,
  })

  const eagerFormState = useEagerFormState()

  const { conclusion: accountConclusion } = useValidation(account, accountValidator)

  useEffect(() => {
    if (!account.isSessionValid()) {
      setOpen(false)
    }
  }, [account, account.sessionState])

  return (
    <div key={account.platform.name} className="w-[266px]">
      <span className="mb-1 text-sm text-gray">{account.platform.name}</span>
      <Popover
        open={open}
        onOpenChange={(open) => {
          setOpen(open && accountConclusion.valid)
        }}
      >
        <PopoverTrigger asChild>
          <div>
            <ButtonTrigger
              value={value?.text ?? ''}
              label="选择位置"
              onClean={() => onChange(null as T)}
              disabled={!accountConclusion.valid}
            />
          </div>
        </PopoverTrigger>
        <PopoverContent className="w-[266px] p-0" side="top">
          <Command shouldFilter={false}>
            <SearchInput
              placeholder="请输入关键字进行搜索"
              warpClass="h-9 p-1 pl-3"
              onSearch={setSearchText}
            ></SearchInput>
            <div className="h-[1px] bg-[#919eab39]"></div>
            <CommandList>
              {query.isLoading ? (
                <div className="flex py-6">
                  <LoadingContainer className="h-5 w-5" />
                </div>
              ) : (
                <CommandEmpty>没有找到位置</CommandEmpty>
              )}
              <CommandGroup>
                {query.data &&
                  query.data.map((item) => (
                    <CommandItem
                      // disabled={item.hasPendingOrder || item.hasVip}
                      key={item.id}
                      value={item.text}
                      className="flex w-full items-center justify-between gap-2"
                      onSelect={(currentValue) => {
                        onChange(query.data.find((item) => item.text === currentValue) as T)
                        setOpen(false)
                      }}
                    >
                      <span>{item.text}</span>
                      <CheckIcon
                        className={cn(
                          'ml-auto h-4 w-4',
                          value?.id === item.id ? 'opacity-100' : 'opacity-0',
                        )}
                      />
                    </CommandItem>
                  ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      <ValidationMessage
        subject={account}
        conclusion={accountConclusion}
        formState={eagerFormState}
      ></ValidationMessage>
    </div>
  )
}

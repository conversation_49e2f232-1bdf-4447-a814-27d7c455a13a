import { useMemo, useRef, useState } from 'react'
import { mediaService } from '@renderer/infrastructure/services'
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@renderer/shadcn-components/ui/dialog'
import type { ImageFileInfo, VideoFileInfo } from '@renderer/infrastructure/model'
import { Button } from '@renderer/shadcn-components/ui/button'
import { VisuallyHidden } from '@radix-ui/react-visually-hidden'
import { localPath2Url } from '@common/protocol'

export function CaptureVideo({
  visible,
  video,
  onCaptured,
  onClose,
}: {
  visible: boolean
  video: VideoFileInfo
  onCaptured: (image: ImageFileInfo) => void
  onClose: () => void
}) {
  const [seeking, setSeeking] = useState(false)
  const [currentSecond, setCurrentSecond] = useState(0)
  const videoRef = useRef<HTMLVideoElement>(null)

  async function captureFrame() {
    const cover = await mediaService.captureVideo(video, currentSecond)
    onCaptured(cover)
  }

  const url = useMemo(() => {
    return localPath2Url(video.filePath)
  }, [video.filePath])

  if (!visible) return null

  return (
    <Dialog open={visible} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>选择封面</DialogTitle>
          <VisuallyHidden>
            <DialogDescription />
          </VisuallyHidden>
        </DialogHeader>
        <div className="flex flex-col items-center px-4">
          <video
            ref={videoRef}
            className="max-h-[70vh] bg-black"
            src={url}
            controls
            onSeeking={() => setSeeking(true)}
            onSeeked={() => {
              setSeeking(false)
              if (videoRef.current) {
                console.log('seeked', videoRef.current.currentTime)
                setCurrentSecond(videoRef.current.currentTime)
              }
            }}
            onTimeUpdate={() => {
              if (videoRef.current) {
                setCurrentSecond(videoRef.current.currentTime)
              }
            }}
          />
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            取消
          </Button>
          <Button onClick={captureFrame} disabled={seeking}>
            确认选择
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default CaptureVideo

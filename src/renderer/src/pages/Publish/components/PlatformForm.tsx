import type { ImageFileInfo, Platform, VideoFileInfo } from '@renderer/infrastructure/model'
import { <PERSON><PERSON>, TabsContent } from '@renderer/components/Tabs'
import type { PlatformName } from '@common/model/platform-name'
import { platformNames } from '@common/model/platform-name'
import { DouYinPlatformForm } from './platform-forms/video/DouyinPlatformForm'
import { KuaiShouPlatformForm } from './platform-forms/video/KuaiShouPlatformForm'
import { XiaoHongShuPlatformForm } from './platform-forms/video/XiaoHongShuPlatformForm'
import { WeiXinShiPinHaoPlatformForm } from './platform-forms/video/WeiXinShiPinHaoPlatformForm'
import { XinLangWeiBoPlatformForm } from './platform-forms/video/XinLangWeiBoPlatformForm'
import { TengXunWeiShiPlatformForm } from './platform-forms/video/TengXunWeiShiPlatformForm'
import { ZhiHuPlatformForm } from './platform-forms/video/ZhiHuPlatformForm'
import { QiEHaoPlatformForm } from './platform-forms/video/QiEHaoPlatformForm'
import { SouHuHaoPlatformForm } from './platform-forms/video/SouHuHaoPlatformForm'
import { YiDianHaoPlatformForm } from './platform-forms/video/YiDianHaoPlatformForm'
import { WangYiHaoPlatformForm } from './platform-forms/video/WangYiHaoPlatformForm'
import { AiQiYiPlatformForm } from './platform-forms/video/AiQiYiPlatformForm'
import { BilibiliPlatformForm } from './platform-forms/video/BilibiliPlatformForm'
import { BaiJiaHaoPlatformForm } from './platform-forms/video/BaiJiaHaoPlatformForm'
import { TouTiaoHaoPlatformForm } from './platform-forms/video/TouTiaoHaoPlatformForm'
import { TiktokPlatformForm } from './platform-forms/video/TiktokPlatformForm'
import { YoutubePlatformForm } from './platform-forms/video/YoutubePlatformForm'
import { TwitterPlatformForm } from './platform-forms/video/TwitterPlatformForm'
import { FacebookPlatformForm } from './platform-forms/video/FacebookPlatformForm'
import { InstagramPlatformForm } from './platform-forms/video/InstagramPlatformForm'
import type {
  PlatformFormsViewModel,
  PlatformFormViewModel,
} from '@renderer/infrastructure/model/config/view-model/video'
import type { DraftFunction } from 'use-immer'
import type { ComponentType } from 'react'
import { useEffect, useMemo, useState } from 'react'
import { QuickFillSheet } from './QuickFillSheet'
import { ScrollablePlatformTabs } from './ScrollablePlatformTabs'
import type { VideoVisualHint } from '../visual-hint/video'

// 平台表单组件通用接口
interface PlatformFormProps<T extends PlatformFormViewModel> {
  model: T
  onChange: (updater: DraftFunction<T>) => void
  // 新增：视频信息，用于海外平台独立封面设置
  videoInfo?: {
    video: VideoFileInfo | null
    cover: ImageFileInfo | null
  }
}

const platformFormComponents: Partial<
  Record<PlatformName, ComponentType<PlatformFormProps<never>>>
> = {
  [platformNames.DouYin]: DouYinPlatformForm,
  [platformNames.KuaiShou]: KuaiShouPlatformForm,
  [platformNames.XiaoHongShu]: XiaoHongShuPlatformForm,
  [platformNames.WeiXinShiPinHao]: WeiXinShiPinHaoPlatformForm,
  [platformNames.XinLangWeiBo]: XinLangWeiBoPlatformForm,
  [platformNames.TengXunWeiShi]: TengXunWeiShiPlatformForm,
  [platformNames.ZhiHu]: ZhiHuPlatformForm,
  [platformNames.QiEHao]: QiEHaoPlatformForm,
  [platformNames.SouHuHao]: SouHuHaoPlatformForm,
  [platformNames.YiDianHao]: YiDianHaoPlatformForm,
  [platformNames.WangYiHao]: WangYiHaoPlatformForm,
  [platformNames.AiQiYi]: AiQiYiPlatformForm,
  [platformNames.BiliBili]: BilibiliPlatformForm,
  [platformNames.BaiJiaHao]: BaiJiaHaoPlatformForm,
  [platformNames.TouTiaoHao]: TouTiaoHaoPlatformForm,
  [platformNames.Tiktok]: TiktokPlatformForm,
  [platformNames.Youtube]: YoutubePlatformForm,
  [platformNames.Twitter]: TwitterPlatformForm,
  [platformNames.Facebook]: FacebookPlatformForm,
  [platformNames.Instagram]: InstagramPlatformForm,
}

export function PlatformForm({
  selectedPlatforms,
  visualHint,
  model: models,
  onChange,
  firstFileName,
  videoInfo,
}: {
  selectedPlatforms: Platform[]
  visualHint: VideoVisualHint
  model: PlatformFormsViewModel
  onChange: (draft: DraftFunction<PlatformFormsViewModel>) => void
  firstFileName?: string | null
  // 新增：视频信息，用于海外平台独立封面设置
  videoInfo?: {
    video: VideoFileInfo | null
    cover: ImageFileInfo | null
  }
}) {
  const [currentTab, setCurrentTab] = useState<string>(selectedPlatforms[0]?.name || '')
  // 确保currentTab始终在可选平台范围内
  useEffect(() => {
    const availablePlatformNames = selectedPlatforms.map((p) => p.name)
    if (
      !availablePlatformNames.includes(currentTab as PlatformName) &&
      availablePlatformNames.length > 0
    ) {
      setCurrentTab(availablePlatformNames[0] as string)
    }
  }, [selectedPlatforms, currentTab])

  // 为每个平台创建稳定的onChange函数，避免不必要的重新渲染
  // 现在onChange来自父组件的稳定回调，所以这个useMemo应该很少重新计算
  const platformOnChangeHandlers = useMemo(() => {
    const handlers: Record<string, (updater: DraftFunction<PlatformFormViewModel>) => void> = {}

    selectedPlatforms.forEach((platform) => {
      handlers[platform.name] = (updater) => {
        onChange((platformsDraft) => {
          updater(platformsDraft[platform.name])
        })
      }
    })

    return handlers
  }, [selectedPlatforms, onChange])

  const renderPlatformForm = (platform: Platform) => {
    const FormComponent = platformFormComponents[platform.name]

    if (!FormComponent) {
      return <div>不支持的平台{platform.name}</div>
    }

    return (
      <FormComponent
        model={models[platform.name]}
        onChange={platformOnChangeHandlers[platform.name]}
        videoInfo={videoInfo}
      />
    )
  }

  return (
    selectedPlatforms.length > 0 && (
      <div className="flex w-full">
        <div className="w-0 flex-grow rounded-lg bg-white px-4 pb-[36px] shadow-sm">
          <Tabs
            value={currentTab}
            onValueChange={setCurrentTab}
            className="flex flex-1 flex-col overflow-hidden"
          >
            <div className="flex items-center justify-between gap-5 border-b">
              <div className="flex items-center">
                <QuickFillSheet
                  selectedPlatforms={selectedPlatforms}
                  visualHint={visualHint}
                  firstFileName={firstFileName}
                />
              </div>
              <div className="h-4 w-[1px] border-r"></div>
              <div className="min-w-0 flex-1">
                <ScrollablePlatformTabs platforms={selectedPlatforms} value={currentTab} />
              </div>
            </div>

            {selectedPlatforms.map((x) => (
              <TabsContent key={x.name} value={x.name} className="flex-1 overflow-hidden">
                <div className="flex flex-col space-y-9">{renderPlatformForm(x)}</div>
              </TabsContent>
            ))}
          </Tabs>
        </div>
      </div>
    )
  )
}

import type { ImageFileInfo, VideoFileInfo } from '@renderer/infrastructure/model'
import { platforms } from '@renderer/infrastructure/model'
import { FormItem as PublishFormItem } from './FormItem'
import { useFormStateContext } from '@renderer/infrastructure/validation/use-form-state-context'
import { FormStateContext } from '../context/summary-context'
import { CoverSelector } from '@renderer/pages/Publish/components/cover-selector'

interface PlatformCoverSelectorProps {
  label: string
  video: VideoFileInfo | null
  cover: ImageFileInfo | null
  onChange: (cover: ImageFileInfo | null) => void
  onFrameTimeChange?: (frameTime: number) => void
  required?: boolean
  boxClassName?: string
  cropOnly?: boolean
  platformName?: string
}

/**
 * 平台独立封面选择器
 * 用于海外平台等需要独立设置封面的场景
 */
export function PlatformCoverSelector({
  label,
  video,
  cover,
  onChange,
  onFrameTimeChange,
  required = false,
  boxClassName,
  cropOnly = false,
  platformName,
}: PlatformCoverSelectorProps) {
  const formState = useFormStateContext(FormStateContext)
  return (
    <PublishFormItem label={label} required={required}>
      <CoverSelector
        video={video}
        cover={cover}
        onChange={(newCover) => {
          formState.setDirty(true)
          onChange(newCover)
        }}
        onFrameTimeChange={onFrameTimeChange}
        boxClassName={boxClassName}
        cropOnly={cropOnly}
      />

      {platformName &&
        (platformName === platforms.Tiktok.name || platformName === platforms.Twitter.name) && (
          <div className="text-xs text-gray-400">
            {platformName} 官方接口当前仅支持选取视频关键帧作为封面，无法自定义上传封面
          </div>
        )}
    </PublishFormItem>
  )
}

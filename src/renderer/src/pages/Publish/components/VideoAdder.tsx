import UploadIcon from '@renderer/assets/publish/upload.svg?react'
import type { VideoFileInfo } from '@renderer/infrastructure/model'

export function VideoAdder({
  className,
}: {
  selectSuperIds: string[]
  selectMediaIds: string[]
  onAdd: (videoInfo: VideoFileInfo[]) => void
  className: string
  multiple: boolean
}) {
  return (
    <div
      className={`flex flex-col ${className}`}
      onClick={() => {
        window.parent.postMessage(
          {
            type: 'onAddVideo',
          },
          '*',
        )
      }}
    >
      <div className="flex h-full w-full cursor-pointer flex-col items-center justify-center rounded-md bg-secondary">
        <div className="mb-2">
          <UploadIcon className={'h-4 w-4'} />
        </div>
        <div className="text-sm text-gray-800">请上传视频</div>
      </div>
    </div>
  )
}

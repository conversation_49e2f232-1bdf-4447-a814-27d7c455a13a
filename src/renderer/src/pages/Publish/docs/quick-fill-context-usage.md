# 一键填写 Context 使用指南

## 概述

新的一键填写功能使用 React Context 替代了之前的 eventBus 方案，提供了更好的类型安全性和调试体验。

## 设计原则

1. **广播模式**：一键填写不关心已选平台，直接广播给所有平台
2. **平台自治**：每个平台表单有自己的响应逻辑，决定如何处理填写数据
3. **类型安全**：使用 TypeScript 提供编译时类型检查

## 架构设计

### Context 结构

```typescript
interface QuickFillData {
  aContentConfig?: APlatformFormViewModel // A类平台内容配置
  bContentConfig?: BPlatformFormViewModel // B类平台内容配置
  type?: string // 类型：自制、转载等
  categories?: VideoCategories // 多平台分类
  scheduledTime?: TimeStamp // 定时发布
}

interface QuickFillContextValue {
  data: QuickFillData | null
  triggerFill: (data: QuickFillData) => void
}
```

### Provider 层级

QuickFillContextProvider 被集成在 `PublishInfrastructureProvider` 中，确保所有发布相关的组件都能访问到这个 Context。

## 使用方式

### 1. 触发一键填写（QuickFillSheet）

```typescript
import { useQuickFill } from '../hooks/use-quick-fill'

export function QuickFillSheet() {
  const { triggerFill } = useQuickFill()

  const handleApplyToAll = () => {
    // 广播给所有平台，不关心已选平台
    triggerFill({
      aContentConfig: quickFillData.aContentConfig,
      bContentConfig: quickFillData.bContentConfig,
      type: quickFillData.type || undefined,
      categories: quickFillData.categories,
      scheduledTime: quickFillData.scheduledTime || undefined,
    })
  }
}
```

### 2. 平台表单监听（每个平台自定义逻辑）

每个平台表单应该实现自己的响应逻辑：

```typescript
import { useQuickFill } from '@renderer/pages/Publish/hooks/use-quick-fill'
import { useEffect } from 'react'

export function WeiXinShiPinHaoPlatformForm({ model, onChange }: PlatformFormProps) {
  const formState = useFormState()
  const { data: quickFillData } = useQuickFill()

  useEffect(() => {
    if (quickFillData) {
      formState.setDirty(true)
      onChange((draft) => {
        // 微信视频号是A类平台，使用aContentConfig
        if (quickFillData.aContentConfig) {
          if (quickFillData.aContentConfig.title) {
            draft.title = quickFillData.aContentConfig.title
          }
          if (quickFillData.aContentConfig.description) {
            draft.description = quickFillData.aContentConfig.description
          }
        }
        // 微信视频号不处理 tags 字段
      })
    }
  }, [quickFillData, onChange, formState])
}
```

### 3. 其他平台示例

```typescript
// A类平台示例（如小红书）
export function XiaoHongShuPlatformForm({ model, onChange }: PlatformFormProps) {
  const { data: quickFillData } = useQuickFill()

  useEffect(() => {
    if (quickFillData) {
      formState.setDirty(true)
      onChange((draft) => {
        // 小红书是A类平台，使用aContentConfig
        if (quickFillData.aContentConfig) {
          if (quickFillData.aContentConfig.title) draft.title = quickFillData.aContentConfig.title
          if (quickFillData.aContentConfig.description) draft.description = quickFillData.aContentConfig.description
        }
        // 小红书不支持标签字段
      })
    }
  }, [quickFillData, onChange, formState])
}

// B类平台示例（如哔哩哔哩）
export function BilibiliPlatformForm({ model, onChange }: PlatformFormProps) {
  const { data: quickFillData } = useQuickFill()

  useEffect(() => {
    if (quickFillData) {
      formState.setDirty(true)
      onChange((draft) => {
        // 哔哩哔哩是B类平台，使用bContentConfig
        if (quickFillData.bContentConfig) {
          if (quickFillData.bContentConfig.title) draft.title = quickFillData.bContentConfig.title
          if (quickFillData.bContentConfig.description) draft.description = quickFillData.bContentConfig.description
          if (quickFillData.bContentConfig.tags && quickFillData.bContentConfig.tags.length > 0) {
            draft.tags = [...quickFillData.bContentConfig.tags] // 哔哩哔哩支持标签
          }
        }
        if (quickFillData.type) {
          // 处理类型字段映射
          if (quickFillData.type === '自制') {
            draft.type = 0
          } else if (quickFillData.type === '转载') {
            draft.type = 1
          }
        }
        if (quickFillData.scheduledTime) {
          draft.scheduledTime = quickFillData.scheduledTime
        }
      })
    }
  }, [quickFillData, onChange, formState])
}

// A类平台示例 - 字段名不同的平台
export function CustomAPlatformForm({ model, onChange }: PlatformFormProps) {
  const { data: quickFillData } = useQuickFill()

  useEffect(() => {
    if (quickFillData) {
      formState.setDirty(true)
      onChange((draft) => {
        // A类平台直接使用aContentConfig
        if (quickFillData.aContentConfig?.title) {
          draft.customTitleField = quickFillData.aContentConfig.title
        }
        if (quickFillData.aContentConfig?.description) {
          draft.content = quickFillData.aContentConfig.description
        }
        // A类平台不处理标签字段
      })
    }
  }, [quickFillData, onChange, formState])
}

// B类平台示例 - 字段名不同的平台
export function CustomBPlatformForm({ model, onChange }: PlatformFormProps) {
  const { data: quickFillData } = useQuickFill()

  useEffect(() => {
    if (quickFillData) {
      formState.setDirty(true)
      onChange((draft) => {
        // B类平台直接使用bContentConfig
        if (quickFillData.bContentConfig?.title) {
          draft.customTitleField = quickFillData.bContentConfig.title
        }
        if (quickFillData.bContentConfig?.description) {
          draft.content = quickFillData.bContentConfig.description
        }
        if (quickFillData.bContentConfig?.tags && quickFillData.bContentConfig.tags.length > 0) {
          draft.keywords = quickFillData.bContentConfig.tags.join(',')
        }
      })
    }
  }, [quickFillData, onChange, formState])
}
```

## 优势对比

### vs EventBus 方案

| 特性 | Context 方案 | EventBus 方案 |
|------|-------------|---------------|
| 类型安全 | ✅ 强类型检查 | ⚠️ 运行时类型检查 |
| 调试体验 | ✅ React DevTools 支持 | ❌ 难以追踪 |
| 性能 | ✅ 精确的依赖控制 | ⚠️ 可能的内存泄漏 |
| 代码可读性 | ✅ 清晰的数据流 | ⚠️ 隐式的事件关系 |
| React 生态 | ✅ 原生支持 | ❌ 外部依赖 |

## 注意事项

1. **依赖数组**：使用 `useEffect` 时，确保正确设置依赖数组，包含 `quickFillData`
2. **平台自治**：每个平台表单应该实现自己的响应逻辑，不要使用统一的处理方式
3. **平台类型自知**：
   - 每个平台表单自己知道自己是 A 类还是 B 类平台
   - A类平台直接使用 `quickFillData.aContentConfig`
   - B类平台直接使用 `quickFillData.bContentConfig`
   - 不需要外部判断或条件选择，平台内部直接访问对应字段
4. **字段映射**：不同平台可能有不同的字段名，在各自的 `useEffect` 中处理
5. **标签支持**：B类平台通常支持标签字段，A类平台通常不支持
6. **广播模式**：一键填写会广播给所有平台，平台自己决定是否响应
7. **类型字段**：`type` 字段（自制/转载）主要用于B类平台，需要进行数值映射

## 迁移指南

从 eventBus 方案迁移到 Context 方案：

1. 移除 eventBus 相关导入：`eventBus`, `quickFillEvent`
2. 导入 `useQuickFill` hook
3. 使用 `useEffect` + `useQuickFill` 替代 `eventBus.on`
4. 移除平台名称检查逻辑（不再需要 `targetPlatforms.includes(platformName)`）
5. 实现平台特定的字段处理逻辑
6. 测试功能是否正常工作

### 迁移示例

**之前（eventBus）：**
```typescript
useEffect(() => {
  return eventBus.on(quickFillEvent, (eventData) => {
    if (eventData.platforms.includes(platformNames.WeiXinShiPinHao)) {
      // 处理逻辑
    }
  })
}, [])
```

**现在（Context）：**
```typescript
const { data: quickFillData } = useQuickFill()

useEffect(() => {
  if (quickFillData) {
    // 直接处理逻辑，不需要平台名称检查
  }
}, [quickFillData])
```

import { features, type IAssetLibraryItem } from '@renderer/infrastructure/model'
import { useFeatureManager } from '@renderer/infrastructure/services'
import { DialogContent } from '@renderer/shadcn-components/dialog'
import { Button } from '@renderer/shadcn-components/ui/button'
import {
  DialogClose,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@renderer/shadcn-components/ui/dialog'
import { useInnerContextStore } from '@renderer/store/contextStore'
import { useEffect, useMemo, useRef } from 'react'
import { usePublishVideoDialogContext } from './usePublishVideoDialog'

const marketAgentIdMaps = {}

export function MarketAgent({
  selectMarketAgentIds,
  onClose,
  onChangeFile,
}: {
  selectMarketAgentIds: string[]
  onClose: React.Dispatch<React.SetStateAction<boolean>>
  onChangeFile: (file: IAssetLibraryItem[]) => void
}) {
  const { close } = usePublishVideoDialogContext()
  const { openFeature } = useFeatureManager()
  const currentTeam = useInnerContextStore((state) => state.currentTeam)

  const aToken = currentTeam?.components?.find((c) => c.name === 'marketAgent')?.componentArgs
    ?.token

  const marketAgentUrl = useMemo(() => {
    if (aToken) {
      const params = new URLSearchParams()

      // 添加防缓存参数 - 确保每次URL都不同
      params.set('_t', Date.now().toString()) // 时间戳
      // 或者使用随机数：params.set('_nc', Math.random().toString(36).substring(2, 11))

      return `${import.meta.env.VITE_MARKET_AGENT_URL}?${params.toString()}`
    }

    return null
  }, [aToken])

  const iframeRef = useRef<HTMLIFrameElement>(null)
  useEffect(() => {
    const handleMessage = async (event: { data: string }) => {
      try {
        const { type, ...rest } = JSON.parse(event.data)

        const ret: { id: string; fileName: string; filePath: string }[] = []

        if (rest.action === 'confirm') {
          ;(rest.data as Record<string, string>[]).forEach((item: Record<string, string>) => {
            if (!selectMarketAgentIds.includes(item.id)) {
              ret.push({
                id: item.id,
                fileName: item.name,
                filePath: item.file,
              })
            }
            marketAgentIdMaps[item.id] = item
          })

          onChangeFile(ret as IAssetLibraryItem[])
        }

        if (type === 'mounted') {
          if (
            selectMarketAgentIds.length > 0 &&
            iframeRef.current &&
            iframeRef.current.contentWindow
          ) {
            const selectData = new Set(selectMarketAgentIds)
            const data = [...selectData].map((id) => marketAgentIdMaps[id].id).filter(Boolean)

            iframeRef.current.contentWindow.postMessage(
              JSON.stringify({
                from: 'videoSelect',
                type: 'defaultVideos',
                data: data,
              }),
              '*',
            )
            console.log(
              '已发送',
              JSON.stringify({
                from: 'videoSelect',
                type: 'defaultVideos',
                data: data,
              }),
            )
          }

          if (iframeRef.current && iframeRef.current.contentWindow) {
            if (aToken) {
              iframeRef.current.contentWindow.postMessage(
                JSON.stringify({
                  from: 'videoSelect',
                  type: 'setToken',
                  token: aToken,
                }),
                '*',
              )
            }
          }

          return
        }

        if (type === 'close' && rest.action !== 'confirm') {
          onClose(false)
          return
        }
      } catch (error) {
        // console.log('error', error)
      }
    }

    // 添加事件监听器
    window.addEventListener('message', handleMessage)

    // 清理函数：组件卸载时移除事件监听器
    return () => {
      window.removeEventListener('message', handleMessage)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [onChangeFile, onClose])

  return marketAgentUrl ? (
    <div className="relative">
      <iframe ref={iframeRef} className="h-[90vh] w-[80vw]" src={marketAgentUrl} />
    </div>
  ) : (
    <DialogContent>
      <DialogHeader>
        <DialogTitle>AI营销智能体</DialogTitle>
        <DialogDescription></DialogDescription>
      </DialogHeader>
      <p>AI营销智能体 token 未设置</p>
      <DialogFooter>
        <DialogClose asChild>
          <Button type="button" variant="outline">
            取消
          </Button>
        </DialogClose>
        <Button
          type="button"
          onClick={() => {
            openFeature(features.团队管理, { tabsDefaultValue: 'plugins' })
            onClose(false)
            close()
          }}
          variant="default"
        >
          跳转设置
        </Button>
      </DialogFooter>
    </DialogContent>
  )
}

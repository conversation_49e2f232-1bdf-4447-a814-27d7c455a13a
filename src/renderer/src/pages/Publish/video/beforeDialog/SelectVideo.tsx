import { useCallback, useMemo, useRef, useState } from 'react'

import { CardContent } from '@renderer/shadcn-components/ui/card'
import Upload from '@renderer/assets/publish/cloud-upload.svg?react'
import { cn } from '@renderer/lib/utils'
import { Input } from '@renderer/shadcn-components/ui/input'
import { ScrollArea } from '@renderer/shadcn-components/ui/scroll-area'
import type { VideoFileInfo } from '@renderer/infrastructure/model'
import { VideoCard } from './VideoCard'
import { AddVideo } from './AddVideo'
import { useAssetLibrary } from '@renderer/hooks/useAssetLibrary'
import { filterValidFiles, fileToVideoFileInfo, videoAccept } from '@renderer/utils/file'
import LibraryIcon from '@renderer/assets/publish/menu/library.svg?react'
import { useDropzone } from 'react-dropzone'
// 超级编导
import SuperIcon from '@renderer/assets/team/super.png'
import MarketAgentIcon from '@renderer/assets/team/marketAgent.png'
import { Dialog, DialogContent, DialogTrigger } from '@renderer/shadcn-components/ui/dialog'
import { SuperDirector } from './SuperDirector'
import { useSystem } from '@renderer/pages/context'
import { useLocalFileService } from '@renderer/infrastructure/services/application-service/infrastructure-service/LocalFileService'
import { useCurrentTeam } from '@renderer/hooks/use-current-team'
import { useTeamService } from '@renderer/infrastructure/services'
import { useQuery } from '@tanstack/react-query'

// AI营销智能体
import { MarketAgent } from './MarketAgent'

// 最大值
const MAX_FILES = 20

const SelectVideo = ({
  setFiles,
  files,
}: {
  setFiles: (files: VideoFileInfo[]) => void
  files: VideoFileInfo[]
}) => {
  const team = useCurrentTeam()
  const teamService = useTeamService()
  const localFileService = useLocalFileService()
  const { onSetDialogSub } = useSystem()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [openSuper, setOpenSuper] = useState(false)
  const [openMarketAgent, setOpenMarketAgent] = useState(false)

  const { data: currentTeam } = useQuery({
    queryKey: ['getTeamDetail-plugins-select-video'],
    queryFn: () => {
      return teamService.getTeamDetail(team.id)
    },
    initialData: null,
  })

  const superdirEnabled = useMemo(() => {
    const superdir = currentTeam?.components?.find((c) => c.name === 'superdir')
    return !!superdir?.enabled
  }, [currentTeam])

  const marketAgentEnabled = useMemo(() => {
    const marketAgent = currentTeam?.components?.find((c) => c.name === 'marketAgent')
    return !!marketAgent?.enabled
  }, [currentTeam])

  // onAddVideo
  const onAddVideo = useCallback(
    (file: VideoFileInfo[]) => {
      setFiles([...files, ...file].slice(0, MAX_FILES))
    },
    [files, setFiles],
  )

  const { selectMultipleVideoAsset } = useAssetLibrary()

  async function selectFromAssetLibrary() {
    const videoInfo = await selectMultipleVideoAsset()
    if (!videoInfo) return
    onAddVideo(videoInfo)
  }

  // 处理文件拖放
  const onDrop = useCallback(
    async (files: File[] | null) => {
      if (files && files.length > 0) {
        const newfiles = filterValidFiles(files, { type: 'video' })
        const videos = (
          await Promise.all(newfiles.map((file) => fileToVideoFileInfo(file)))
        ).filter((x) => x) as VideoFileInfo[]
        onAddVideo(videos)
      }
    },
    [onAddVideo],
  )

  const { isDragAccept: isOver, getRootProps } = useDropzone({
    onDrop,
    accept: videoAccept,
    useFsAccessApi: false,
  })

  // 处理点击上传区域
  const handleAreaClick = () => {
    fileInputRef.current?.click()
  }

  // 处理文件选择
  const handleFileSelect = useCallback(
    async (e: React.ChangeEvent<HTMLInputElement>) => {
      const list = e.currentTarget.files
      if (list) {
        const newfiles = filterValidFiles(list, { type: 'video' })
        console.log(newfiles)
        const videos = (
          await Promise.all(newfiles.map((file) => fileToVideoFileInfo(file)))
        ).filter((x) => x) as VideoFileInfo[]
        console.log(videos)
        onAddVideo(videos)
      }
    },
    [onAddVideo],
  )

  const onChangeSuperFiles = useCallback(
    (files) => {
      onSetDialogSub((dialogMap) => ({
        ...dialogMap.assetLibraryDownload,
        elementProps: {
          list: files,
          async onDone(list) {
            const res = await Promise.all(
              list.map(async (item) => {
                const value = await localFileService.getVideoFileInfo(item.filePath)
                value.superId = item.id
                return value
              }),
            )
            onAddVideo(res)
          },
        },
        dialogContentProps: {
          onInteractOutside(event) {
            event.preventDefault()
          },
        },
      }))
    },
    [localFileService, onAddVideo, onSetDialogSub],
  )

  const onChangeMarketAgentFiles = useCallback(
    (files) => {
      onSetDialogSub((dialogMap) => ({
        ...dialogMap.assetLibraryDownload,
        elementProps: {
          list: files,
          async onDone(list) {
            const res = await Promise.all(
              list.map(async (item) => {
                const value = await localFileService.getVideoFileInfo(item.filePath)
                value.mediaId = item.id
                return value
              }),
            )
            setOpenMarketAgent(false)
            onAddVideo(res)
          },
        },
        dialogContentProps: {
          onInteractOutside(event) {
            event.preventDefault()
          },
        },
      }))
    },
    [localFileService, onAddVideo, onSetDialogSub],
  )

  return (
    <div className="flex flex-1">
      {/* Upload Area - 整个区域可点击 */}
      {!files.length ? (
        <div className="flex flex-1 flex-col items-center gap-4 px-7 pb-4 pt-2">
          <div
            className={cn(
              'group h-full w-full flex-1 cursor-pointer flex-col rounded-lg border-2 border-dashed bg-secondary transition-colors duration-200',
              { 'border-primary bg-muted': isOver },
            )}
            {...getRootProps()}
            onClick={handleAreaClick}
          >
            <CardContent className="flex h-full flex-col items-center justify-center gap-2">
              <>
                <Upload className="mb-3 h-16" />

                <p className="font-medium">点击上传或将文件拖入此区域</p>
                <p className="text-sm text-muted-foreground">支持视频格式: MP4、MOV、AVI、MKV</p>

                <Input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  className="hidden"
                  accept="video/mp4,video/quicktime,video/x-msvideo,video/x-matroska,.mp4,.mov,.avi,.mkv"
                  onChange={handleFileSelect}
                />
              </>
            </CardContent>
          </div>

          <div className="flex w-full items-center gap-4">
            <div
              className="flex h-[80px] w-full flex-1 cursor-pointer items-center justify-center gap-2 rounded-lg border-2 border-dashed bg-secondary"
              onClick={(e) => {
                e.stopPropagation() // 防止触发外层Card的点击事件
                void selectFromAssetLibrary()
              }}
            >
              <LibraryIcon />
              <span className="text-[14px] text-muted-foreground">蚁小二素材库</span>
            </div>

            {superdirEnabled && (
              <Dialog open={openSuper} onOpenChange={setOpenSuper}>
                <DialogTrigger asChild>
                  <div className="flex h-[80px] w-full flex-1 cursor-pointer items-center justify-center gap-2 rounded-lg border-2 border-dashed bg-secondary">
                    <img src={SuperIcon} alt="" className="h-[20px]" />
                    <span className="text-[14px] text-muted-foreground">超级编导素材库</span>
                  </div>
                </DialogTrigger>
                <DialogContent
                  className="super-dialog w-auto bg-white p-0"
                  style={{ maxWidth: 'none' }}
                >
                  <SuperDirector
                    onClose={setOpenSuper}
                    onChangeFile={onChangeSuperFiles}
                    selectSuperIds={files.map((item) => item.superId).filter(Boolean) as string[]}
                  />
                </DialogContent>
              </Dialog>
            )}

            {marketAgentEnabled && (
              <Dialog open={openMarketAgent} onOpenChange={setOpenMarketAgent}>
                <DialogTrigger asChild>
                  <div className="flex h-[80px] w-full flex-1 cursor-pointer items-center justify-center gap-2 rounded-lg border-2 border-dashed bg-secondary">
                    <img src={MarketAgentIcon} alt="" className="h-[20px]" />
                    <span className="text-[14px] text-muted-foreground">AI营销智能体素材库</span>
                  </div>
                </DialogTrigger>
                <DialogContent
                  className="super-dialog w-auto bg-white p-0"
                  style={{ maxWidth: 'none' }}
                >
                  <MarketAgent
                    onClose={setOpenMarketAgent}
                    onChangeFile={onChangeMarketAgentFiles}
                    selectMarketAgentIds={
                      files.map((item) => item.mediaId).filter(Boolean) as string[]
                    }
                  />
                </DialogContent>
              </Dialog>
            )}
          </div>
        </div>
      ) : (
        <ScrollArea className="flex-1 overflow-hidden">
          <div className="grid grid-cols-5 gap-4 px-6 py-2">
            {files.map((item, index) => (
              <VideoCard
                key={index}
                item={item}
                onRemove={() => setFiles(files.filter((x) => x !== item))}
              />
            ))}
            {files.length < MAX_FILES && (
              <div className="h-44">
                <AddVideo
                  mode="multiple"
                  onChange={onAddVideo}
                  selectSuperIds={files.map((x) => x.superId).filter(Boolean) as string[]}
                  selectMediaIds={files.map((x) => x.mediaId).filter(Boolean) as string[]}
                />
              </div>
            )}
          </div>
        </ScrollArea>
      )}
    </div>
  )
}

export default SelectVideo

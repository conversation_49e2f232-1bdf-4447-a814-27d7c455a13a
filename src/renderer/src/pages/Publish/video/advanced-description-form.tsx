import type { VideoVisualHint } from '@renderer/pages/Publish/visual-hint/video'
import type { BPlatformFormViewModel, APlatformFormViewModel } from '@renderer/infrastructure/model'
import type { DraftFunction } from 'use-immer'
import { useFormStateContext } from '@renderer/infrastructure/validation/use-form-state-context'
import { FormStateContext } from '@renderer/pages/Publish/context/summary-context'

import { useState } from 'react'
import { htmlService } from '@renderer/infrastructure/services'
import { FormItem as PublishFormItem } from '@renderer/pages/Publish/components/FormItem'
import { DescriptionEditor } from '@renderer/pages/Publish/components/DescriptionEditor'

import { TagsInput } from '../components/tags-input'
import {
  ATypeVideoPlatforms,
  BTypeVideoPlatforms,
} from '@renderer/pages/Publish/specification/content-type/supports'
import { Tabs, TabsList, TabsTrigger } from '@renderer/components/Tabs'
import { PublishInput } from '@renderer/components/PublishInput'
import { useNotify } from '@renderer/hooks/use-notify'

enum PlatformType {
  A = 'A',
  B = 'B',
}

export function AdvancedDescriptionForm({
  aContentConfig,
  bContentConfig,
  visualHint: { hasBTypeVideoPlatforms, hasATypeVideoPlatforms },
  aSetContentConfig,
  bSetContentConfig,
}: {
  visualHint: VideoVisualHint
  aContentConfig: APlatformFormViewModel
  bContentConfig: BPlatformFormViewModel
  bSetContentConfig: (func: DraftFunction<BPlatformFormViewModel>) => void
  aSetContentConfig: (func: DraftFunction<APlatformFormViewModel>) => void
}) {
  const [platformType, setPlatformType] = useState<string>(PlatformType.A)
  const formState = useFormStateContext(FormStateContext)

  const { notifyService } = useNotify()

  const needTabs = hasBTypeVideoPlatforms && hasATypeVideoPlatforms

  // 当不需要tabs时，根据平台类型确定显示的内容
  const currentPlatformType = needTabs
    ? platformType
    : hasBTypeVideoPlatforms
      ? PlatformType.B
      : PlatformType.A

  return (
    <div className="flex gap-2 p-5">
      <div className="flex grow">
        <div className="w-0 flex-grow overflow-hidden rounded-lg border bg-white pr-0">
          <div className="flex bg-[#F8F8FA]">
            <div className="flex grow items-center justify-between">
              <div className="h-[48px] gap-4 px-4">
                {needTabs ? (
                  <Tabs
                    value={platformType}
                    onValueChange={setPlatformType}
                    className="flex h-[48px] shrink-0 flex-col"
                  >
                    <TabsList className="border-none">
                      <TabsTrigger value={PlatformType.A}>A类平台</TabsTrigger>
                      <TabsTrigger value={PlatformType.B}>B类平台</TabsTrigger>
                    </TabsList>
                  </Tabs>
                ) : (
                  <div className="flex h-[48px] items-center">
                    <span className="text-sm font-medium">
                      {hasATypeVideoPlatforms && 'A类平台'}
                      {hasBTypeVideoPlatforms && 'B类平台'}
                    </span>
                  </div>
                )}
              </div>
            </div>
            <div className="flex items-center gap-2 pr-4">
              {currentPlatformType === PlatformType.B
                ? BTypeVideoPlatforms.map((platform, index) => (
                    <img key={index} src={platform.icon} alt="" className="h-5 w-5 rounded-full" />
                  ))
                : ATypeVideoPlatforms.map((platform, index) => (
                    <img key={index} src={platform.icon} alt="" className="h-5 w-5 rounded-full" />
                  ))}
            </div>
          </div>
          <div className="border-t border-gray-200"></div>
          {currentPlatformType === PlatformType.B && (
            <div className="space-y-9 px-4 pb-[36px] pt-[28px]">
              <PublishFormItem label="标题">
                <PublishInput
                  type="text"
                  value={bContentConfig.title}
                  onChange={(e) => {
                    formState.setDirty(true)
                    bSetContentConfig((x) => {
                      x.title = e.target.value
                    })
                  }}
                />
              </PublishFormItem>

              <PublishFormItem label="描述">
                <DescriptionEditor
                  description={bContentConfig.description}
                  onChange={(description) => {
                    formState.setDirty(true)
                    bSetContentConfig((x) => {
                      x.description = description
                    })
                  }}
                  isSupportTopic={false}
                />
              </PublishFormItem>

              <PublishFormItem label="标签">
                <TagsInput
                  value={bContentConfig.tags}
                  onChange={(tags) => {
                    formState.setDirty(true)
                    bSetContentConfig((x) => {
                      x.tags = tags
                      if (tags.length > x.tags.length) {
                        notifyService.error('不允许添加重复标签')
                      }
                    })
                  }}
                />
              </PublishFormItem>
            </div>
          )}
          {currentPlatformType === PlatformType.A && (
            <div className="space-y-9 px-4 pb-[36px] pt-[28px]">
              <PublishFormItem label="标题">
                <PublishInput
                  type="text"
                  value={aContentConfig.title}
                  onChange={(e) => {
                    formState.setDirty(true)
                    aSetContentConfig((x) => {
                      x.title = e.target.value
                    })
                  }}
                />
              </PublishFormItem>

              <PublishFormItem label="描述">
                <DescriptionEditor
                  onSyncB={() => {
                    bSetContentConfig((x) => {
                      x.title = aContentConfig.title
                      // 去除topics后同步到B类平台的description
                      x.description = htmlService.trimTopicsFromDescription(
                        aContentConfig.description,
                      )
                      x.tags = htmlService.getTopics(aContentConfig.description)
                    })
                  }}
                  description={aContentConfig.description}
                  onChange={(description) => {
                    formState.setDirty(true)
                    aSetContentConfig((x) => {
                      x.description = description
                    })
                  }}
                />
              </PublishFormItem>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

import { createContext } from 'react'

// 一键填写 Context

import type { TimeStamp } from '@renderer/infrastructure/types/brand'
import type {
  VideoCategories,
  APlatformFormViewModel,
  BPlatformFormViewModel,
} from '@renderer/infrastructure/model'

export interface QuickFillData {
  aContentConfig?: APlatformFormViewModel // A类平台内容配置
  bContentConfig?: BPlatformFormViewModel // B类平台内容配置
  categories?: VideoCategories // 多平台分类
  scheduledTime?: TimeStamp // 定时发布
}

export interface QuickFillContextValue {
  data: QuickFillData | null
  triggerFill: (data: QuickFillData) => void
}

export const QuickFillContext = createContext<QuickFillContextValue | null>(null)

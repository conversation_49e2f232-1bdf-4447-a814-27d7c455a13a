import type { ImageFileInfo, Platform } from '@renderer/infrastructure/model'
import { usePlatformSpecification } from '@renderer/pages/Publish/specification'
import { useMemo } from 'react'
import { Validator } from '@renderer/infrastructure/validation/validator'
import type { PublishRuleResultExtra } from '@renderer/pages/Publish/validation/types/publish-rule-result-extra'
import { RuleResult } from '@renderer/infrastructure/validation/rule'
import { datetimeService, htmlService } from '@renderer/infrastructure/services'
import { TimeSpan } from '@renderer/infrastructure/model/utils/time-span'

export function useTitleValidator(platforms: Platform[]) {
  const platformSpecification = usePlatformSpecification()

  return useMemo(() => {
    const validator = Validator.of<string, PublishRuleResultExtra>()
    for (const platform of platforms) {
      const extra = {
        platform: platform,
        fieldName: '标题',
      } satisfies PublishRuleResultExtra
      const specification = platformSpecification.getImageTextSpecification(platform)
      if (!specification) continue
      if (specification.titleRequired) {
        validator.addRule((subject: string) => {
          if (!subject) {
            return new RuleResult('invalid', '不可为空', extra)
          }
          return RuleResult.newValid(extra)
        })
      }
      if (specification.titleMaxLength !== null) {
        const titleMaxLength = specification.titleMaxLength
        validator.addRule((subject: string) => {
          if (subject.length > titleMaxLength) {
            return new RuleResult('invalid', `不可超过${titleMaxLength}个字`, extra)
          }
          return RuleResult.newValid(extra)
        })
      }
    }
    return validator
  }, [platforms, platformSpecification])
}

export function useDescriptionValidator(platforms: Platform[]) {
  const platformSpecification = usePlatformSpecification()

  return useMemo(() => {
    const validator = Validator.of<string, PublishRuleResultExtra>()
    for (const platform of platforms) {
      const extra = {
        platform: platform,
        fieldName: '描述',
      } satisfies PublishRuleResultExtra
      const specification = platformSpecification.getImageTextSpecification(platform)
      if (!specification) continue
      if (specification.descriptionRequired) {
        validator.addRule((subject: string) => {
          if (!subject) {
            return new RuleResult('invalid', '不可为空', extra)
          }
          return RuleResult.newValid(extra)
        })
      }
      if (specification.descriptionMaxLength !== null) {
        const descriptionMaxLength = specification.descriptionMaxLength
        validator.addRule((subject: string) => {
          if (htmlService.extractTextFromHtml(subject).length > descriptionMaxLength) {
            return new RuleResult('invalid', `仅支持${descriptionMaxLength}个字符`, extra)
          }
          return RuleResult.newValid(extra)
        })
      }
    }
    return validator
  }, [platforms, platformSpecification])
}

export function useTopicValidator(platforms: Platform[]) {
  const platformSpecification = usePlatformSpecification()

  return useMemo(() => {
    const validator = Validator.of<string[], PublishRuleResultExtra>()
    for (const platform of platforms) {
      const extra = {
        platform: platform,
        fieldName: '话题',
      } satisfies PublishRuleResultExtra
      const specification = platformSpecification.getImageTextSpecification(platform)
      if (!specification) continue
      if (specification.topicMaxCount !== null) {
        const topicMaxCount = specification.topicMaxCount
        validator.addRule((topics: string[]) => {
          if (topics.length > topicMaxCount) {
            return new RuleResult('warning', `仅生效前${topicMaxCount}个话题`, extra)
          }
          return RuleResult.newValid(extra)
        })
      }
      if (specification.topicMaxLength !== null) {
        const topicMaxLength = specification.topicMaxLength
        validator.addRule((topics: string[]) => {
          if (topics.some((topic) => topic.length > topicMaxLength))
            return new RuleResult('invalid', `单个话题仅支持${topicMaxLength}字`, extra)
          return RuleResult.newValid(extra)
        })
      }
    }
    return validator
  }, [platforms, platformSpecification])
}

export function useImageValidator(platforms: Platform[]) {
  const platformSpecification = usePlatformSpecification()

  return useMemo(() => {
    const validator = Validator.of<ImageFileInfo | null, PublishRuleResultExtra>()
    for (const platform of platforms) {
      const extra = {
        platform: platform,
        fieldName: '视频',
      } satisfies PublishRuleResultExtra
      const specification = platformSpecification.getImageTextSpecification(platform)
      if (!specification) continue
      if (specification.imageMaxByteSize !== null) {
        const imageMaxByteSize = specification.imageMaxByteSize
        validator.addRule((subject: ImageFileInfo | null) => {
          if (subject && subject.byteSize > imageMaxByteSize) {
            return new RuleResult('invalid', `大小不能超过${imageMaxByteSize.toString()}`, extra)
          }
          return RuleResult.newValid(extra)
        })
      }
    }
    return validator
  }, [platforms, platformSpecification])
}

export function useImagesValidator(platforms: Platform[]) {
  const platformSpecification = usePlatformSpecification()

  return useMemo(() => {
    const validator = Validator.of<ImageFileInfo[], PublishRuleResultExtra>()
    for (const platform of platforms) {
      const extra = {
        platform: platform,
        fieldName: '图片',
      } satisfies PublishRuleResultExtra
      const specification = platformSpecification.getImageTextSpecification(platform)
      if (!specification) continue
      if (specification.imageRequired) {
        validator.addRule((subject: ImageFileInfo[]) => {
          if (subject.length === 0) {
            return new RuleResult('invalid', '至少选择一张图片', extra)
          }
          return RuleResult.newValid(extra)
        })
      }
      // 图片数量限制
      if (specification.imageMaxCount !== null) {
        const imageMaxCount = specification.imageMaxCount
        validator.addRule((subject: ImageFileInfo[]) => {
          if (subject.length > imageMaxCount) {
            return new RuleResult('warning', `仅生效前${imageMaxCount}张`, extra)
          }
          return RuleResult.newValid(extra)
        })
      }
      if (specification.imageMaxByteSize !== null) {
        const imageMaxByteSize = specification.imageMaxByteSize
        validator.addRule((subject: ImageFileInfo[]) => {
          if (subject.some((image) => image && image.byteSize > imageMaxByteSize)) {
            return new RuleResult('invalid', `大小不能超过${imageMaxByteSize.toString()}`, extra)
          }
          return RuleResult.newValid(extra)
        })
      }
    }
    return validator
  }, [platforms, platformSpecification])
}

export function useCoverValidator(platforms: Platform[]) {
  const platformSpecification = usePlatformSpecification()

  return useMemo(() => {
    const validator = Validator.of<ImageFileInfo | null, PublishRuleResultExtra>()
    for (const platform of platforms) {
      const extra = {
        platform: platform,
        fieldName: '封面',
      } satisfies PublishRuleResultExtra
      const specification = platformSpecification.getImageTextSpecification(platform)
      if (!specification) continue
      if (specification.coverRequired) {
        validator.addRule((subject: ImageFileInfo | null) => {
          if (!(subject && subject.path)) {
            return new RuleResult('invalid', '不可为空', extra)
          }
          return RuleResult.newValid(extra)
        })
      }
    }
    return validator
  }, [platforms, platformSpecification])
}

export function useScheduledTimeValidator(platforms: Platform[]) {
  const platformSpecification = usePlatformSpecification()

  return useMemo(() => {
    const validator = Validator.of<number | undefined, PublishRuleResultExtra>()
    for (const platform of platforms) {
      const extra = {
        platform: platform,
        fieldName: '定时发布时间',
      } satisfies PublishRuleResultExtra
      const specification = platformSpecification.getImageTextSpecification(platform)
      if (!specification) continue
      if (specification.scheduledTimeMinTimeSpan !== null) {
        let minTimeSpan = specification.scheduledTimeMinTimeSpan
        const protectTimeSpan = TimeSpan.fromMinutes(10)
        minTimeSpan = minTimeSpan.add(protectTimeSpan)
        validator.addRule((subject: number | undefined) => {
          if (subject && subject < datetimeService.timeSpanLater(minTimeSpan).getTime()) {
            return new RuleResult('invalid', `不能小于当前时间${minTimeSpan.toString()}`, extra)
          }
          return RuleResult.newValid(extra)
        })
      }
      if (specification.scheduledTimeMaxTimeSpan !== null) {
        const maxTimeSpan = specification.scheduledTimeMaxTimeSpan
        validator.addRule((subject: number | undefined) => {
          if (subject && subject > datetimeService.timeSpanLater(maxTimeSpan).getTime()) {
            return new RuleResult('invalid', `不得超过${maxTimeSpan.days}天`, extra)
          }
          return RuleResult.newValid(extra)
        })
      }
    }
    return validator
  }, [platforms, platformSpecification])
}

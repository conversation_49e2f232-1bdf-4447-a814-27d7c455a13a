import { defaultSpecification } from '../default'
import { type VideoSpecification } from '../video-specification'
import { ByteSize } from '@renderer/infrastructure/model/utils/byte-size'
import { TimeSpan } from '@renderer/infrastructure/model/utils/time-span'

export default {
  ...defaultSpecification,
  platformType: 'A',
  videoMaxByteSize: ByteSize.fromMB(512),
  videoMaxDuration: TimeSpan.fromSeconds(140),
} satisfies VideoSpecification as VideoSpecification

import { use<PERSON><PERSON>back, useContext, useEffect, useMemo, useRef, useState } from 'react'
import type { ImageFileInfo, SpiderAccount, VideoFileInfo } from '@renderer/infrastructure/model'
import {
  MultipleVideoAccountViewModel,
  MultipleVideoAccountViewModelItem,
  SingleVideoAccountViewModel,
  VideoContentViewModel,
  WechatShiPinHao3rdPartySubAccount,
} from '@renderer/infrastructure/model'
import {
  htmlService,
  useFeatureManager,
  usePushingService,
} from '@renderer/infrastructure/services'
import { useNotify } from '@renderer/hooks/use-notify'
import { SingleVideoAccount } from '@renderer/pages/Publish/components/SingleVideoAccount'
import { useValidation } from '@renderer/hooks/validation/validation'
import { useRpaService } from '@renderer/infrastructure/services/application-service/rpa-service'
import { useImmer } from 'use-immer'
import {
  AccountSummaryContext,
  CommonSummaryContext,
  FormStateContext,
  PlatformSummaryContext,
} from '@renderer/pages/Publish/context/summary-context'
import { MultipleVideoAccount } from '@renderer/pages/Publish/components/MultipleVideoAccount'

import { FormItem, FormItem as PublishFormItem } from '@renderer/pages/Publish/components/FormItem'
import { HelpTooltop } from '@renderer/components/helpTooltop'
import { DateTimePicker } from '@renderer/components/DateTimePicker'
import { VideoCategory } from '@renderer/pages/Publish/components/VideoCategory'
import { CountExhaustDialog } from '@renderer/components/vip/CountExhaustDialog'
import { useVideoVisualHint } from '@renderer/pages/Publish/visual-hint/video'
import {
  useCategoryValidator,
  useDescriptionValidator,
  useScheduledTimeValidator,
  useTagValidator,
  useTitleValidator,
  useTopicValidator,
} from '@renderer/pages/Publish/validation/useVideoContentValidation'
import { useLocalFileService } from '@renderer/infrastructure/services/application-service/infrastructure-service/LocalFileService'
import { AdvancedDescriptionForm } from '@renderer/pages/Publish/video/advanced-description-form'
import { RadioGroup, RadioGroupItem } from '@renderer/shadcn-components/radio-group'
import { Label } from '@renderer/shadcn-components/ui/label'
import { features } from '@renderer/infrastructure/model/features/features'
import { FeatureInstanceContext } from '@renderer/pages/IndexPage/components/feature-instance-context'
import { FrequencyUsedCategoryGroup } from '@renderer/pages/Publish/components/frequency-used-category-group'
import { ScrollArea } from '@renderer/shadcn-components/ui/scroll-area'
import { PositionSelector } from './components/positionSelect.tsx'
import { useFeatureStore } from '@renderer/store/feature-store'
import { VideoForm } from './video/video-form'
import { sort } from '@renderer/utils/array'
import { BaseDescriptionForm } from './video/base-description-form'
import { PublishHeaderBase } from '@renderer/pages/Publish/PublishHeaderBase'
import { PublishNowHoverCard } from '@renderer/pages/Publish/components/publish-now-hover-card'
import { usePublishValidationPassed } from '@renderer/pages/Publish/hooks/use-publish-validation-passed'
import { useSessionCheckContext } from '@renderer/components/session-check/context/session-check-context'
import { PublishInfrastructureProvider } from './components/publish-infrastructure-provider'
import { eventBus } from '@renderer/infrastructure/event-bus/buses'
import { authorizeEvents } from '@renderer/infrastructure/event-bus/business-events'
import { useScenarioContext, type AccountsWithTokens } from '@renderer/context/scenario-context'
import { useEffectEvent } from 'use-effect-event'
import { useFormStateContext } from '@renderer/infrastructure/validation/use-form-state-context'
import { useSummaryContext } from '@renderer/infrastructure/validation/use-summary-context'
import { Validator } from '@renderer/infrastructure/validation/validator'
import { RuleResult } from '@renderer/infrastructure/validation/rule'
import type { AccountRuleResultExtra } from './validation/types/publish-rule-result-extra'
import { LoadingModalDialog } from '@renderer/components/LoadingDialog'
import { BusinessError } from '@renderer/infrastructure/model/error/businessError'
import type { AccountForm, VideoTaskSetForm } from '@renderer/infrastructure/types/cloud-publish'
import { PlatformRepresentativeAccountContextProvider } from '@renderer/context/platform-representative-account-context.js'
import { platformNames } from '@common/model/platform-name.js'
import { nanoid } from 'nanoid'
import { Checkbox } from '@renderer/shadcn-components/ui/checkbox.js'
import { isElectron } from '@common/isElectron.js'

type VideoPublishFromProps = {
  videos?: VideoFileInfo[]
  accountsWithTokens?: AccountsWithTokens
  formData?: {
    commonForm: VideoTaskSetForm
    accountForms: {
      accountId: string
      formData: AccountForm
    }[]
  }
}

function VideoPublishFrom({ formData, videos, accountsWithTokens }: VideoPublishFromProps) {
  const published = useRef(false)

  const { setAccounts, releaseAllAccount } = useScenarioContext()

  const { isSingle, initSingleModel, initMultipleModel } = useMemo(() => {
    const isSingle = (videos ?? []).length < 2

    const initSingleModel = new SingleVideoAccountViewModel()
    const initMultipleModel = new MultipleVideoAccountViewModel()

    const accounts = accountsWithTokens?.accounts as SpiderAccount[] | undefined

    if (accounts && videos) {
      if (isSingle) {
        initSingleModel.video = videos[0]
        initSingleModel.accounts = accounts as SpiderAccount[]
      } else {
        const items = Math.max(accounts.length, videos.length)
        const list: MultipleVideoAccountViewModelItem[] = []
        for (let i = 0; i < items; i++) {
          list.push(new MultipleVideoAccountViewModelItem(videos[i], null, accounts[i]))
        }
        initMultipleModel.items = list
      }
    }

    return {
      isSingle,
      initSingleModel,
      initMultipleModel,
    }
  }, [videos, accountsWithTokens?.accounts])

  const {
    startSuperLock,
    startSuperOccupy,
    startLocalTask,
    reportBrowserTask,
    generatePushingConfigsForSingleVideo,
    generatePushingConfigsForMultipleVideo,
    publishCloudForSingleVideo,
    publishCloudForMultipleVideo,
  } = usePushingService()
  const featureInstance = useContext(FeatureInstanceContext)

  const localFileService = useLocalFileService()

  const { openFeature, removeInstance } = useFeatureManager()
  const updateInstance = useFeatureStore((state) => state.updateInstance)

  const { check: sessionCheck } = useSessionCheckContext()

  const { notifyService } = useNotify()
  const [toDraft, setToDraft] = useState(false)

  const [contentConfig, setContentConfig] = useImmer<VideoContentViewModel>(
    new VideoContentViewModel(),
  )

  const [singleConfig, setSingleConfig] = useImmer(initSingleModel)
  const [multipleConfig, setMultipleConfig] = useImmer(initMultipleModel)

  const [mode, setMode] = useState<'single' | 'multiple'>(isSingle ? 'single' : 'multiple')

  const { singleVideoPublish, multipleVideoPublish } = useRpaService()

  const selectedAccounts = useMemo(() => {
    if (mode === 'single') {
      return singleConfig.accounts
    } else {
      return multipleConfig.items.map((x) => x.account).filter((x) => x !== null)
    }
  }, [mode, multipleConfig.items, singleConfig.accounts])

  const selectedPlatforms = useMemo(() => {
    return Array.from(new Set(selectedAccounts.map((x) => x!.platform))).sort(
      sort.by((x) => x.displayOrder),
    )
  }, [selectedAccounts])

  const [advancedMode, setAdvancedMode] = useState(false)

  const visualHint = useVideoVisualHint(selectedPlatforms)

  // region 验证

  const formState = useFormStateContext(FormStateContext)

  const commonSummary = useSummaryContext(CommonSummaryContext)
  const platformSummary = useSummaryContext(PlatformSummaryContext)

  const { browserValidationPassed, spiderValidationPassed, cloudValidationPassed } =
    usePublishValidationPassed()

  useValidation(contentConfig.categories, useCategoryValidator(selectedPlatforms), platformSummary)

  useValidation(contentConfig.timing, useScheduledTimeValidator(selectedPlatforms), platformSummary)

  useValidation(
    useMemo(
      () => htmlService.getDescriptionPureText(contentConfig.aPlatformForm.description),
      [contentConfig.aPlatformForm.description],
    ),
    useDescriptionValidator(selectedPlatforms, true),
    platformSummary,
  )
  useValidation(
    useMemo(
      () => htmlService.getTopics(contentConfig.aPlatformForm.description),
      [contentConfig.aPlatformForm.description],
    ),
    useTopicValidator(selectedPlatforms),
    platformSummary,
  )

  useValidation(
    contentConfig.aPlatformForm.title,
    useTitleValidator(selectedPlatforms, true),
    platformSummary,
  )

  useValidation(
    useMemo(
      () => htmlService.getDescriptionPureText(contentConfig.bPlatformForm.description),
      [contentConfig.bPlatformForm.description],
    ),
    useDescriptionValidator(selectedPlatforms, false),
    platformSummary,
  )

  useValidation(
    contentConfig.bPlatformForm.title,
    useTitleValidator(selectedPlatforms, false),
    platformSummary,
  )

  useValidation(
    contentConfig.bPlatformForm.tags,
    useTagValidator(selectedPlatforms),
    platformSummary,
  )

  // endregion

  const [pending, setPending] = useState(false)

  const [countFull, setCountFull] = useState(false)

  const publishBrowser = useCallback(async () => {
    formState.setDirty(true)
    if (!browserValidationPassed) {
      notifyService.error('请检查您的输入')
      console.debug(commonSummary, platformSummary)
      return
    }

    const validAccounts =
      mode === 'single' ? singleConfig.accounts : multipleConfig.items.map((x) => x.account!)

    // 如果同一父账号下有多个子账号，提示用户无法发布
    const parentAccountIds = new Set<string>()
    for (const account of validAccounts.filter(
      (x) => x instanceof WechatShiPinHao3rdPartySubAccount,
    )) {
      if (parentAccountIds.has(account.parentAccountId)) {
        notifyService.error('无法同时发布同一微信账号下的多个视频号')
        return
      }
      parentAccountIds.add(account.parentAccountId)
    }

    if (mode === 'single') {
      const pushingConfigs = await generatePushingConfigsForSingleVideo(
        singleConfig,
        contentConfig,
        validAccounts,
        toDraft,
      )

      await singleVideoPublish(pushingConfigs, singleConfig)
    } else {
      const pushingConfigs = await generatePushingConfigsForMultipleVideo(
        multipleConfig,
        contentConfig,
        validAccounts,
        toDraft,
      )

      await multipleVideoPublish(pushingConfigs, multipleConfig)
    }
    await reportBrowserTask()
  }, [
    browserValidationPassed,
    commonSummary,
    contentConfig,
    formState,
    generatePushingConfigsForMultipleVideo,
    generatePushingConfigsForSingleVideo,
    mode,
    multipleConfig,
    multipleVideoPublish,
    notifyService,
    platformSummary,
    reportBrowserTask,
    singleConfig,
    singleVideoPublish,
    toDraft,
  ])

  const publishCloud = useCallback(async () => {
    formState.setDirty(true)
    if (!cloudValidationPassed) {
      notifyService.error('请检查您的输入')
      console.debug(commonSummary, platformSummary)
      return
    }

    try {
      setPending(true)
      const lockId = nanoid()
      const videoIds = new Set<string>()
      if (mode === 'single') {
        if (
          singleConfig.accounts.filter((x) => x.platform.name === platformNames.DouYin).length &&
          singleConfig.video?.superId
        ) {
          singleConfig.video.superLockId = lockId
          videoIds.add(singleConfig.video.superId)
        }

        if (videoIds.size) {
          await startSuperOccupy({ lockId, videoIds: [...videoIds] })
        }

        const taskSetId = await publishCloudForSingleVideo(
          singleConfig,
          contentConfig,
          toDraft,
          'cloud',
        )

        if (videoIds.size) {
          await startSuperLock({ lockId, activityId: taskSetId })
        }
      } else {
        for (let i = 0; i < multipleConfig.items.length; i++) {
          if (
            multipleConfig.items[i].video?.superId &&
            multipleConfig.items[i].account?.platform.name === platformNames.DouYin
          ) {
            multipleConfig.items[i].video!.superLockId = lockId
            videoIds.add(multipleConfig.items[i].video!.superId!)
          }
        }

        if (videoIds.size) {
          await startSuperOccupy({ lockId, videoIds: [...videoIds] })
        }

        const taskSetId = await publishCloudForMultipleVideo(
          multipleConfig,
          contentConfig,
          toDraft,
          'cloud',
        )

        if (videoIds.size) {
          await startSuperLock({ lockId, activityId: taskSetId })
        }
      }

      published.current = true

      if (featureInstance !== null) removeInstance(featureInstance)
      openFeature(features.发布)
    } catch (e) {
      console.error(e)
      if (!(e instanceof BusinessError)) {
        notifyService.error(`启动发布异常：${e instanceof Error ? e.message : JSON.stringify(e)}`)
      }
    } finally {
      setPending(false)
    }
  }, [
    cloudValidationPassed,
    commonSummary,
    contentConfig,
    featureInstance,
    formState,
    mode,
    multipleConfig,
    notifyService,
    openFeature,
    platformSummary,
    publishCloudForMultipleVideo,
    publishCloudForSingleVideo,
    removeInstance,
    singleConfig,
    startSuperLock,
    startSuperOccupy,
    toDraft,
  ])

  const publishNow = useCallback(async () => {
    formState.setDirty(true)
    if (!spiderValidationPassed) {
      notifyService.error('请检查您的输入')
      console.debug(commonSummary, platformSummary)
      return
    }

    const lockId = nanoid()
    const videoIds = new Set<string>()

    try {
      setPending(true)
      let taskSetId = ''

      if (mode === 'single') {
        if (
          singleConfig.accounts.filter((x) => x.platform.name === platformNames.DouYin).length &&
          singleConfig.video?.superId
        ) {
          singleConfig.video.superLockId = lockId
          videoIds.add(singleConfig.video.superId)
        }

        taskSetId = await publishCloudForSingleVideo(singleConfig, contentConfig, toDraft, 'local')
      } else {
        for (let i = 0; i < multipleConfig.items.length; i++) {
          if (
            multipleConfig.items[i].video?.superId &&
            multipleConfig.items[i].account?.platform.name === platformNames.DouYin
          ) {
            multipleConfig.items[i].video!.superLockId = lockId
            videoIds.add(multipleConfig.items[i].video!.superId!)
          }
        }

        taskSetId = await publishCloudForMultipleVideo(
          multipleConfig,
          contentConfig,
          toDraft,
          'local',
        )
      }
      if (videoIds.size) {
        await startSuperOccupy({ lockId, videoIds: [...videoIds] })
      }

      await startLocalTask(taskSetId)

      if (videoIds.size) {
        await startSuperLock({ lockId, activityId: taskSetId })
      }

      published.current = true

      if (featureInstance !== null) removeInstance(featureInstance)
      openFeature(features.发布)
    } catch (e) {
      console.error(e)
      if (!(e instanceof BusinessError)) {
        notifyService.error(`启动发布异常：${e instanceof Error ? e.message : JSON.stringify(e)}`)
      }
    } finally {
      setPending(false)
    }
  }, [
    formState,
    spiderValidationPassed,
    notifyService,
    commonSummary,
    platformSummary,
    mode,
    startSuperOccupy,
    startLocalTask,
    startSuperLock,
    featureInstance,
    removeInstance,
    openFeature,
    singleConfig,
    publishCloudForSingleVideo,
    contentConfig,
    toDraft,
    multipleConfig,
    publishCloudForMultipleVideo,
  ])

  function resetCountFull(open: boolean) {
    setCountFull(open)
  }

  // 多个视频时，只有一个视频时，自动切换到单视频模式
  useEffect(() => {
    if (mode === 'multiple' && multipleConfig.items.length <= 1) {
      setMode('single')
      setSingleConfig((draft) => {
        draft.video = multipleConfig.items[0]?.video ?? null
        draft.cover = multipleConfig.items[0]?.cover ?? null
        draft.accounts = multipleConfig.items.map((item) => item.account).filter((x) => x !== null)
      })
    }
  }, [mode, multipleConfig.items, setSingleConfig])

  useEffect(() => {
    if (featureInstance) {
      let title: string | null = null
      if (mode === 'single' && singleConfig.video) {
        title = singleConfig.video.fileName
      } else if (multipleConfig.items.length > 0) {
        const firstVideo = multipleConfig.items.find((x) => x.video)
        if (firstVideo && firstVideo.video) {
          title = `${firstVideo.video.fileName}等${multipleConfig.items.length - 1}个`
        }
      }
      featureInstance.name = title
      updateInstance(featureInstance)
    }
  }, [featureInstance, mode, multipleConfig.items, singleConfig.video, updateInstance])

  const initFormTitle = useEffectEvent((title: string | null) => {
    if (title) {
      if (!contentConfig.aPlatformForm.title) {
        setContentConfig((x) => {
          x.aPlatformForm.title = title ?? ''
        })
      }
      if (!contentConfig.bPlatformForm.title) {
        setContentConfig((x) => {
          x.bPlatformForm.title = title ?? ''
        })
      }
    }
  })

  useEffect(() => {
    let firstFileName: string | null = null
    if (mode === 'single' && singleConfig.video) {
      firstFileName = singleConfig.video.fileName
    } else if (multipleConfig.items.length > 0) {
      const firstVideo = multipleConfig.items.find((x) => x.video)
      if (firstVideo && firstVideo.video) {
        firstFileName = firstVideo.video.fileName
      }
    }
    if (firstFileName) {
      const lastDotIndex = firstFileName.lastIndexOf('.')
      firstFileName = firstFileName.slice(0, lastDotIndex)
    }
    initFormTitle(firstFileName)
  }, [initFormTitle, mode, multipleConfig.items, singleConfig.video])

  // region 重新发布

  const refillSingleConfig = useEffectEvent(async () => {
    if (!formData || !accountsWithTokens) return
    const errors: string[] = []
    const accounts: SpiderAccount[] = accountsWithTokens.accounts

    const firstConfig = formData.accountForms[0].formData

    let video: VideoFileInfo | null = null
    if (firstConfig.video) {
      const isLocalVideoFile = !!firstConfig.video.path
      if (isLocalVideoFile) {
        const path = firstConfig.video.path!
        if (await localFileService.isFileExist(path)) {
          video = await localFileService.getVideoFileInfo(path)
        } else {
          errors.push('视频文件不存在，请重新选择视频文件')
        }
      }
    }

    let cover: ImageFileInfo | null = null
    if (firstConfig.cover) {
      const isLocalImageFile = !!firstConfig.cover.path
      if (isLocalImageFile) {
        const path = firstConfig.cover.path!
        if (await localFileService.isFileExist(path)) {
          cover = await localFileService.getImageFileInfo(path)
        } else {
          errors.push('封面文件不存在，请重新选择封面文件')
        }
      }
    }

    if (errors.length > 0) {
      notifyService.error(errors.map((x, index) => <div key={index}>{x}</div>))
    }

    setSingleConfig((x) => {
      x.video = video
      x.cover = cover
      x.accounts = accounts
    })
    setMode('single')
  })

  const refillMultipleConfig = useEffectEvent(async () => {
    if (!formData || !accountsWithTokens) return
    const errors: string[] = []
    const items: MultipleVideoAccountViewModelItem[] = []
    for (const { accountId, formData: accountform } of formData.accountForms) {
      const account = accountsWithTokens.accounts.find((x) => x.accountId === accountId)

      if (!account) continue

      let video: VideoFileInfo | null = null
      if (accountform.video) {
        const isLocalVideoFile = !!accountform.video.path
        if (isLocalVideoFile) {
          const path = accountform.video.path!
          if (await localFileService.isFileExist(path)) {
            video = await localFileService.getVideoFileInfo(path)
          } else {
            errors.push('视频文件不存在，请重新选择视频文件')
          }
        }
      }

      let cover: ImageFileInfo | null = null
      if (accountform.cover) {
        const isLocalImageFile = !!accountform.cover.path
        if (isLocalImageFile) {
          const path = accountform.cover.path!
          if (await localFileService.isFileExist(path)) {
            cover = await localFileService.getImageFileInfo(path)
          } else {
            errors.push('封面文件不存在，请重新选择封面文件')
          }
        }
      }

      items.push(new MultipleVideoAccountViewModelItem(video, cover, account))
    }

    if (errors.length > 0) {
      notifyService.error(errors.map((x, index) => <div key={index}>{x}</div>))
    }
    setMultipleConfig((x) => {
      x.items = items
    })
    setMode('multiple')
  })

  useEffect(() => {
    if (formData) {
      const videoPaths = new Set(
        formData.accountForms
          .map((x) => x.formData.video?.key || x.formData.video?.path)
          .filter((x) => x !== undefined),
      )
      if (videoPaths.size > 1) {
        void refillMultipleConfig()
      } else {
        void refillSingleConfig()
      }
    }
  }, [formData, refillMultipleConfig, refillSingleConfig])

  useEffect(() => {
    if (formData) {
      const commonForm = formData.commonForm
      setContentConfig((x) => {
        x.aPlatformForm.title = commonForm.aPlatform.title
        x.aPlatformForm.description = commonForm.aPlatform.description
        x.bPlatformForm.title = commonForm.bPlatform.title
        x.bPlatformForm.description = commonForm.bPlatform.description
        x.bPlatformForm.tags = commonForm.bPlatform.tags

        x.isOriginal = commonForm.isOriginal ?? false
        x.location = commonForm.location
          ? Object.fromEntries(
              Object.entries(commonForm.location).map(([key, value]) => [key, value ?? null]),
            )
          : {}

        if (commonForm.categories) {
          for (const [platformName, categories] of Object.entries(commonForm.categories)) {
            if (categories) x.categories[platformName] = categories
          }
        }
      })
      setToDraft(formData.commonForm.isDraft)
    }
  }, [formData, setContentConfig])

  // endregion

  const silentSessionCheck = useEffectEvent(() => {
    void sessionCheck(selectedAccounts)
  })

  // 账号数量变化时应该尝试检查账号登录状态
  useEffect(() => {
    // if (isElectron()) {
    silentSessionCheck()
    // }
  }, [selectedAccounts.length, silentSessionCheck])

  useEffect(() => {
    return eventBus.on(authorizeEvents.accountUpdated, (account) => {
      if (mode === 'single') {
        setSingleConfig((draft) => {
          if (draft.accounts.some((item) => item.accountId === account.accountId)) {
            draft.accounts = [
              ...draft.accounts.filter((item) => item.accountId !== account.accountId),
              account as SpiderAccount,
            ]
          }
        })
      } else {
        setMultipleConfig((draft) => {
          for (const item of draft.items) {
            if (item.account?.accountId === account.accountId) {
              item.account = account as SpiderAccount
            }
          }
        })
      }
    })
  }, [mode, setMultipleConfig, setSingleConfig])

  // region 微信场景/锁定相关

  useEffect(() => {
    console.debug('初始化场景数据', accountsWithTokens)
    if (accountsWithTokens) {
      setAccounts(accountsWithTokens)
    }
  }, [accountsWithTokens, setAccounts])

  const clearup = useEffectEvent(() => {
    const needRelease = !published.current
    // 未发布的任务需要释放锁定
    if (needRelease) {
      releaseAllAccount()
    }
  })

  useEffect(() => {
    return () => {
      clearup()
    }
  }, [clearup])

  const publishWithBrowserSupported = useMemo(() => {
    const parentAccountIds = new Set<string>()
    if (mode === 'single') {
      for (const account of singleConfig.accounts.filter(
        (x) => x instanceof WechatShiPinHao3rdPartySubAccount,
      )) {
        if (parentAccountIds.has(account.parentAccountId)) {
          return false
        }
        parentAccountIds.add(account.parentAccountId)
      }
      return true
    }

    for (const item of multipleConfig.items) {
      if (item.account instanceof WechatShiPinHao3rdPartySubAccount) {
        if (parentAccountIds.has(item.account.parentAccountId)) {
          return false
        }
        parentAccountIds.add(item.account.parentAccountId)
      }
    }
    return true
  }, [singleConfig, multipleConfig, mode])
  // endregion

  return (
    <PlatformRepresentativeAccountContextProvider accounts={selectedAccounts}>
      <div className="flex h-full flex-col bg-[#F8F8FA]">
        {/* 标题栏 */}
        <PublishHeaderBase title="发布视频">
          <PublishNowHoverCard
            publishWithSpider={publishNow}
            publishWithBrowserSupported={publishWithBrowserSupported}
            publishWithBrowser={publishBrowser}
            publishCloud={publishCloud}
            publishCloudDeveloping={false}
            pending={pending}
            showPlatformType={advancedMode}
          />
        </PublishHeaderBase>

        {/* 内容部分 */}
        <ScrollArea className="flex flex-1 flex-grow flex-col gap-2 overflow-hidden">
          <div className="flex flex-grow flex-col gap-2 p-3">
            <div className="flex w-full rounded-lg bg-white">
              <div className="w-0 flex-grow px-4 pb-[36px] pr-0 pt-[28px]">
                {mode === 'single' ? (
                  <SingleVideoAccount
                    config={singleConfig}
                    onVideoAdd={(videos: VideoFileInfo[]) => {
                      const newVideos = singleConfig.video
                        ? [singleConfig.video, ...videos]
                        : videos
                      if (newVideos.length > 1) {
                        setMode('multiple')
                        setMultipleConfig((draft) => {
                          draft.items = newVideos.map(
                            (video, index) =>
                              new MultipleVideoAccountViewModelItem(
                                video,
                                index === 0 ? singleConfig.cover : null,
                                singleConfig.accounts[0] ?? null,
                              ),
                          )
                        })
                      } else {
                        setSingleConfig((draft) => {
                          draft.video = newVideos[0]
                        })
                      }
                    }}
                    setConfig={setSingleConfig}
                    selectedPlatforms={selectedPlatforms}
                  />
                ) : (
                  <MultipleVideoAccount config={multipleConfig} setConfig={setMultipleConfig} />
                )}
              </div>
            </div>
            <div className="flex gap-2">
              <VideoForm
                advancedMode={advancedMode}
                onAdvancedMode={() => {
                  setAdvancedMode(!advancedMode)

                  if (advancedMode) {
                    setContentConfig((draft) => {
                      draft.bPlatformForm.description =
                        contentConfig.aPlatformForm.description.replace(
                          /<topic[^>]*>.*?<\/topic>/g,
                          '',
                        )
                      draft.bPlatformForm.title = contentConfig.aPlatformForm.title
                      draft.bPlatformForm.tags = htmlService.getTopics(
                        contentConfig.aPlatformForm.description,
                      )
                    })
                  }
                }}
                visualHint={visualHint}
                contentConfig={contentConfig.aPlatformForm}
                setContentConfig={(updater) => {
                  setContentConfig((draft) => {
                    updater(draft.aPlatformForm)
                  })
                }}
                selectedPlatforms={selectedPlatforms}
              >
                {advancedMode ? (
                  <AdvancedDescriptionForm
                    visualHint={visualHint}
                    bContentConfig={contentConfig.bPlatformForm}
                    aContentConfig={contentConfig.aPlatformForm}
                    aSetContentConfig={(updater) => {
                      setContentConfig((draft) => {
                        updater(draft.aPlatformForm)
                      })
                    }}
                    bSetContentConfig={(updater) => {
                      setContentConfig((draft) => {
                        updater(draft.bPlatformForm)
                      })
                    }}
                  />
                ) : (
                  <BaseDescriptionForm
                    visualHint={visualHint}
                    contentConfig={contentConfig.aPlatformForm}
                    aSetContentConfig={(updater) => {
                      setContentConfig((draft) => {
                        updater(draft.aPlatformForm)
                      })
                    }}
                    bSetContentConfig={(updater) => {
                      setContentConfig((draft) => {
                        updater(draft.bPlatformForm)
                      })
                    }}
                    selectedPlatforms={selectedPlatforms}
                  />
                )}
              </VideoForm>
            </div>

            {(visualHint.originalSupport ||
              visualHint.locationSupport ||
              visualHint.scheduledTimeSupport ||
              visualHint.categorySupport ||
              visualHint.tagSupport) && (
              <div className="flex w-full">
                <div className="w-0 flex-grow rounded-lg bg-white px-4 pb-[36px] pr-0 pt-[28px] shadow-sm">
                  <div className="flex flex-col space-y-9">
                    {visualHint.originalSupport && (
                      <PublishFormItem label="类型">
                        <div className="flex min-h-10 flex-col justify-center">
                          <div className="flex items-center space-x-2">
                            <RadioGroup
                              defaultValue="true"
                              id="isOriginal"
                              value={contentConfig.isOriginal.toString()}
                              onValueChange={(value) => {
                                formState.setDirty(true)
                                setContentConfig((x) => {
                                  x.isOriginal = value === 'true'
                                })
                              }}
                              className="grid-cols-2"
                            >
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="true" id="r1" />
                                <Label htmlFor="r1" className="cursor-pointer">
                                  原创
                                </Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="false" id="r2" />
                                <Label htmlFor="r2" className="cursor-pointer">
                                  转载
                                </Label>
                              </div>
                            </RadioGroup>
                          </div>
                        </div>
                      </PublishFormItem>
                    )}
                    {visualHint.locationSupport && (
                      <PublishFormItem contentWidthLimited={false} label="位置">
                        <PositionSelector
                          selectedAccounts={selectedAccounts}
                          value={contentConfig.location}
                          onChange={(location) => {
                            formState.setDirty(true)
                            setContentConfig((x) => {
                              x.location = location
                            })
                          }}
                          allowedPlatforms={visualHint.locationPlatforms}
                        />
                      </PublishFormItem>
                    )}

                    {visualHint.scheduledTimeSupport && !toDraft && (
                      <FormItem
                        label={
                          <div className="flex items-center gap-1">
                            <span>定时发送</span>
                            <HelpTooltop title="设置后作品将推送至平台以设定的时间发布" />
                          </div>
                        }
                      >
                        <DateTimePicker
                          timestamp={contentConfig.timing}
                          onChange={(timing) => {
                            formState.setDirty(true)
                            setContentConfig((x) => {
                              x.timing = timing
                            })
                          }}
                        />
                      </FormItem>
                    )}

                    {visualHint.aiStatementSupport && (
                      <FormItem
                        label={
                          <div className="flex items-center gap-1">
                            <span>创作声明</span>
                          </div>
                        }
                      >
                        <span className="text-sm text-[#ABA9AD]">百家号</span>
                        <div className="flex items-center gap-1">
                          <Checkbox
                            onCheckedChange={(check: boolean) => {
                              formState.setDirty(true)
                              setContentConfig((x) => {
                                if (!x.aiStatement) x.aiStatement = {}
                                x.aiStatement[platformNames.BaiJiaHao] = { isAigc: check }
                              })
                            }}
                          />
                          <span className="text-sm">Ai创作声明</span>
                        </div>
                      </FormItem>
                    )}

                    {visualHint.categorySupport && (
                      <PublishFormItem
                        label="分类"
                        required={visualHint.categoryRequired}
                        contentWidthLimited={false}
                      >
                        <VideoCategory
                          categoryPlatforms={visualHint.categoryPlatforms}
                          value={contentConfig.categories}
                          onChange={(categories) => {
                            formState.setDirty(true)
                            setContentConfig((x) => {
                              x.categories = categories
                            })
                          }}
                        />
                        <FrequencyUsedCategoryGroup
                          fillingCategories={contentConfig.categories}
                          onSelect={(categories) => {
                            formState.setDirty(true)
                            setContentConfig((x) => {
                              x.categories = categories
                            })
                          }}
                        />
                      </PublishFormItem>
                    )}

                    {visualHint.draftPublishSupport && (
                      <PublishFormItem
                        label={
                          <div className="flex items-center gap-1">
                            <span>发布类型</span>
                            <HelpTooltop
                              title={`不支持：腾讯微视、哔哩哔哩平台不支持发布草稿，将直接公布稿件,
                                不支持：抖音、快手、小红书平台不支持发布草稿，将转为私密稿件`}
                            />
                          </div>
                        }
                        contentWidthLimited={false}
                      >
                        <div className="flex min-h-10 flex-col justify-center">
                          <div className="flex items-center space-x-2">
                            <RadioGroup
                              defaultValue="false"
                              id="push-type-v"
                              onValueChange={(value) => {
                                const valueBoolean = value === 'true'

                                setToDraft(valueBoolean)
                                if (valueBoolean) {
                                  setContentConfig((x) => {
                                    x.timing = undefined
                                  })
                                }
                              }}
                              className="flex items-center gap-2"
                            >
                              <div className="flex items-center gap-2">
                                <RadioGroupItem value="false" id="rt1" />
                                <Label htmlFor="rt1" className="cursor-pointer">
                                  正式
                                </Label>
                              </div>
                              <div className="flex items-center gap-2">
                                <RadioGroupItem value="true" id="rt2" />
                                <Label htmlFor="rt2" className="cursor-pointer">
                                  草稿/私密
                                </Label>
                              </div>
                            </RadioGroup>
                          </div>
                        </div>
                      </PublishFormItem>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </ScrollArea>
      </div>
      <CountExhaustDialog open={countFull} openChange={resetCountFull} />
      <LoadingModalDialog isLoading={pending} message="正在发布..."></LoadingModalDialog>
      {selectedAccounts.map((x) => (
        <AccountValidation account={x} key={x.accountId} />
      ))}
    </PlatformRepresentativeAccountContextProvider>
  )
}

const accountValidator = new Validator<SpiderAccount, AccountRuleResultExtra>().addRule(
  (subject) => {
    const extra = {
      platform: subject.platform,
      fieldName: '代理区域',
      account: subject,
    } satisfies AccountRuleResultExtra
    if (!subject.proxyRegionCode) {
      return new RuleResult<AccountRuleResultExtra>('invalid', '失效账号无法发布', extra)
    }
    return RuleResult.newValid(extra)
  },
)

function AccountValidation({ account }: { account: SpiderAccount }) {
  const summary = useSummaryContext(AccountSummaryContext)

  useValidation(account, accountValidator, summary)

  return null
}

export function VideoPublish(props: VideoPublishFromProps) {
  return (
    <PublishInfrastructureProvider>
      <VideoPublishFrom {...props} />
    </PublishInfrastructureProvider>
  )
}

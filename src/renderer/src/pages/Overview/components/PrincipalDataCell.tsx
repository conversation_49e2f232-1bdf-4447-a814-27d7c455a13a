import { TableCell } from '@renderer/shadcn-components/ui/table'
import ICountIcon from '@renderer/assets/i-count.svg?react'

interface PrincipalDataCellProps {
  currentValue: number
  incrementValue: number
  className?: string
}

export function PrincipalDataCell({
  currentValue,
  incrementValue,
  className = "bg-white px-3 text-left font-medium transition-colors group-hover:bg-[#f8fafc]"
}: PrincipalDataCellProps) {
  return (
    <TableCell className={className}>
      <div className="flex min-w-0">
        <div className="flex shrink-0">{currentValue}</div>
        <div
          className="flex items-center gap-1 pl-2 min-w-0"
          style={{
            display: incrementValue ? 'flex' : 'none',
          }}
        >
          <ICountIcon
            className="shrink-0"
            style={{
              color: '#FF4D4F',
              transform: 'scaleY(-1)',
              ...(incrementValue > 1 && {
                color: '#16A34A',
                transform: 'scaleY(1)',
              }),
            }}
          />
          <span className="text-[12px] text-[#666666] truncate">
            {Math.abs(incrementValue)}
          </span>
        </div>
      </div>
    </TableCell>
  )
}

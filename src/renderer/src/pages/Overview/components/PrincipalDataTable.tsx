import SearchInput from '@renderer/components/searchInput'
import { type PrincipalMemberList, type MemberInfo } from '@renderer/infrastructure/types/principal'
import { Button } from '@renderer/shadcn-components/ui/button'
import {
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@renderer/shadcn-components/ui/table'
import { PrincipalDataCell } from './PrincipalDataCell'
import { useMemo, useState } from 'react'
import { datetimeService, electronService } from '@renderer/infrastructure/services'
import { ChevronUp, ChevronDown } from 'lucide-react'
import { sort } from '@renderer/utils/array'
import { PrincipalDetailSheet } from './PrincipalDetailSheet'

type SortField = 'name' | 'publish' | 'fans' | 'play' | 'comments' | 'likes' | 'favorites'
type SortOrder = 'asc' | 'desc' | null

interface SortState {
  field: SortField | null
  order: SortOrder
}

interface PrincipalDataTableProps {
  data: PrincipalMemberList
  showSearch?: boolean
  showExport?: boolean
  loading?: boolean
  className?: string
}

export function PrincipalDataTable({
  data,
  showSearch = false,
  showExport = false,
  loading = false,
  className = '',
}: PrincipalDataTableProps) {
  const [search, setSearch] = useState('')
  const [sortState, setSortState] = useState<SortState>({ field: null, order: null })
  const [detailSheetOpen, setDetailSheetOpen] = useState(false)
  const [selectedMember, setSelectedMember] = useState<{
    id: string
    name: string
    avatar: string
  } | null>(null)

  // 排序处理函数
  const handleSort = (field: SortField) => {
    setSortState((prev) => {
      if (prev.field === field) {
        // 同一字段：无排序 -> 升序 -> 降序 -> 无排序
        if (prev.order === null) return { field, order: 'asc' }
        if (prev.order === 'asc') return { field, order: 'desc' }
        return { field: null, order: null }
      } else {
        // 不同字段：直接设为升序
        return { field, order: 'asc' }
      }
    })
  }

  // 获取字段值的函数
  const getFieldValue = (item: MemberInfo, field: SortField): number | string => {
    switch (field) {
      case 'name':
        return item.name || ''
      case 'publish':
        return item.data.current.publishTotal || 0
      case 'fans':
        return item.data.current.fansTotal || 0
      case 'play':
        return item.data.current.playTotal || 0
      case 'comments':
        return item.data.current.commentsTotal || 0
      case 'likes':
        return item.data.current.likesTotal || 0
      case 'favorites':
        return item.data.current.favoritesTotal || 0
      default:
        return 0
    }
  }

  const filteredData = useMemo(() => {
    let result = data

    // 先进行搜索过滤
    if (showSearch && search) {
      result = result.filter((item) => (item.name || '').includes(search))
    }

    // 再进行排序
    if (sortState.field && sortState.order) {
      result = [...result].sort(
        sort.by((item) => getFieldValue(item, sortState.field!), sortState.order === 'desc'),
      )
    }

    return result
  }, [data, search, showSearch, sortState])

  // 处理查看详情点击事件
  const handleViewDetail = (member: MemberInfo) => {
    setSelectedMember({
      id: member.data.memberId,
      name: member.name || '',
      avatar: member.avatar || '',
    })
    setDetailSheetOpen(true)
  }

  // 可排序表头组件
  const SortableTableHead = ({
    field,
    children,
  }: {
    field: SortField
    children: React.ReactNode
  }) => {
    const isActive = sortState.field === field
    const order = isActive ? sortState.order : null

    return (
      <TableHead
        className="cursor-pointer select-none truncate px-3 text-left hover:bg-gray-50"
        onClick={() => handleSort(field)}
      >
        <div className="flex min-w-0 items-center gap-1">
          <span className="truncate">{children}</span>
          <div className="flex shrink-0 flex-col">
            <ChevronUp
              className={`h-3 w-3 ${isActive && order === 'asc' ? 'text-blue-600' : 'text-gray-400'}`}
            />
            <ChevronDown
              className={`-mt-1 h-3 w-3 ${isActive && order === 'desc' ? 'text-blue-600' : 'text-gray-400'}`}
            />
          </div>
        </div>
      </TableHead>
    )
  }

  const onExport = async () => {
    if (!showExport) return

    const path = await electronService.openSaveDialogSync({
      filters: [{ name: 'CSV', extensions: ['csv'] }],
      defaultPath: `负责人数据${datetimeService.formatForFileName(datetimeService.now())}.csv`,
    })

    if (!path) return

    // 准备CSV数据
    const headers = [
      '负责人',
      '发布',
      '发布增量',
      '粉丝',
      '粉丝增量',
      '播放',
      '播放增量',
      '评论',
      '评论增量',
      '点赞',
      '点赞增量',
      '收藏',
      '收藏增量',
    ]

    const csvData = filteredData.map((item) => {
      return [
        item.name || '', // 负责人
        item.data.current.publishTotal || 0, // 发布
        item.data.increments.publishTotal || 0, // 发布增量
        item.data.current.fansTotal || 0, // 粉丝
        item.data.increments.fansTotal || 0, // 粉丝增量
        item.data.current.playTotal || 0, // 播放总数
        item.data.increments.playTotal || 0, // 播放增量
        item.data.current.commentsTotal || 0, // 评论
        item.data.increments.commentsTotal || 0, // 评论增量
        item.data.current.likesTotal || 0, // 点赞
        item.data.increments.likesTotal || 0, // 点赞增量
        item.data.current.favoritesTotal || 0, // 收藏
        item.data.increments.favoritesTotal || 0, // 收藏增量
      ]
    })

    const csvString = [headers, ...csvData]
      .map((row) =>
        row
          .map((cell) => {
            const cellStr = String(cell)
            if (cellStr.includes(',') || cellStr.includes('"') || cellStr.includes('\n')) {
              return `"${cellStr.replace(/"/g, '""')}"`
            }
            return cellStr
          })
          .join(','),
      )
      .join('\n')

    try {
      await electronService.saveCSVFile(csvString, path)
      console.log('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
    }
  }

  return (
    <div className={`flex flex-col gap-4 ${className}`}>
      {(showSearch || showExport) && (
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {showSearch && (
              <SearchInput placeholder="搜索负责人" onSearch={setSearch} className="w-64" />
            )}
          </div>
          <div className="flex items-center gap-2">
            {showExport && (
              <Button variant="secondary" onClick={onExport}>
                导出
              </Button>
            )}
          </div>
        </div>
      )}

      <div className="overflow-hidden rounded-lg border bg-white">
        <div className="overflow-x-auto">
          <table className="w-full table-fixed text-sm">
            <TableHeader className="sticky top-[-16px] z-10 border-b-0 bg-secondary">
              <TableRow className="border-none">
                <SortableTableHead field="name">负责人</SortableTableHead>
                <SortableTableHead field="publish">发布数</SortableTableHead>
                <SortableTableHead field="fans">新增粉丝</SortableTableHead>
                <SortableTableHead field="play">新增播放(阅读)</SortableTableHead>
                <SortableTableHead field="comments">新增评论</SortableTableHead>
                <SortableTableHead field="likes">新增点赞</SortableTableHead>
                <SortableTableHead field="favorites">新增收藏</SortableTableHead>
                <TableHead className="w-[100px] px-3 text-left">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={8} className="py-8 text-center">
                    加载中...
                  </TableCell>
                </TableRow>
              ) : filteredData.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="py-8 text-center">
                    暂无数据
                  </TableCell>
                </TableRow>
              ) : (
                filteredData.map((item) => (
                  <TableRow
                    key={item.id}
                    className="group !border-b border-[#E4E6EB] hover:bg-gray-50"
                  >
                    <TableCell className="w-[200px] bg-white px-3 text-left font-medium transition-colors group-hover:bg-[#f8fafc]">
                      <div className="flex min-w-0 items-center gap-2">
                        <img src={item.avatar} className="h-8 w-8 shrink-0 rounded-full" />
                        <span className="truncate">{item.name}</span>
                      </div>
                    </TableCell>
                    <PrincipalDataCell
                      currentValue={item.data.current.publishTotal}
                      incrementValue={item.data.increments.publishTotal}
                    />
                    <PrincipalDataCell
                      currentValue={item.data.current.fansTotal}
                      incrementValue={item.data.increments.fansTotal}
                    />
                    <PrincipalDataCell
                      currentValue={item.data.current.playTotal}
                      incrementValue={item.data.increments.playTotal}
                    />
                    <PrincipalDataCell
                      currentValue={item.data.current.commentsTotal}
                      incrementValue={item.data.increments.commentsTotal}
                    />
                    <PrincipalDataCell
                      currentValue={item.data.current.likesTotal}
                      incrementValue={item.data.increments.likesTotal}
                    />
                    <PrincipalDataCell
                      currentValue={item.data.current.favoritesTotal}
                      incrementValue={item.data.increments.favoritesTotal}
                    />
                    <TableCell className="w-[100px] bg-white px-3 text-left transition-colors group-hover:bg-[#f8fafc]">
                      <Button
                        variant="link"
                        size="sm"
                        className="h-auto p-0 text-blue-600 hover:text-blue-800"
                        onClick={() => handleViewDetail(item)}
                      >
                        查看详情
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </table>
        </div>
      </div>

      {/* 负责人详情Sheet */}
      <PrincipalDetailSheet
        open={detailSheetOpen}
        onOpenChange={setDetailSheetOpen}
        memberId={selectedMember?.id || null}
        memberName={selectedMember?.name}
        memberAvatar={selectedMember?.avatar}
      />
    </div>
  )
}

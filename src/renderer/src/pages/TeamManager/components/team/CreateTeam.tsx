import { Button } from '@renderer/shadcn-components/ui/button'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@renderer/shadcn-components/ui/dialog'
import { Input } from '@renderer/shadcn-components/ui/input'
import { Avatar, AvatarImage, AvatarFallback } from '@renderer/shadcn-components/ui/avatar'
import { useMutation } from '@tanstack/react-query'
import { VisuallyHidden } from '@radix-ui/react-visually-hidden'
import CameraIcon from '@renderer/assets/user/camera.svg?react'
import DefaultAvatar from '@renderer/assets/team/defaultAvatar.png'
import { useEffect, useState } from 'react'
import type { Team } from '@renderer/infrastructure/model'
import {
  electronService,
  useTeamService,
  useUploadFileService,
} from '@renderer/infrastructure/services'
import { useNotify } from '@renderer/hooks/use-notify'
import { RuleResult, useValidateSummary } from '@renderer/infrastructure/validation/rule'
import { useFormState, useValidation } from '@renderer/hooks/validation/validation'
import { ValidationMessage } from '@renderer/components/ValidationMessage'
import { Validator } from '@renderer/infrastructure/validation/validator'
import { eventBus } from '@renderer/infrastructure/event-bus/buses'
import { teamEvents } from '@renderer/infrastructure/event-bus/business-events'
import { localPath2Url } from '@common/protocol'

const nameValidator = Validator.of<string>().addRule((subject) => {
  if (subject.length === 0) {
    return RuleResult.invalid('请输入团队名称')
  }
  return RuleResult.valid
})

export function CreateTeam({ open, setOpen }: { open: boolean; setOpen: (open: boolean) => void }) {
  const teamService = useTeamService()
  const uploadFileService = useUploadFileService()
  const { notifyService } = useNotify()
  const [name, setName] = useState('')
  const [editTeamLogo, setEditTeamLogo] = useState(false)
  const [avatarKey, setAvatarKey] = useState('')
  const [avatarUrl, setAvatarUrl] = useState(DefaultAvatar)

  const formState = useFormState()

  const summary = useValidateSummary()

  const { conclusion: nameConclusion } = useValidation(name, nameValidator, summary)

  const setTeamLogoUrl = async () => {
    try {
      const imgData = await electronService.openCompressedImageFile('jpg', 'png', 'jpeg')

      if (imgData?.path) {
        const protocol = localPath2Url(imgData.path)
        setAvatarUrl(protocol)

        const response = await uploadFileService.getUploadUrl('assets')
        const result = await uploadFileService.putFile(response.serviceUrl, imgData.path as string)
        if (result?.status === 200) {
          setAvatarKey(response.key)
        }
      }
    } catch (error) {
      const msg = (error as Error).message
      console.debug(msg)
      notifyService.error('图片格式不正确，只支持jpg、png、jpeg')
    }
  }

  useEffect(() => {
    if (!open) {
      resetVariable()
    }
  }, [open])

  const resetVariable = () => {
    setEditTeamLogo(false)
    setAvatarKey('')
  }

  const mutation = useMutation({
    mutationFn: () => {
      return teamService.createTeam(name, avatarKey)
    },
    onSuccess: async (team: Team) => {
      if (team && team.id) {
        eventBus.emit(teamEvents.teamDetailChanged, team.id)
        notifyService.success('创建团队成功')
        resetVariable()
        setOpen(false)
      }
    },
  })

  const submit = async () => {
    formState.setDirty(true)
    if (!summary.valid) {
      return
    }

    mutation.mutate()
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="w-[480px] gap-0">
        <DialogHeader>
          <DialogTitle className="text-lg font-medium leading-6 text-[#222222]">
            创建团队
          </DialogTitle>

          <VisuallyHidden>
            <DialogDescription />
          </VisuallyHidden>
        </DialogHeader>

        <div className="mt-11 flex justify-center">
          <div
            className="relative"
            onMouseEnter={() => setEditTeamLogo(true)}
            onMouseLeave={() => setEditTeamLogo(false)}
          >
            <Avatar className="z-0 h-[72px] w-[72px] rounded-2xl">
              <AvatarImage src={avatarUrl} />
              <AvatarFallback className="z-0 h-[72px] w-[72px] rounded-2xl"></AvatarFallback>
            </Avatar>

            {editTeamLogo && (
              <div
                className="absolute left-0 top-0 z-10 flex h-[72px] w-[72px] cursor-pointer items-center justify-center rounded-2xl bg-[rgba(0,0,0,0.3)]"
                onClick={setTeamLogoUrl}
              >
                <CameraIcon />
              </div>
            )}
          </div>
        </div>

        <div className="my-[40px]">
          <Input
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="请输入团队名称"
            maxLength={16}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault()
              }
            }}
          />
          <ValidationMessage subject={name} conclusion={nameConclusion} formState={formState} />
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => {
              resetVariable()
              setOpen(false)
            }}
          >
            取消
          </Button>
          <Button disabled={!summary.valid} onClick={submit}>
            创建团队
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

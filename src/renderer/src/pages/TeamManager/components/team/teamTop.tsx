import { Avatar, AvatarFallback, AvatarImage } from '@renderer/shadcn-components/ui/avatar'

import CameraIcon from '@renderer/assets/user/camera.svg?react'
import MoreIcon from '@renderer/assets/common/more.svg?react'

import { MasterRestricted } from '@renderer/components/MasterRestricted'
import { NonMasterRestricted } from '@renderer/components/NonMasterRestricted'
import { ManagerRestricted } from '@renderer/components/ManagerRestricted'
import { useContextStore } from '@renderer/store/contextStore'
import {
  electronService,
  useCurrentTeamService,
  useTeamService,
  useUploadFileService,
} from '@renderer/infrastructure/services'
import { notifyService } from '@renderer/hooks/use-notify'
import { EditTeam } from './EditTeam'
import { DissolveTeam } from './DissolveTeam'
import { useState } from 'react'
import { useConfirm } from '@renderer/hooks/useConfirm'
import { CopyButton } from '@renderer/components/copyButton'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@renderer/shadcn-components/dropdown-menu'
import { Button } from '@renderer/shadcn-components/ui/button'
import { VipTag } from './VipTag'
import type { FileObjectSource } from '@renderer/infrastructure/model'

export function TeamTop() {
  const currentTeam = useContextStore((state) => state.currentTeam)
  const currentTeamService = useCurrentTeamService()
  const uploadFileService = useUploadFileService()
  const teamService = useTeamService()
  const [openEditTeam, setOpenEditTeam] = useState(false)
  const [openDissolveTeam, setOpenDissolveTeam] = useState(false)
  const { confirm } = useConfirm()

  /**
   * 选择团队logo
   */
  const selectTeamLogo = async () => {
    try {
      const fileSource = (await electronService.openImageFile('jpg', 'png', 'jpeg'))?.fileSource as
        | FileObjectSource
        | undefined

      if (fileSource) {
        //TODO uploadFileService一般不应在外层直接使用，可以考虑封装进currentTeamService.updateTeamLogo
        const response = await uploadFileService.getUploadUrl('assets')
        const result = await uploadFileService.putFileWeb(
          response.serviceUrl,
          fileSource.fileObject,
        )
        if (result?.status === 200) {
          await currentTeamService.updateTeamLogo(response.key)
          notifyService.success('操作成功')
        }
      }
    } catch (error) {
      const msg = (error as Error).message
      console.debug(msg)
      notifyService.error('图片格式不正确，只支持jpg、png、jpeg')
    }
  }

  /**
   * 退出团队
   */
  const onQuitTeam = async () => {
    if (
      await confirm({
        title: '退出团队',
        description: '确定退出该团队？',
        confirmText: '确定退出',
        cancelText: '取消',
        type: 'destructive',
      })
    ) {
      await teamService.quitTeam()
    }
  }

  return (
    <div className="flex items-center gap-2 rounded-lg border border-solid border-[#E4E6EB] p-4">
      <div className="group relative h-max w-max">
        <Avatar className="z-0 h-16 w-16 rounded-md">
          <AvatarImage src={currentTeam?.logoUrl} className={'scale-[1.1]'} />
          <AvatarFallback className="z-0 h-16 w-16 rounded-md"></AvatarFallback>
        </Avatar>

        <ManagerRestricted>
          <div
            className="absolute left-0 top-0 z-10 hidden h-16 w-16 cursor-pointer items-center justify-center rounded-md bg-[rgba(0,0,0,0.5)] group-hover:flex"
            onClick={selectTeamLogo}
          >
            <CameraIcon />
          </div>
        </ManagerRestricted>
      </div>
      <div className="flex h-full flex-1 shrink-0 flex-col justify-between gap-1">
        <div className="flex flex-1 items-center justify-between">
          <div className="flex flex-1 items-center gap-2">
            <span className="line-clamp-1 text-base font-medium leading-6 text-[#222222]">
              {currentTeam?.name}
            </span>

            <VipTag />

            {/* <ManagerRestricted>
              <EditIcon
                className="flex-shrink-0 ml-2 cursor-pointer"
                onClick={() => {
                  setOpenEditTeam(true)
                }}
              />
            </ManagerRestricted> */}
          </div>
        </div>
        <div className="flex items-center gap-1 text-sm text-gray-400">
          <span>团队ID：{currentTeam?.code}</span>
          <CopyButton className="h-6 w-6" text={`${currentTeam?.code}`} />
        </div>
      </div>
      <div className="h-[60px]">
        <div className="flex flex-col gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button className="h-6 w-6" size={'icon'} variant={'outline'}>
                <MoreIcon />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent side="bottom" align="end">
              <DropdownMenuItem onClick={() => setOpenEditTeam(true)}>
                修改团队名称
              </DropdownMenuItem>
              <MasterRestricted>
                <DropdownMenuItem onClick={() => setOpenDissolveTeam(true)}>
                  解散团队
                </DropdownMenuItem>
              </MasterRestricted>
              <NonMasterRestricted>
                <DropdownMenuItem onClick={onQuitTeam}>退出团队</DropdownMenuItem>
              </NonMasterRestricted>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      {/* 修改团队名称 */}
      <EditTeam open={openEditTeam} setOpen={setOpenEditTeam} />

      {/* 解散团队 */}
      <DissolveTeam open={openDissolveTeam} setOpen={setOpenDissolveTeam} />
    </div>
  )
}

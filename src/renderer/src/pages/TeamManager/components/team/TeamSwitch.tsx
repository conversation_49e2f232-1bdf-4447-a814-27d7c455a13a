import { Popover, PopoverContent, PopoverTrigger } from '@renderer/shadcn-components/ui/popover'
import { Separator } from '@renderer/shadcn-components/ui/separator'
import { useState } from 'react'
import SwitchIcon from '@renderer/assets/user/switch.svg?react'
import JoinTeamIcon from '@renderer/assets/user/join.svg?react'
import CreateTeamIcon from '@renderer/assets/user/team.svg?react'
import { useTeamsStore } from '@renderer/store/teamsStore'
import SauryTooltip from '@renderer/components/tooltip'
import { useUserService } from '@renderer/infrastructure/services'
import { CreateTeam } from '@renderer/pages/TeamManager/components/team/CreateTeam'
import { JoinTeam } from '@renderer/pages/TeamManager/components/team/JoinTeam'
import { useConfirm } from '@renderer/hooks/useConfirm'
import { useCurrentTeam } from '@renderer/hooks/use-current-team'
import { cn } from '@renderer/lib/utils'
import { VipIconComponent } from '@renderer/components/vip/VipIcon'

export function TeamSwitch() {
  const { teams } = useTeamsStore((state) => ({
    teams: state.teams,
  }))
  const userService = useUserService()
  const currentTeam = useCurrentTeam()
  const [open, setOpen] = useState(false)

  const [openCreateTeam, setOpenCreateTeam] = useState(false)
  const [openJoinTeam, setOpenJoinTeam] = useState(false)

  const { confirm } = useConfirm()

  /**
   * 切换团队
   */
  const handleSwitchTeam = async (teamId: string) => {
    if (
      await confirm({
        title: '切换团队',
        type: 'destructive',
        description: '切换团队将清空左侧菜单栏，是否切换？',
        confirmText: '确定切换',
        cancelText: '取消',
      })
    ) {
      void userService.switchTeam(teamId)
    }
  }

  /**
   * 创建团队
   */
  const handleCreateTeam = () => {
    setOpen(false)
    setOpenCreateTeam(true)
  }

  /**
   * 加入团队
   */
  const handleJoinTeam = () => {
    setOpen(false)
    setOpenJoinTeam(true)
  }

  return (
    <div className="flex grow items-center gap-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <div className="flex h-6 grow cursor-pointer items-center justify-center p-[1px]">
            <div className="flex h-[18px] grow items-center justify-center">
              <SauryTooltip tooltip="切换团队">
                <div className="flex grow items-center justify-between gap-1">
                  <span className="text-sm font-normal text-[#2D2933]">切换团队</span>
                  <SwitchIcon className="h-[16px] w-[16px]" />
                </div>
              </SauryTooltip>
            </div>
          </div>
        </PopoverTrigger>

        <PopoverContent className="mr-2 flex w-[220px] flex-col p-0" side="right" align="start">
          <div className="flex max-h-[260px] flex-grow flex-col gap-1 overflow-y-auto p-2">
            {teams.map((team) => (
              <div
                key={team.id}
                className={cn(
                  'flex items-center rounded-md px-3 py-[6px] hover:bg-[#F1F1FD]',
                  currentTeam.id === team.id ? 'bg-[#F1F1FD]' : '',
                )}
                onClick={() => handleSwitchTeam(team.id)}
              >
                <img src={team.logoUrl} alt="" className="h-6 w-6 rounded-md" />

                <div className="ml-3 flex flex-1 items-center overflow-hidden">
                  <SauryTooltip tooltip={team.name}>
                    <span className={'line-clamp-1 text-sm font-normal text-[#2D2933]'}>
                      {team.name}
                    </span>
                  </SauryTooltip>
                </div>

                {team.isVip && <VipIconComponent />}
              </div>
            ))}
          </div>

          <Separator />

          <ul className="px-2 pb-2 pt-[5px]">
            <li
              className="flex w-full cursor-pointer items-center rounded-md px-[14px] py-[6px] hover:bg-[#F1F1FD]"
              onClick={handleCreateTeam}
            >
              <CreateTeamIcon />
              <span className="ml-[14px] text-sm font-normal">创建团队</span>
            </li>

            <li
              className="flex w-full cursor-pointer items-center rounded-md px-[14px] py-[6px] hover:bg-[#F1F1FD]"
              onClick={handleJoinTeam}
            >
              <JoinTeamIcon />
              <span className="ml-[14px] text-sm font-normal">加入团队</span>
            </li>
          </ul>
        </PopoverContent>
      </Popover>

      {/* 创建团队弹窗 */}
      {openCreateTeam && <CreateTeam open={openCreateTeam} setOpen={setOpenCreateTeam} />}
      {/* 加入团队弹窗 */}
      {openJoinTeam && <JoinTeam open={openJoinTeam} setOpen={setOpenJoinTeam} />}
    </div>
  )
}

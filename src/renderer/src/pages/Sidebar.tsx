import { UserAvatar } from '@renderer/components/user/UserAvatar'
import { ServicePopover } from '@renderer/components/Service'
import { Updates } from './Message/updates'
import { DynamicFeatureTabItems } from '@renderer/pages/IndexPage/components/dynamic-feature-tab-items'
import { cn } from '@renderer/lib/utils'
import {
  FixedFeatureTabItem,
  FixedFeatureTabItems,
} from '@renderer/pages/IndexPage/components/fixed-feature-tab-items'
import { ShortcutContextMenu } from '@renderer/pages/IndexPage/shortcut-context-menu'
import { isWin } from '@common/protocol'
import { Separator } from '@renderer/shadcn-components/ui/separator'
import { ArrowDown } from 'lucide-react'
import TooltipButton from '@renderer/components/tooltipButton'
import { ResizableHandle, ResizablePanel } from '@renderer/shadcn-components/ui/resizable'

import { useAppStore, useInnerAppStore } from '@renderer/store/appStore'
import { useEffect, useMemo, useState } from 'react'
import { useFeatureManager } from '@renderer/infrastructure/services'
import { usePixelToPercentage } from '@renderer/hooks/usePixelToPercentage'
// import { mainEvents } from '@common/events/main-events'
import { tabFeatures } from '@renderer/infrastructure/model'
import { useScrollShadow } from '@renderer/hooks/useScrollShadow'

import { MessagesPopover } from '@renderer/components/Messages'
import { useNavigate } from 'react-router-dom'
export const Sidebar = () => {
  const navigate = useNavigate()
  const [collapsed, setIsCollapsed] = useAppStore((state) => [state.collapsed, state.setCollapsed])

  const { ref, shadow } = useScrollShadow()

  const expanded = !collapsed

  const defaultLayout = useMemo(() => {
    return useInnerAppStore.getState().defaultLayout
  }, [])
  const { instances, clearInstances } = useFeatureManager()
  const [isFullScreen] = useState(false)
  const [minSize, collapsedSize, maxSize] = usePixelToPercentage(184, 120, 220)
  useEffect(() => {
    const handleSaveAuthData = (e: Event) => {
      const detail = (e as CustomEvent).detail as { action: string; payload: { url: string } }
      console.log(detail)
    }

    window.addEventListener('save-auth-data', handleSaveAuthData)
    return () => {
      window.removeEventListener('save-auth-data', handleSaveAuthData)
    }
  }, [])
  return (
    <ResizablePanel
      defaultSize={defaultLayout[0]}
      collapsedSize={collapsedSize}
      collapsible={true}
      minSize={minSize}
      maxSize={maxSize}
      onCollapse={() => {
        setIsCollapsed(true)
      }}
      onExpand={() => {
        setIsCollapsed(false)
      }}
      className={cn('relative shrink-0', {
        'max-w-[68px]': !expanded,
        'mx-2': expanded,
      })}
    >
      {/* <button
        onClick={() => {
          const customEvent = new CustomEvent('open-auth-view', {
            detail: {
              action: 'notify',
              payload: { url: 'https://creator.douyin.com/' },
            },
          })
          document.dispatchEvent(customEvent)
        }}
      >
        测试
      </button> */}
      <div
        className={cn('electron-drag-region flex h-full flex-shrink-0 flex-col gap-4 py-2 pt-4', {
          'pt-10': !isWin && !isFullScreen,
        })}
      >
        <div className={cn('flex w-full items-center', expanded ? 'px-3' : 'px-1.5')}>
          <div
            className={cn(
              'electron-drag-region flex flex-1 items-center gap-2',
              expanded ? '' : 'flex-col gap-4',
            )}
          >
            <UserAvatar expanded={expanded} />
            <div
              className={cn('flex items-center gap-1', {
                'ml-auto': expanded,
              })}
            >
              <ShortcutContextMenu size={expanded ? 'small' : 'large'} />
            </div>
          </div>
        </div>
        <div className="flex flex-1 flex-col gap-2 overflow-hidden">
          <div className="group/features flex grow flex-col gap-1 overflow-hidden">
            <div className={cn(expanded ? 'px-3' : 'px-1.5')}>
              <FixedFeatureTabItem feature={tabFeatures.主页} expanded={expanded} />
            </div>
            <div className={cn('flex flex-1 flex-col overflow-hidden', shadow)}>
              <div
                ref={ref}
                className={cn(
                  'scrollbar-hide relative flex flex-1 flex-col overflow-y-auto overflow-x-hidden',
                  expanded ? 'px-3' : 'px-1.5',
                )}
              >
                <FixedFeatureTabItems expanded={expanded}></FixedFeatureTabItems>
                {instances.length > 0 && (
                  <div className="flex h-8 shrink-0 items-center justify-between px-3">
                    <Separator className="flex-1 bg-[#969499]/15"></Separator>
                    {expanded && (
                      <TooltipButton
                        tooltipProps={{
                          contentOptions: {
                            side: 'right',
                          },
                        }}
                        onClick={() => {
                          clearInstances()
                          navigate('/')
                        }}
                        tooltip="关闭下方全部标签页"
                        className="hidden h-5 gap-0 p-0 px-1 text-xs text-textSecondary hover:bg-mainAccent group-hover/features:flex"
                        variant="ghost"
                      >
                        <ArrowDown className="h-3.5 w-3.5" />
                        关闭
                      </TooltipButton>
                    )}
                  </div>
                )}
                <DynamicFeatureTabItems expanded={expanded}></DynamicFeatureTabItems>
                <div className="drag-handler w-full flex-1"></div>
              </div>
            </div>
          </div>
          <div className={cn(expanded ? 'px-3' : 'px-1.5')}>
            <div
              className={cn('electron-drag-region flex items-center justify-between gap-1', {
                'flex-col gap-2': !expanded,
              })}
            >
              <Updates expanded={expanded} />

              <ServicePopover expanded={expanded} />

              <MessagesPopover expanded={expanded} />
            </div>
          </div>
        </div>
      </div>
      <ResizableHandle
        className="duration-250 absolute right-0 top-0 my-4 w-0.5 flex-1 bg-transparent transition-all ease-linear hover:bg-primary"
        style={{ height: 'calc(100% - 32px)' }}
      />
    </ResizablePanel>
  )
}

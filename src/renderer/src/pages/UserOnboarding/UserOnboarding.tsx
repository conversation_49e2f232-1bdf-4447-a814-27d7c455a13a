import { animated, useSpring } from '@react-spring/web'
import Logo from '@renderer/assets/passport/logo.svg?react'
import { useOnboardingStep } from '@renderer/store/userOnboardingStore'
import { TeamSelectionStep } from './components/TeamSelectionStep'
import { JoinTeamStep } from './components/JoinTeamStep'
import { CreateTeamStep } from './components/CreateTeamStep'

export function UserOnboarding() {
  const currentStep = useOnboardingStep()

  const springs = useSpring({
    from: { opacity: 0, transform: 'translateY(20px)' },
    to: { opacity: 1, transform: 'translateY(0px)' },
    config: { duration: 300 },
  })

  const renderStep = () => {
    switch (currentStep) {
      case 'team-selection':
        return <TeamSelectionStep />

      case 'join-team':
        return <JoinTeamStep />

      case 'create-team':
        return <CreateTeamStep />
      default:
        return <TeamSelectionStep />
    }
  }

  return (
    <div className="h-full bg-[url('@renderer/assets/passport/background.png')] bg-cover">
      <div className="relative flex h-full w-full flex-col items-center justify-center p-14 pt-0">
        {/* Logo */}
        <div className="absolute left-6 top-3 h-16">
          <Logo />
        </div>

        {/* 步骤内容 */}
        <animated.div style={springs} className="w-full max-w-screen-lg">
          {renderStep()}
        </animated.div>
      </div>
    </div>
  )
}

import { useState, useCallback } from 'react'
import { Button } from '@renderer/shadcn-components/ui/button'
import { Input } from '@renderer/shadcn-components/ui/input'
import { Card, CardContent, CardHeader } from '@renderer/shadcn-components/ui/card'
import { CameraIcon, ChevronLeft } from 'lucide-react'
import { LoadingButton } from '@renderer/components/LoadingButton'
import { useNotify } from '@renderer/hooks/use-notify'
import { useOnboardingActions } from '@renderer/store/userOnboardingStore'
import { useTeamsStore } from '@renderer/store/teamsStore'
import { useMutation } from '@tanstack/react-query'
import defaultAvatar from '@renderer/assets/team/defaultAvatar.png'
import { useTeamService, useUploadFileService } from '@renderer/infrastructure/services'
import { Avatar, AvatarFallback, AvatarImage } from '@renderer/shadcn-components/ui/avatar'

const selectImageFileInWeb = (): Promise<File | null> => {
  return new Promise((resolve) => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.jpg,.jpeg,.png'
    input.style.display = 'none'

    input.onchange = (e) => {
      const files = (e.target as HTMLInputElement).files
      if (files && files.length > 0) {
        resolve(files[0])
      } else {
        resolve(null)
      }
      document.body.removeChild(input)
    }

    input.oncancel = () => {
      resolve(null)
      document.body.removeChild(input)
    }

    document.body.appendChild(input)
    input.click()
  })
}

const compressImageInWeb = (file: File, quality: number = 0.8): Promise<ArrayBuffer> => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    img.onload = () => {
      // 设置画布尺寸
      canvas.width = img.width
      canvas.height = img.height

      // 绘制图片
      ctx?.drawImage(img, 0, 0)

      // 转换为Blob并压缩
      canvas.toBlob(
        async (blob) => {
          if (blob) {
            const buffer = await blob.arrayBuffer()
            resolve(buffer)
          } else {
            reject(new Error('图片压缩失败'))
          }
        },
        'image/jpeg',
        quality,
      )
    }

    img.onerror = () => reject(new Error('图片加载失败'))
    img.src = URL.createObjectURL(file)
  })
}

/**
 * 创建团队步骤组件
 */
export function CreateTeamStep() {
  const { notifyService } = useNotify()

  const { setCurrentStep } = useOnboardingActions()
  const teamService = useTeamService()
  const uploadFileService = useUploadFileService()

  const { updateTeam } = useTeamsStore((state) => ({
    updateTeam: state.updateTeam,
  }))

  const [avatarUrl, setAvatarUrl] = useState(defaultAvatar)

  const [avatarKey, setAvatarKey] = useState('')

  // 本地状态管理
  const [teamName, setTeamName] = useState('未命名团队')

  const handleBack = () => {
    setCurrentStep('team-selection')
  }

  const mutation = useMutation({
    mutationFn: () => teamService.createTeam(teamName, avatarKey || ''),
    onSuccess: async (team) => {
      notifyService.success('创建团队成功')
      await new Promise((resolve) => setTimeout(resolve, 100))
      const teamDetail = await teamService.getTeamDetail(team.id)
      updateTeam(teamDetail)
      setCurrentStep('completed')
    },
  })

  const handleCreateTeam = useCallback(async () => {
    if (!teamName.trim()) {
      notifyService.error('请输入团队名称')
      return
    }
    mutation.mutate()
  }, [teamName, mutation, notifyService])

  const handleSkip = useCallback(async () => {
    mutation.mutate()
  }, [mutation])

  const setTeamLogoUrl = async () => {
    try {
      const selectedFile = await selectImageFileInWeb()

      if (selectedFile) {
        // 显示预览
        const previewUrl = URL.createObjectURL(selectedFile)
        setAvatarUrl(previewUrl)

        // 压缩图片
        const compressedBuffer = await compressImageInWeb(selectedFile, 0.8)

        // 上传压缩后的图片
        const response = await uploadFileService.getUploadUrl('assets')
        const result = await uploadFileService.putUint8Array(
          response.serviceUrl,
          new Uint8Array(compressedBuffer),
        )

        if (result?.status === 200) {
          setAvatarKey(response.key)
        }

        // 清理预览URL
        URL.revokeObjectURL(previewUrl)
      }
    } catch (error) {
      const msg = (error as Error).message
      console.debug(msg)
      notifyService.error('图片格式不正确，只支持jpg、png、jpeg')
    }
  }

  return (
    <div className="flex justify-center">
      <Card className="relative h-[534px] w-[600px] rounded-[20px] border-0 bg-white/95 shadow-xl backdrop-blur-sm">
        {/* 返回按钮 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleBack}
          className="absolute left-[42px] top-6 p-1 text-lg text-black hover:text-gray-900"
        >
          <ChevronLeft className="mr-1 h-6 w-6" />
          返回
        </Button>

        <CardHeader className="pb-6 pt-16">
          <div className="flex flex-col items-center space-y-6">
            {/* 团队头像 */}
            <div className="group relative h-max w-max">
              <Avatar className="z-0 h-20 w-20 rounded-full">
                <AvatarImage src={avatarUrl} className={'scale-[1.1]'} />
                <AvatarFallback className="z-0 h-20 w-20 rounded-md"></AvatarFallback>
              </Avatar>

              <div
                className="absolute left-0 top-0 z-10 hidden h-20 w-20 cursor-pointer items-center justify-center rounded-full bg-[rgba(0,0,0,0.5)] text-white group-hover:flex"
                onClick={setTeamLogoUrl}
              >
                <CameraIcon />
              </div>
            </div>
          </div>
        </CardHeader>

        <CardContent className="px-10 pb-20">
          <div className="space-y-6">
            {/* 团队名称输入 */}
            <div className="space-y-2">
              <Input
                placeholder="请输入团队名称"
                value={teamName}
                onChange={(e) => setTeamName(e.target.value)}
                className="h-[54px] border-gray-300 text-center text-lg focus:border-primary"
                maxLength={50}
              />
            </div>

            {/* 创建按钮 */}
            <LoadingButton
              className="h-[54px] w-full bg-primary py-3 text-lg font-medium text-white hover:bg-primary/90"
              isPending={mutation.isPending}
              disabled={!teamName.trim()}
              onClickAsync={handleCreateTeam}
            >
              创建
            </LoadingButton>

            {/* 跳过链接 */}
            <div className="text-center">
              <button
                onClick={handleSkip}
                className="text-sm text-primary underline hover:text-primary/80"
              >
                跳过
              </button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

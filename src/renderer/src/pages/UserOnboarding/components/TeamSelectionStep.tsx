import { Card, CardContent, CardHeader } from '@renderer/shadcn-components/ui/card'
import { useOnboardingActions } from '@renderer/store/userOnboardingStore'
import { useQuery } from '@tanstack/react-query'
import { useMessageManagerService } from '@renderer/infrastructure/services/application-service/message/message-manager-service'
import joinTeamImg from '@renderer/assets/team/joinTeam.png'
import createTeamImg from '@renderer/assets/team/createTeam.png'
import { ApplyStatus } from '@renderer/pages/UserOnboarding/components/ApplyStatus'
import { PendingInvitationStep } from '@renderer/pages/UserOnboarding/components/PendingInvitationStep'
import { RotateCcw } from 'lucide-react'
import _ from 'lodash-es'
import { useCallback, useMemo } from 'react'

/**
 * 团队选择步骤组件
 */
export function TeamSelectionStep() {
  const { setCurrentStep } = useOnboardingActions()
  const messageManagerService = useMessageManagerService()

  // 查询申请记录 或者邀请记录
  const checkStatus = useQuery({
    queryKey: ['proposalInfo'],
    queryFn: async () => await messageManagerService.getApplyOfInvite(),
  })

  // 节流
  const throttledCheckStatus = useMemo(() => {
    return _.throttle(() => {
      checkStatus.refetch()
    }, 2000)
  }, [checkStatus])

  const handleRefresh = useCallback(() => {
    throttledCheckStatus()
  }, [throttledCheckStatus])

  // 邀请处理完成后的回调
  const handleInvitationProcessed = () => {
    checkStatus.refetch()
  }

  const handleJoinTeam = () => {
    setCurrentStep('join-team')
  }

  const handleCreateTeam = () => {
    setCurrentStep('create-team')
  }

  // 如果有待处理的邀请，显示邀请界面
  if (checkStatus.data && checkStatus.data?.status === 'invitation') {
    return (
      <PendingInvitationStep
        invitation={checkStatus.data}
        onInvitationProcessed={handleInvitationProcessed}
      />
    )
  }

  if (checkStatus.data && checkStatus.data?.status === 'proposal') {
    return <ApplyStatus invitation={checkStatus.data} />
  }

  return (
    <div className="flex justify-center">
      <Card className="relative h-[534px] w-[600px] rounded-[20px] border-0 bg-white/95 shadow-xl backdrop-blur-sm">
        <CardHeader className="pb-[54px] pt-16">
          <div className="text-center">
            <h2 className="text-2xl font-semibold text-gray-900">创建/加入团队</h2>
          </div>
        </CardHeader>

        <CardContent className="px-12 pb-[100px]">
          <div className="grid grid-cols-2 gap-8">
            {/* 加入团队选项 */}
            <div className="cursor-pointer" onClick={handleJoinTeam}>
              <div className="rounded-2xl border border-gray-300 p-8 transition-colors hover:border-primary/50">
                <div className="flex flex-col items-center space-y-4 text-center">
                  {/* 插图区域 - 加入团队插图 */}
                  <div className="relative flex h-[150px] w-[150px] items-center justify-center overflow-hidden rounded-xl">
                    <img
                      src={joinTeamImg}
                      alt="加入团队"
                      className="h-full w-full object-contain"
                    />
                  </div>

                  <div>
                    <h3 className="mb-2 text-lg font-semibold text-gray-900">加入团队</h3>
                    <p className="text-sm text-gray-500">适合团队成员</p>
                  </div>
                </div>
              </div>
            </div>

            {/* 创建团队选项 */}
            <div className="cursor-pointer" onClick={handleCreateTeam}>
              <div className="relative rounded-2xl border border-gray-300 p-8 transition-colors hover:border-primary/50">
                <div className="flex flex-col items-center space-y-4 text-center">
                  <div className="relative flex h-[150px] w-[150px] items-center justify-center overflow-hidden rounded-xl">
                    <img
                      src={createTeamImg}
                      alt="创建团队"
                      className="h-full w-full object-contain"
                    />
                  </div>

                  <div>
                    <h3 className="mb-2 text-lg font-semibold text-gray-900">创建团队</h3>
                    <p className="text-sm text-gray-500">适合个人用户和团队管理员</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className={'mt-10 flex justify-center text-sm text-[#5A5A5A]'}>
            <span
              onClick={handleRefresh}
              className={'inline-flex cursor-pointer items-center justify-center gap-1 text-center'}
            >
              <RotateCcw size={18} className={`${checkStatus.isFetching ? 'animate-spin' : ''}`} />
              刷新
            </span>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

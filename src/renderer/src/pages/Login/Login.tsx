import Logo from '@renderer/assets/passport/logo.svg?react'
import { LoadingContainer } from '@renderer/components/LoadingContainer'
import { cn } from '@renderer/lib/utils'
import { Progress } from '@renderer/shadcn-components/ui/progress'
import { RenderLoginForm } from './LoginForm'
import { useUpdateStore } from '@renderer/store/updateStore'
import { Button } from '@renderer/shadcn-components/ui/button'
// import { isWin } from '@common/protocol'
// import { windowService } from '@renderer/infrastructure/services/application-service/infrastructure-service/window-service'
// import MinimizeIcon from '@renderer/assets/title/minimize.svg?react'
// import CloseIcon from '@renderer/assets/title/close.svg?react'
import { useVersionQuery } from '@renderer/hooks/useVersonQuery'
import { version } from '@renderer/infrastructure/utils/version'

export function Login() {
  const [versionQuery] = useVersionQuery()
  const [status, progress, exceptionMsg] = useUpdateStore((state) => [
    state.status,
    state.progress,
    state.exceptionMsg,
  ])

  // const onStartUpdate = useCallback((url?: string) => {
  //   updateService.checkAutoUpdate(true, true, url)
  // }, [])

  // useEffect(() => {
  //   if (isForceUpdate) {
  //     onStartUpdate(versionQuery.data?.url)
  //   }
  // }, [isForceUpdate, versionQuery.data?.url, onStartUpdate])

  const hasRenderLoginForm = !versionQuery.isLoading

  return (
    <div className="h-full bg-[url('@renderer/assets/passport/background.png')] bg-cover">
      {/* <div className={cn('electron-drag-region flex h-10 shrink-0 items-start justify-end')}>
        {isWin && (
          <div className="flex h-7">
            <div
              className="flex h-full w-10 cursor-pointer items-center justify-center hover:bg-mainAccent"
              onClick={() => {
                void windowService.minimize()
              }}
            >
              <MinimizeIcon className="h-4 w-4" />
            </div>
            <div
              className="flex h-full w-10 cursor-pointer items-center justify-center hover:bg-mainAccent"
              onClick={() => {
                void windowService.close()
              }}
            >
              <CloseIcon className="h-4 w-4" />
            </div>
          </div>
        )}
      </div> */}
      <div className="flex h-full w-full flex-col items-center justify-center p-14 pt-0">
        <div
          className={cn('h-16 transition-all duration-500 ease-out', {
            'mt-36': !hasRenderLoginForm,
          })}
        >
          <Logo />
        </div>

        {hasRenderLoginForm ? (
          <RenderLoginForm />
        ) : (
          <>
            {status === 'downloading' ? (
              <div className="flex h-16 w-full flex-col items-center justify-end gap-5 px-14">
                <Progress value={progress} />
                <span className="text-sm text-muted-foreground">下载中…</span>
              </div>
            ) : status === 'exception' ? (
              <>
                <span className="text-sm text-destructive">{exceptionMsg}</span>
                <Button
                  onClick={() => {
                    location.reload()
                  }}
                >
                  重试
                </Button>
              </>
            ) : (
              <LoadingContainer className="h-16 flex-none" />
            )}
          </>
        )}
      </div>
      <div className="text-center text-sm text-muted-foreground">{version}</div>
    </div>
  )
}

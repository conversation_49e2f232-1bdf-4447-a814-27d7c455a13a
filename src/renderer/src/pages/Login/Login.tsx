import Logo from '@renderer/assets/passport/logo.svg?react'
import { LoadingContainer } from '@renderer/components/LoadingContainer'
import { cn } from '@renderer/lib/utils'
import { Progress } from '@renderer/shadcn-components/ui/progress'
import { RenderLoginForm } from './LoginForm'
import { useUpdateStore } from '@renderer/store/updateStore'
import { Button } from '@renderer/shadcn-components/ui/button'
// import { isWin } from '@common/protocol'
// import { windowService } from '@renderer/infrastructure/services/application-service/infrastructure-service/window-service'
// import MinimizeIcon from '@renderer/assets/title/minimize.svg?react'
// import CloseIcon from '@renderer/assets/title/close.svg?react'
import { useVersionQuery } from '@renderer/hooks/useVersonQuery'
import { version } from '@renderer/infrastructure/utils/version'
import { useGlobalStorageService } from '@renderer/infrastructure/services'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { useEffect } from 'react'
import { localStorageService } from '@renderer/infrastructure/services/storage-service'

export function Login() {
  const globalStorageService = useGlobalStorageService()
  const [versionQuery] = useVersionQuery()
  const [status, progress, exceptionMsg] = useUpdateStore((state) => [
    state.status,
    state.progress,
    state.exceptionMsg,
  ])
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()

  const token = searchParams.get('token')
  const phone = searchParams.get('phone')
  const channel = searchParams.get('channel') || localStorageService.getItem("channel");

  useEffect(() => {
    if (token && phone) {
      globalStorageService.setToken(token)
      globalStorageService.setLastUsedPhone(phone)
      setTimeout(() => {
        navigate('/')
      }, 0)
    }
  }, [token, phone, globalStorageService, navigate])

  // 如果有 token 和 phone，显示加载状态而不是直接返回
  if (token && phone) {
    return (
      <div className="h-full bg-[url('@renderer/assets/passport/background.png')] bg-cover">
        <div className="flex flex-col items-center justify-center w-full h-full pt-0 p-14">
          <LoadingContainer className="flex-none h-16" />
        </div>
      </div>
    )
  }

  const hasRenderLoginForm = !versionQuery.isLoading

  return (
    <div className="h-full bg-[url('@renderer/assets/passport/background.png')] bg-cover">
      <div className="flex flex-col items-center justify-center w-full h-full pt-0 p-14">
        {hasRenderLoginForm ? (
          <RenderLoginForm channel={channel} />
        ) : (
          <>
            <div
              className={cn('h-16 transition-all duration-500 ease-out', {
                'mt-36': !hasRenderLoginForm,
              })}
            >
              <Logo />
            </div>
            {status === 'downloading' ? (
              <div className="flex flex-col items-center justify-end w-full h-16 gap-5 px-14">
                <Progress value={progress} />
                <span className="text-sm text-muted-foreground">下载中…</span>
              </div>
            ) : status === 'exception' ? (
              <>
                <span className="text-sm text-destructive">{exceptionMsg}</span>
                <Button
                  onClick={() => {
                    location.reload()
                  }}
                >
                  重试
                </Button>
              </>
            ) : (
              <LoadingContainer className="flex-none h-16" />
            )}
          </>
        )}
      </div>
      <div className="text-sm text-center text-muted-foreground">{version}</div>
    </div>
  )
}

import { VipPopoverTips } from '@renderer/components/vip/VipPopoverTips'
import { FeatureContentItems } from './IndexPage/components/feature-content-items'
import BackgroundImage from '@renderer/assets/index-page/background.png'
import { cn } from '@renderer/lib/utils'
import { DropFileTips } from './DropFileTips'
import { useCallback, useMemo } from 'react'
import { useFeatureManager } from '@renderer/infrastructure/services'
import {
  fileToImageFileInfo,
  fileToVideoFileInfo,
  filterValidFiles,
  imageAccept,
  videoAccept,
} from '@renderer/utils/file'
import { features } from '@renderer/infrastructure/model'
import { DndProvider } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import { useDropzone } from 'react-dropzone'
import { isWin } from '@common/protocol'
import { windowService } from '@renderer/infrastructure/services/application-service/infrastructure-service/window-service'
import MaximizeIcon from '@renderer/assets/title/maximize.svg?react'
import MinimizeIcon from '@renderer/assets/title/minimize.svg?react'
import CloseIcon from '@renderer/assets/title/close.svg?react'
import { useAppStore, useInnerAppStore } from '@renderer/store/appStore'
import { ResizablePanel, ResizablePanelGroup } from '@renderer/shadcn-components/ui/resizable'
// import { Sidebar } from './Sidebar'
import type { AccountsWithTokens } from '@renderer/context/scenario-context'
import { isElectron } from '@common/isElectron'
import { Sidebar } from './Sidebar'

export const Layout = () => {
  const setDefaultLayout = useAppStore((state) => state.setDefaultLayout)

  const defaultLayout = useMemo(() => {
    return useInnerAppStore.getState().defaultLayout
  }, [])
  const { openFeature } = useFeatureManager()

  const onDrop = useCallback(
    async (files: File[]) => {
      const pathFiles = files.filter((file) => file.path)
      if (!pathFiles || !pathFiles.length) return
      const filteredFiles = filterValidFiles(pathFiles, { type: ['image', 'video'] })
      if (filteredFiles.video?.length) {
        const videos = (
          await Promise.all(
            filteredFiles.video.slice(0, 20).map((file) => fileToVideoFileInfo(file)),
          )
        ).filter((x) => x)
        openFeature(features.视频发布, {
          videos: videos,
          accountsWithTokens: {
            accounts: [],
            tokens: new Map(),
          } satisfies AccountsWithTokens,
        })
      }
      if (filteredFiles.image?.length) {
        const images = (
          await Promise.all(filteredFiles.image.map((file) => fileToImageFileInfo(file)))
        ).filter((x) => x)
        console.log(images)
        openFeature(features.发布图文, {
          images: images,
        })
      }
    },
    [openFeature],
  )

  const { isDragAccept, getRootProps } = useDropzone({
    onDrop,
    accept: {
      ...imageAccept,
      ...videoAccept,
    },
    useFsAccessApi: false,
  })

  return (
    <div
      className="h-full"
      style={{
        backgroundImage: `url(${BackgroundImage})`,
        backgroundSize: 'cover',
        backgroundRepeat: 'no-repeat',
      }}
      {...getRootProps()}
    >
      <div className="flex h-full">
        <div className="relative flex grow">
          {isDragAccept && <DropFileTips />}
          <ResizablePanelGroup
            id="group"
            direction="horizontal"
            onLayout={(sizes: number[]) => {
              setDefaultLayout(sizes)
            }}
            className="h-full items-stretch"
          >
            <Sidebar />
            <ResizablePanel
              defaultSize={defaultLayout[1]}
              minSize={40}
              className="relative w-0 flex-1"
            >
              <DndProvider backend={HTML5Backend}>
                <div className="flex h-full flex-col">
                  <div
                    className={cn(
                      'flex shrink-0 items-center',
                      isWin && isElectron() ? 'h-7' : 'h-1.5',
                    )}
                  >
                    {isWin && isElectron() && (
                      <>
                        <div className="app-drag box-content flex h-full grow items-center pl-3 text-xs"></div>
                        <div
                          className="flex h-full w-10 cursor-pointer items-center justify-center hover:bg-mainAccent"
                          onClick={() => {
                            void windowService.minimize()
                          }}
                        >
                          <MinimizeIcon className="h-4 w-4" />
                        </div>
                        <div
                          className="flex h-full w-10 cursor-pointer items-center justify-center hover:bg-mainAccent"
                          onClick={() => {
                            void windowService.maximize()
                          }}
                        >
                          <MaximizeIcon className="h-4 w-4" />
                        </div>
                        <div
                          className="flex h-full w-10 cursor-pointer items-center justify-center hover:bg-mainAccent"
                          onClick={() => {
                            void windowService.close()
                          }}
                        >
                          <CloseIcon className="h-4 w-4" />
                        </div>
                      </>
                    )}
                  </div>
                  <div className="relative m-1.5 mt-0 flex h-0 grow flex-col overflow-hidden rounded-lg bg-[#F8F8F9]">
                    <VipPopoverTips />
                    <div className="relative flex flex-1 overflow-hidden">
                      <FeatureContentItems />
                    </div>
                  </div>
                </div>
              </DndProvider>
            </ResizablePanel>
          </ResizablePanelGroup>
        </div>
      </div>
    </div>
  )
}

import { LoadingButton } from '@renderer/components/LoadingButton'
import { PublishInput } from '@renderer/components/PublishInput'
import { useNotify } from '@renderer/hooks/use-notify'
import {
  electronService,
  useProfileService,
  useUploadFileService,
} from '@renderer/infrastructure/services'
import { useUserApi } from '@renderer/infrastructure/services/entity-service'
import { Avatar, AvatarFallback, AvatarImage } from '@renderer/shadcn-components/ui/avatar'
import { useContextStore } from '@renderer/store/contextStore'
import { CameraIcon } from 'lucide-react'
import { useRef, useState, useEffect } from 'react'
import { SetPassword } from './SetPassword'
import { ChangePhone } from './changePhone'
import { BindAccount } from './BindAccount'
import { BindPhone } from './BindPhone'
import { Button } from '@renderer/shadcn-components/ui/button'
import { ScriptUpdate } from '@renderer/components/Online'
import { updateDialogManager } from '../Update'
import { cn } from '@renderer/lib/utils'
import UserYiIcon from '@renderer/assets/user/useryi.svg?react'
import UserSafeIcon from '@renderer/assets/user/usersafe.svg?react'
import UserInfoIcon from '@renderer/assets/user/userinfo.svg?react'
import { UserInfoMenu, useUserInfoMenuStore } from '@renderer/store/userInfoMenuStore'

export function UserInfo() {
  const { defaultMenu, setDefaultMenu } = useUserInfoMenuStore()
  const [menu, setMenu] = useState(defaultMenu || UserInfoMenu.Info)

  // 监听默认菜单变化
  useEffect(() => {
    if (defaultMenu !== null) {
      setMenu(defaultMenu)
      // 清除默认菜单状态，避免下次打开时还是这个菜单
      setDefaultMenu(null)
    }
  }, [defaultMenu, setDefaultMenu])
  const { userInfo, setUserInfo } = useContextStore((state) => ({
    userInfo: state.userInfo,
    setUserInfo: state.setUserInfo,
  }))
  const profileService = useProfileService()
  const uploadFileService = useUploadFileService()
  const userApi = useUserApi()
  const { notifyService } = useNotify()

  const [isPending, setIsPending] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)

  async function fetchAndSetUserInfo() {
    const userInfo = await userApi.getUserInfo()
    setUserInfo(userInfo)
  }
  /**
   * 选择用户头像
   */
  const setAvatar = async () => {
    try {
      const imgData = await electronService.openCompressedImageFile('jpg', 'png', 'jpeg')

      if (imgData?.path && typeof imgData.path === 'string') {
        const response = await uploadFileService.getUploadUrl('assets')
        const result = await uploadFileService.putFile(response.serviceUrl, imgData.path)
        if (result?.status === 200) {
          await profileService.updateUserAvatar(response.key)
          await fetchAndSetUserInfo()
        }
      }
    } catch (error) {
      const msg = (error as Error).message
      console.debug(msg)
      notifyService.error('图片格式不正确，只支持jpg、png、jpeg')
    }
  }

  const onUpdateNickName = async () => {
    const nickName = inputRef.current?.value
    if (nickName && nickName !== userInfo?.nickName) {
      setIsPending(true)
      try {
        await profileService.updateUserNickName(nickName)
        const userInfo = await userApi.getUserInfo()
        setUserInfo(userInfo)
      } catch {
        //
      }
      setIsPending(false)
    }
  }

  return (
    <div className="electron-drag-region flex h-full w-full flex-col bg-[#F8F8F9]">
      <div className="drag-handler bg-[#ffffff] p-5 font-medium">个人设置</div>
      <div className="flex flex-1 gap-4 p-4">
        <div className="flex w-[200px] flex-col gap-2 rounded-lg bg-[#ffffff] p-3">
          <Button
            variant="ghost"
            className={cn('justify-start', {
              'bg-[#F1F1FD] text-[#4F46E5]': menu === UserInfoMenu.Info,
            })}
            onClick={() => {
              setMenu(UserInfoMenu.Info)
            }}
          >
            <UserInfoIcon />
            个人资料
          </Button>
          <Button
            variant="ghost"
            className={cn('justify-start', {
              'bg-[#F1F1FD] text-[#4F46E5]': menu === UserInfoMenu.Password,
            })}
            onClick={() => {
              setMenu(UserInfoMenu.Password)
            }}
          >
            <UserSafeIcon />
            账号安全
          </Button>
          <Button
            variant="ghost"
            className={cn('justify-start', {
              'bg-[#F1F1FD] text-[#4F46E5]': menu === UserInfoMenu.YiXiaoer,
            })}
            onClick={() => {
              setMenu(UserInfoMenu.YiXiaoer)
            }}
          >
            <UserYiIcon />
            关于蚁小二
          </Button>
        </div>
        <div className="flex w-full flex-1 flex-col rounded-lg bg-white shadow-sm">
          <div className="flex flex-col gap-8 p-5">
            {menu === UserInfoMenu.Info && (
              <>
                <div className="flex items-center">
                  <span className="w-20">头像</span>
                  <div className="user-avatar-wrapper h-[54px] w-[54px] flex-shrink-0">
                    <div className="common-avatar-style avatar-overlay-v1-style"></div>

                    <Avatar className="z-10 h-[48px] w-[48px]">
                      <AvatarImage src={userInfo?.avatarUrl} />
                      <AvatarFallback></AvatarFallback>
                    </Avatar>

                    <div className="overlay-camera-style" onClick={setAvatar}>
                      <CameraIcon className="text-white" />
                    </div>
                  </div>
                </div>

                <div className="flex items-center">
                  <span className="w-20">昵称</span>

                  <PublishInput
                    placeholder="请输入昵称"
                    defaultValue={userInfo?.nickName}
                    className="w-[300px]"
                    ref={inputRef}
                    maxLength={15}
                    onKeyDown={(event) => {
                      if (event.key === 'Enter') {
                        void onUpdateNickName()
                      }
                    }}
                    onBlur={(event) => {
                      if (!event.currentTarget.value) {
                        event.currentTarget.value = userInfo?.nickName ?? ''
                      }
                    }}
                  />

                  <LoadingButton
                    variant="outline"
                    className="ml-4"
                    onClick={onUpdateNickName}
                    isPending={isPending}
                  >
                    保存
                  </LoadingButton>
                </div>
              </>
            )}

            {menu === UserInfoMenu.Password && (
              <>
                <div className="flex items-center">
                  <span className="w-20">手机号</span>
                  <PublishInput
                    disabled
                    defaultValue={userInfo?.phone}
                    type="tel"
                    placeholder="手机号"
                    className="mr-4 w-[300px]"
                  />
                  {userInfo && (
                    <>
                      {userInfo.phone ? (
                        <ChangePhone phone={userInfo.phone} onSuccess={fetchAndSetUserInfo} />
                      ) : (
                        <BindPhone onSuccess={fetchAndSetUserInfo}>
                          <Button variant="outline">绑定手机号</Button>
                        </BindPhone>
                      )}
                    </>
                  )}
                </div>
                <div className="flex items-center">
                  <span className="w-20">密码</span>
                  <div className="mr-4 w-[300px]">
                    {!userInfo?.isPassword ? (
                      <PublishInput
                        disabled
                        placeholder="未设置"
                        className="h-9 border-none bg-secondary"
                      />
                    ) : (
                      <span className="text-muted-foreground">已设置</span>
                    )}
                  </div>
                  {userInfo && (
                    <SetPassword
                      isPassword={!!userInfo.isPassword}
                      phone={userInfo.phone}
                      onSuccess={() => {
                        void fetchAndSetUserInfo()
                      }}
                    />
                  )}
                </div>
                <div className="flex items-center">
                  <span className="w-20">账号绑定</span>
                  <div className="mr-4 w-[300px]">
                    {!userInfo?.account ? (
                      <PublishInput
                        disabled
                        placeholder="未绑定"
                        className="h-9 border-none bg-secondary"
                      />
                    ) : (
                      <div className="flex items-center gap-2">
                        <PublishInput
                          disabled
                          placeholder={userInfo.account}
                          className="h-9 border-none bg-secondary"
                        />
                      </div>
                    )}
                  </div>
                  {userInfo && !userInfo.account && (
                    <BindAccount
                      hasPhone={!!userInfo.phone}
                      currentPhone={userInfo.phone}
                      onSuccess={fetchAndSetUserInfo}
                    >
                      <Button variant="outline">绑定账号</Button>
                    </BindAccount>
                  )}
                </div>
              </>
            )}

            {menu === UserInfoMenu.YiXiaoer && (
              <div>
                <div className="flex items-center">
                  <div className="w-[100px] text-[#757575]">客户端版本:</div>
                  <span>{import.meta.env.APP_VERSION}</span>

                  <Button
                    className="ml-4"
                    size="sm"
                    onClick={() => {
                      updateDialogManager.open(true)
                    }}
                  >
                    检测更新
                  </Button>
                </div>
                <ScriptUpdate />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

import { zodResolver } from '@hookform/resolvers/zod'
import { VisuallyHidden } from '@radix-ui/react-visually-hidden'
import { LoadingButton } from '@renderer/components/LoadingButton'
import { VerificationCodeButton } from '@renderer/components/verificationCodeButton'
import { useApiMutation } from '@renderer/hooks/useApiQuery'
import { Button } from '@renderer/shadcn-components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@renderer/shadcn-components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@renderer/shadcn-components/ui/form'
import { Input } from '@renderer/shadcn-components/ui/input'
import { zodIsPhone } from '@renderer/utils/zod'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { toast } from 'sonner'

const formSchema = z.object({
  phone: zodIsPhone,
  code: z.string().length(6, { message: '验证码必须为6位' }),
})

type FormSchema = z.infer<typeof formSchema>

interface BindPhoneProps {
  isUpdate?: boolean
  currentPhone?: string
  onSuccess?: () => void
  children?: React.ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

export const BindPhone = ({
  isUpdate = false,
  onSuccess,
  children,
  open: externalOpen,
  onOpenChange: externalOnOpenChange,
}: BindPhoneProps) => {
  const title = '绑定手机号码'

  const [internalOpen, setInternalOpen] = useState(false)
  const open = externalOpen !== undefined ? externalOpen : internalOpen
  const setOpen = externalOnOpenChange || setInternalOpen

  const form = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      phone: '',
      code: '',
    },
    mode: 'onChange',
  })

  // 绑定手机号码的API调用
  const bindMutation = useApiMutation<{ phone: string; code: string }, void>(
    (params) => ({
      url: '/users/bind/phone',
      method: 'PUT',
      data: params,
    }),
    {
      onSuccess: () => {
        setOpen(false)
        toast.success('手机号码绑定成功')
        onSuccess?.()
      },
      onError: (error) => {
        console.error('绑定手机号码失败:', error)
      },
    },
  )

  const onSubmit = (data: FormSchema) => {
    bindMutation.mutate({
      phone: data.phone,
      code: data.code,
    })
  }

  const onOpenChange = (open: boolean) => {
    setOpen(open)
    if (!open) {
      form.reset()
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {children && <DialogTrigger asChild>{children}</DialogTrigger>}
      {!children && externalOpen === undefined && (
        <DialogTrigger asChild>
          <Button variant="outline">{title}</Button>
        </DialogTrigger>
      )}
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <VisuallyHidden>
            <DialogDescription>
              {isUpdate ? '更换您的手机号码' : '绑定您的手机号码以增强账号安全性'}
            </DialogDescription>
          </VisuallyHidden>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>手机号</FormLabel>
                  <FormControl>
                    <Input placeholder="请输入11位手机号" maxLength={11} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="code"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>验证码</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        maxLength={6}
                        className="pr-24"
                        placeholder="请输入验证码"
                        {...field}
                      />
                      <VerificationCodeButton
                        sence="bindPhone"
                        className="right-1 top-1 h-7"
                        phoneName="phone"
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end gap-2 pt-4">
              <Button type="button" variant="outline" onClick={() => setOpen(false)}>
                取消
              </Button>
              <LoadingButton
                isPending={bindMutation.isPending}
                disabled={!form.formState.isValid || bindMutation.isPending}
                type="submit"
              >
                绑定
              </LoadingButton>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}

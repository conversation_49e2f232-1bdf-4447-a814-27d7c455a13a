import { datetimeService } from '@renderer/infrastructure/services'
import EmptyMessage from '@renderer/assets/messages/empty.svg?react'
import { useSystemNotifyStateStore } from '@renderer/store/systemNotifyStore'
import { ScrollArea } from '@renderer/shadcn-components/ui/scroll-area'

export class SystemNotify {
  constructor(
    public id: string,
    public title: string,
    public content: string,
    public isPopUp: boolean,
    public createTime: Date,
  ) {}
}

export function SystemList() {
  const { notifies } = useSystemNotifyStateStore((store) => ({
    notifies: store.notifies,
  }))

  return (
    <div className="flex h-full grow flex-col px-2">
      {notifies.length === 0 && (
        <div className="flex h-full flex-col justify-center text-center text-sm text-gray-500">
          <div className={'text-center'}>
            <EmptyMessage className={'inline-block'} />
          </div>
          <div className={'pt-2'}>暂无消息通知</div>
        </div>
      )}
      {notifies.length > 0 && (
        <div>
          {notifies.map((item) => (
            <NotifyItem key={item.id} notify={item} />
          ))}{' '}
        </div>
      )}
    </div>
  )
}

export function NotifyItem({ notify }: { notify: SystemNotify }) {
  const { setDialogNotify } = useSystemNotifyStateStore((state) => ({
    setDialogNotify: state.setDialogNotify,
  }))

  return (
    <div
      className="group cursor-pointer rounded-md px-2 py-1.5 transition duration-300 hover:bg-accent"
      onClick={() => setDialogNotify(notify)}
    >
      <div className="truncate text-sm group-hover:text-primary">{notify.title}</div>
      <div className="mt-1 text-xs text-muted-foreground">
        {datetimeService.formatToDate(notify.createTime)}
      </div>
    </div>
  )
}

export function NoticePanel() {
  return (
    <div className="relative flex max-h-[272px] flex-col rounded-lg bg-white">
      <div className="mx-4 mb-2 mt-4">
        <h3 className="font-semibold">系统公告</h3>
      </div>
      <ScrollArea className="max-h-[220px] min-h-[180px] overflow-y-auto">
        <SystemList />
      </ScrollArea>
    </div>
  )
}

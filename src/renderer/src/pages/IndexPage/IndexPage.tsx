import { AdvertisePanel } from '@renderer/pages/IndexPage/advertise-panel'
import { VipPanel } from '@renderer/pages/IndexPage/vip-panel'
import { FAQPanel } from '@renderer/pages/IndexPage/faq-panel'
import { NoticePanel } from '@renderer/pages/IndexPage/notice-panel'
import { OverviewPanel } from '@renderer/pages/IndexPage/overview-panel'
import { OverviewActionPanel } from './OverviewActionPanel'
import { AdvertiseDialog } from './activity-dialog'

export function IndexPage() {
  return (
    <div className="flex h-full flex-col">
      <div className="flex-1 overflow-y-auto pr-[14px]">
        <div className="flex min-w-0 gap-[14px]">
          {/* 左侧主要内容区域 */}
          <div className="flex min-w-0 flex-1 flex-col gap-[14px]">
            <OverviewActionPanel />
            <OverviewPanel />
          </div>

          {/* 右侧固定宽度侧边栏 */}
          <div className="flex w-[330px] shrink-0 flex-col gap-[14px]">
            <VipPanel />
            <AdvertisePanel />
            <FAQPanel />
            <NoticePanel />
          </div>
        </div>
      </div>
      <AdvertiseDialog />
    </div>
  )
}

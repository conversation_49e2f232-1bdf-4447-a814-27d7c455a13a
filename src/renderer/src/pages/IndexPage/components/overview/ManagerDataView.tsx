import { ManagerTrendsBlock } from './ManagerTrendsBlock'
import { PlatformTrendsBlock } from './PlatformTrendsBlock'
import { OverviewBlock } from './OverviewBlock'
import { ManagerDataBlock } from './ManagerDataBlock'
import { HelpTooltop } from '@renderer/components/helpTooltop'

export function ManagerDataView() {
  return (
    <>
      <ManagerTrendsBlock />
      <OverviewBlock
        title={
          <>
            增长数据<HelpTooltop title="团队近30天每日的新增数据概览"></HelpTooltop>
          </>
        }
      />
      <PlatformTrendsBlock />
      <ManagerDataBlock />
    </>
  )
}

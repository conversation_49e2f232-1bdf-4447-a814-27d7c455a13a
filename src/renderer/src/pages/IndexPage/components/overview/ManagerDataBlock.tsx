import { HelpTooltop } from '@renderer/components/helpTooltop'
import { useAccountCheckService } from '@renderer/infrastructure/services/application-service/accountCheck-service'
import { type PrincipalMemberList } from '@renderer/infrastructure/types/principal'
import { PrincipalDataTable } from '@renderer/pages/Overview/components/PrincipalDataTable'
import { useEffect, useState } from 'react'

export function ManagerDataBlock() {
  const [list, setList] = useState<PrincipalMemberList>([])
  const accountCheckService = useAccountCheckService()

  useEffect(() => {
    accountCheckService.getOverview().then((res) => {
      setList(res)
    })
  }, [accountCheckService])

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center">
        <div className="flex grow items-center gap-2 font-semibold">
          负责人数据<HelpTooltop title="已设为负责人的媒体账号，系统将统计其近期 30 天的新增数据"></HelpTooltop>
        </div>
      </div>
      <PrincipalDataTable data={list} showSearch={false} showExport={false} />
    </div>
  )
}

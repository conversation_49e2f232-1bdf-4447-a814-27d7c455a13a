import 首页Default from '@renderer/assets/feature/icon/tab/首页.svg?react'
import 首页Active from '@renderer/assets/feature/icon/tab/首页Active.svg?react'
import 发布记录Default from '@renderer/assets/feature/icon/tab/发布.svg?react'
import 发布记录Active from '@renderer/assets/feature/icon/tab/发布Active.svg?react'
import 账号Default from '@renderer/assets/feature/icon/tab/账号.svg?react'
import 账号Active from '@renderer/assets/feature/icon/tab/账号Active.svg?react'
import 数据Default from '@renderer/assets/feature/icon/tab/数据.svg?react'
import 数据Active from '@renderer/assets/feature/icon/tab/数据Active.svg?react'
import 团队管理Default from '@renderer/assets/feature/icon/tab/团队管理.svg?react'
import 团队管理Active from '@renderer/assets/feature/icon/tab/团队管理Active.svg?react'
import 素材库Default from '@renderer/assets/feature/icon/tab/素材库.svg?react'
import 素材库Active from '@renderer/assets/feature/icon/tab/素材库Active.svg?react'
import 个人设置Default from '@renderer/assets/feature/icon/tab/个人设置.svg?react'
import 个人设置Active from '@renderer/assets/feature/icon/tab/个人设置Active.svg?react'
import 新增发布视频Default from '@renderer/assets/feature/icon/tab/新增视频.svg?react'
import 新增发布视频Active from '@renderer/assets/feature/icon/tab/新增视频Active.svg?react'
import 新增发布图文Default from '@renderer/assets/feature/icon/tab/新增图文.svg?react'
import 新增发布图文Active from '@renderer/assets/feature/icon/tab/新增图文Active.svg?react'
import 新增发布文章Default from '@renderer/assets/feature/icon/tab/新增文章.svg?react'
import 新增发布文章Active from '@renderer/assets/feature/icon/tab/新增文章Active.svg?react'
import 新增发布公众号Default from '@renderer/assets/feature/icon/tab/新增公众号.svg?react'
import 新增发布公众号Active from '@renderer/assets/feature/icon/tab/新增公众号Active.svg?react'
import { features } from '@renderer/infrastructure/model'

interface TabIcon {
  default: React.FC<{ className: string }>
  active: React.FC<{ className: string }>
}

export const IconMap: Record<string, TabIcon> = {
  [features.主页.name]: {
    default: 首页Default,
    active: 首页Active,
  },
  [features.发布.name]: {
    default: 发布记录Default,
    active: 发布记录Active,
  },
  [features.账号.name]: {
    default: 账号Default,
    active: 账号Active,
  },
  [features.数据.name]: {
    default: 数据Default,
    active: 数据Active,
  },
  [features.团队管理.name]: {
    default: 团队管理Default,
    active: 团队管理Active,
  },
  [features.素材.name]: {
    default: 素材库Default,
    active: 素材库Active,
  },
  [features.个人设置.name]: {
    default: 个人设置Default,
    active: 个人设置Active,
  },
  [features.视频发布.name]: {
    default: 新增发布视频Default,
    active: 新增发布视频Active,
  },
  [features.发布图文.name]: {
    default: 新增发布图文Default,
    active: 新增发布图文Active,
  },
  [features.发布文章.name]: {
    default: 新增发布文章Default,
    active: 新增发布文章Active,
  },
  [features.发布公众号.name]: {
    default: 新增发布公众号Default,
    active: 新增发布公众号Active,
  },
} as const as Record<string, TabIcon>

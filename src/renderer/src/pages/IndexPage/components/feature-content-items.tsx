import { cn } from '@renderer/lib/utils'
import { useFeatureManager } from '@renderer/infrastructure/services'
import { FeatureInstanceContext } from '@renderer/pages/IndexPage/components/feature-instance-context'
import { Fragment, useEffect } from 'react'
import { Outlet } from 'react-router-dom'

export function FeatureDynamicContentItems() {
  const { instances: openedFeatures, isActiveInstance, activeInstance } = useFeatureManager()

  useEffect(() => {
    return () => {
      document.body.style.removeProperty('pointer-events')
    }
  }, [activeInstance])

  return (
    <Fragment>
      {openedFeatures.map((featureInstance) => (
        <div
          key={featureInstance.identifier}
          className={cn('h-full w-full', isActiveInstance(featureInstance) ? 'block' : 'hidden')}
        >
          <FeatureInstanceContext.Provider value={featureInstance}>
            {featureInstance.element}
          </FeatureInstanceContext.Provider>
        </div>
      ))}
    </Fragment>
  )
}

export function FeatureContentItems() {
  const { fixedInstance } = useFeatureManager()

  return (
    <div className="flex w-0 grow flex-col">
      <div className="h-full w-full">
        <FeatureInstanceContext.Provider value={fixedInstance}>
          <Outlet />
          <FeatureDynamicContentItems />
        </FeatureInstanceContext.Provider>
      </div>
    </div>
  )
}

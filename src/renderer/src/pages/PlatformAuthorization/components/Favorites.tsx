import type { SpaceFavorite } from '@renderer/infrastructure/model'
import { SpiderAccount, WebSpace } from '@renderer/infrastructure/model'
import { useAuthorizeService, useWebSpaceManageService } from '@renderer/infrastructure/services'
import { useConfirm } from '@renderer/hooks/useConfirm'

import { Badge } from '@renderer/shadcn-components/ui/badge'
import DeleteFavoriteIcon from '@renderer/assets/space/favorite/delete.svg?react'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@renderer/shadcn-components/tooltip-without-portal'
import { useQueryClient } from '@tanstack/react-query'

export function Favorites({
  opening,
  onOpening,
  onOpened,
  account,
}: {
  opening: boolean
  onOpening: () => void
  onOpened: () => void
  account: SpiderAccount | WebSpace
}) {
  const authorizeService = useAuthorizeService()
  const webSpaceManageService = useWebSpaceManageService()
  const queryClient = useQueryClient()
  const { confirm } = useConfirm()

  async function openFavorite(account: SpiderAccount | WebSpace, favorite: SpaceFavorite) {
    try {
      if (opening) return
      onOpening()
      if (account instanceof SpiderAccount) {
        await authorizeService.openFavorite(account, favorite)
      } else if (account instanceof WebSpace) {
        await webSpaceManageService.openFavorite(account, favorite)
      }
    } finally {
      onOpened()
    }
  }

  async function removeFavorite(account: SpiderAccount | WebSpace, favoriteId: string) {
    if (
      await confirm({
        title: '',
        description: '是否确定删除？',
        confirmText: '确定删除',
        cancelText: '取消',
        type: 'destructive',
      })
    ) {
      if (account instanceof SpiderAccount) {
        await authorizeService.removeFavorite(account, favoriteId)
      } else if (account instanceof WebSpace) {
        await webSpaceManageService.removeFavorite(account.accountId, favoriteId)
      }
      queryClient.invalidateQueries({ queryKey: ['cloudAccounts'] })
    }
  }

  return (
    account.favorites.length > 0 && (
      <>
        <div className="border-b"></div>
        <div
          className={`flex flex-wrap gap-2 overflow-hidden px-3 py-2 ${opening ? 'cursor-wait bg-muted' : ''}`}
        >
          {account.favorites.map((favorite) => (
            <TooltipProvider delayDuration={0} key={favorite.id} skipDelayDuration={0}>
              <Tooltip disableHoverableContent={true}>
                <TooltipTrigger className="group/favorite max-w-[98px] grow">
                  <Badge
                    variant="outline"
                    className="flex cursor-pointer items-center gap-x-2 gap-y-1.5 rounded-md bg-white text-xs hover:border hover:border-primary hover:text-primary"
                    onClick={async () => {
                      await openFavorite(account, favorite)
                    }}
                  >
                    <div className="truncate font-normal">{favorite.name}</div>
                    <DeleteFavoriteIcon
                      className="ml-auto hidden h-3 w-3 shrink-0 group-hover/favorite:block"
                      onClick={async (e) => {
                        e.stopPropagation()
                        await removeFavorite(account, favorite.id)
                      }}
                    ></DeleteFavoriteIcon>
                  </Badge>
                </TooltipTrigger>
                <TooltipContent side="right" className="bg-[#000000b8]">
                  <span className="text-white">{favorite.name}</span>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          ))}
        </div>
      </>
    )
  )
}

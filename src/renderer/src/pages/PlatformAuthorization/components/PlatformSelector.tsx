import { OtherPlatform } from '@renderer/infrastructure/model'
import { allPlatform, platforms } from '@renderer/infrastructure/model'
import {
  GeneralPlatformItem,
  OverseasPlatformItem,
  WechatMpPlatformItem,
  WechatShiPinHaoPlatformItem,
} from './PlatformItem'
import { HelpSheet } from '../../Space/components/HelpSheet'
import { useDialogStateStore } from '@renderer/store/dialogStateStore'
import { HelpTooltop } from '@renderer/components/helpTooltop'
import { Fragment, useMemo } from 'react'
import { useMemberApi } from '@renderer/infrastructure/services/entity-service'
import { useQuery } from '@tanstack/react-query'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTrigger,
} from '@renderer/shadcn-components/ui/dialog'
import { Button } from '@renderer/shadcn-components/ui/button'
import VipTips from '@renderer/assets/vip/vipTips.png'

interface PlatformSelectorProps {
  /** 网格容器的额外CSS类名 */
  gridClassName?: string
  /** 其它账号按钮容器的额外CSS类名 */
  otherAccountClassName?: string
  /** 平台点击回调 */
  onPlatformClick?: () => void | PromiseLike<void>
  /** 微信视频号完成回调 */
  onWechatShiPinHaoFinish?: () => void
  /** 其它账号点击回调 */
  onOtherAccountClick?: () => Promise<void>
}

function CheckButton({ children }: { children: React.ReactNode }) {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <div className="PlatformSelector-check-button flex cursor-pointer items-center justify-center rounded-lg hover:bg-[#F8F8FA]">
          {children}
        </div>
      </DialogTrigger>

      <DialogContent overlayClassName="bg-black/10" className="bg-white">
        <DialogHeader>
          <DialogDescription className="text-[16px] text-[#222222]">
            授权账号数量已达到上限
          </DialogDescription>
        </DialogHeader>

        <DialogFooter>
          <DialogTrigger asChild>
            <Button variant="outline">我知道了</Button>
          </DialogTrigger>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export function PlatformSelector({
  gridClassName,
  otherAccountClassName,
  onPlatformClick,
  onWechatShiPinHaoFinish,
  onOtherAccountClick,
}: PlatformSelectorProps) {
  const accountService = useMemberApi()

  const { setAddSpaceOpen } = useDialogStateStore((state) => ({
    setAddSpaceOpen: state.setAddSpaceOpen,
  }))

  const handleOtherAccountClick = async () => {
    if (onOtherAccountClick) {
      await onOtherAccountClick()
    } else {
      setAddSpaceOpen(true)
    }
  }

  const query = useQuery({
    queryKey: ['wechat3rdPartyInvalidAccounts'],
    queryFn: () => accountService.getCurrentUserMaxAccountCount(),
    staleTime: 500,
  })

  const ClickButton = useMemo(() => {
    if (
      query.data &&
      query.data.currentAccountCount >= query.data.maxAccountCount &&
      query.data.maxAccountCount !== 0
    ) {
      return CheckButton
    }

    return Fragment
  }, [query.data])

  const domesticPlatforms = allPlatform.filter((p) => !p.isOverseas)
  const overseasPlatforms = allPlatform.filter((p) => p.isOverseas)
  return (
    <>
      <div className="relative flex flex-col gap-3">
        {/* 平台网格 */}
        <div className={'text-sm font-medium text-[#222]'}>矩阵平台</div>
        <nav className={`mb-4 grid grid-cols-9 ${gridClassName || ''}`}>
          {domesticPlatforms.map((platform) =>
            platform === OtherPlatform ? null : platform === platforms.WeiXinGongZhongHao ? (
              <ClickButton key={platform.name}>
                <WechatMpPlatformItem onClick={onPlatformClick || (() => {})} />
              </ClickButton>
            ) : platform === platforms.WeiXin ? null : platform === platforms.WeiXinShiPinHao ? (
              <ClickButton key={platform.name}>
                <WechatShiPinHaoPlatformItem onFinish={onWechatShiPinHaoFinish || (() => {})} />
              </ClickButton>
            ) : (
              <ClickButton key={platform.name}>
                <GeneralPlatformItem platform={platform} onClick={onPlatformClick || (() => {})} />
              </ClickButton>
            ),
          )}
        </nav>

        {overseasPlatforms.length > 0 && (
          <>
            <div className={'flex items-center gap-1 text-sm font-medium text-[#222]'}>
              海外平台
              <img src={VipTips} className="w-[31px]" alt="" />
              <HelpTooltop title={'海外平台仅VIP可使用，添加1个海外平台账号将扣除4个账号点数'} />
            </div>
            <nav className="grid grid-cols-9 gap-2 pb-3">
              {overseasPlatforms.map((platform) => (
                <OverseasPlatformItem
                  key={platform.name}
                  platform={platform}
                  onClick={onPlatformClick || (() => {})}
                />
              ))}
            </nav>
          </>
        )}

        {/* 其它账号按钮 */}
        <div
          className={`relative w-full cursor-pointer ${otherAccountClassName || ''}`}
          onClick={handleOtherAccountClick}
        >
          <div className="flex h-20 items-center rounded-lg bg-secondary px-5 py-4">
            <div className="h-12 w-12 flex-shrink-0 rounded-lg border bg-white">
              <div className="flex h-full w-full items-center justify-center">
                <img
                  src={OtherPlatform.bigIcon}
                  alt={OtherPlatform.name}
                  className="h-[30px] w-[30px] rounded-lg"
                />
              </div>
            </div>

            <div className="ml-3 flex flex-col">
              <span className="flex items-center gap-1 font-semibold">
                其它账号
                <HelpTooltop title="此类型账号不支持一键发布，仅支持账号管理" />
              </span>
              <div className="flex gap-1 text-xs">
                <span className="text-[#757575]">不支持的平台可以在这里添加</span>
                <div onClick={(e) => e.stopPropagation()}>
                  <HelpSheet />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

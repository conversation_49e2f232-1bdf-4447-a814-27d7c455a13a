import { useState } from 'react'
import type { Account } from '@renderer/infrastructure/model'
import {
  SpiderAccount,
  Wechat3rdPartyAccount,
  WechatGongZhongHaoOpenAccount,
  WechatShiPinHao3rdPartySubAccount,
} from '@renderer/infrastructure/model'
import { useAuthorizeService } from '@renderer/infrastructure/services'
import { eventBus } from '@renderer/infrastructure/event-bus/buses'
import { websocketEvents } from '@renderer/infrastructure/event-bus/business-events'
import { Ellipsis } from 'lucide-react'
import { useConfirm } from '@renderer/hooks/useConfirm'
import { useQueryClient } from '@tanstack/react-query'

import { RemarkDialog } from '@renderer/pages/PlatformAuthorization/RemarkDialog'

import GroupSettings from '@renderer/assets/menu/group_settings.svg?react'

import { SetGroupingDialog } from '@renderer/pages/PlatformAuthorization/SetGroupingDialog'
import RemarkIcon from '@renderer/assets/accounts/remark.svg?react'
import RemoveIcon from '@renderer/assets/accounts/remove.svg?react'

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@renderer/shadcn-components/ui/dropdown-menu'
// import { Favorites } from './Favorites'
import { AccountProfile } from '@renderer/components/AccountProfile'
import { Button } from '@renderer/shadcn-components/ui/button'

import { notifyService } from '@renderer/hooks/use-notify'
import { WechatGongZhongHaoDetailDialog } from './wechat-gong-zhong-hao-detail-dialog'
import { Wechat3rdPartySubAccounts } from '@renderer/pages/PlatformAuthorization/components/wechat3rd-party-sub-accounts'
import { WechatShiPinHao3rdPartyDetailDialog } from '@renderer/pages/PlatformAuthorization/components/wechat-3rd-party-shi-pin-hao-detail-dialog'
import { SetAccountProxyDialog } from '../SetProxyDialog'

import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@renderer/shadcn-components/ui/sheet'
import { SettingOperator } from '@renderer/components/Operator/SettingOperator'
import { VisuallyHidden } from '@radix-ui/react-visually-hidden'
// import { useMemberStore } from '@renderer/store/memberStore'
import { useCurrentTeam } from '@renderer/hooks/use-current-team'

interface AccountItemProps {
  account: Account
  removeAccount: (accountId: string) => void
  setWxMpAuthOpen: (open: boolean) => void
}

export function AccountItem({ account, removeAccount, setWxMpAuthOpen }: AccountItemProps) {
  // const isManager = useMemberStore((store) => store.isManager)
  const { confirm } = useConfirm()
  const currentTeam = useCurrentTeam()

  const authorizeService = useAuthorizeService()
  const [openSelectedAccounts, setOpenSelectedAccounts] = useState(false)
  const [remarkDialogOpen, setRemarkDialogOpen] = useState<boolean>(false)

  const [groupingDialogOpen, setGroupingDialogOpen] = useState<boolean>(false)

  const [proxyDialogOpen, setProxyDialogOpen] = useState<boolean>(false)

  const [wechatGongZhongHaoDetailDialogOpen, setWechatGongZhongHaoDetailDialogOpen] =
    useState<boolean>(false)

  const [wechat3rdShiPinHaoDetailDialogOpen, setWechat3rdShiPinHaoDetailDialogOpen] =
    useState<boolean>(false)

  const [opening, setOpening] = useState(false)

  const queryClient = useQueryClient()

  async function deleteAccount() {
    if (account instanceof WechatGongZhongHaoOpenAccount && account.isStateNormal()) {
      setWxMpAuthOpen(true)
      return
    } else if (account instanceof Wechat3rdPartyAccount) {
      if (
        await confirm({
          title: '删除账号',
          description: <Wechat3rdPartySubAccounts account={account} />,
          confirmText: '确认删除',
          cancelText: '取消',
          type: 'destructive',
          contentClassName: 'max-w-2xl',
        })
      ) {
        await authorizeService.removeAccount(account)
        removeAccount(account.accountId)
      }
      return
    } else if (
      await confirm({
        title: '删除账号',
        description: `您确定要删除账号【${account.nickName}】吗？`,
        confirmText: '删除',
        cancelText: '取消',
        type: 'destructive',
      })
    ) {
      await authorizeService.removeAccount(account)
      removeAccount(account.accountId)
    }
  }

  async function UnFreezeAccount() {
    if (
      await confirm({
        title: '恢复账号',
        description: `团队发生了权益变化，需恢复该账号的使用吗？`,
        confirmText: '恢复',
        cancelText: '取消',
        type: 'primary',
      })
    ) {
      await authorizeService.unFreezeAccount(account)
      // 主动触发账号更新事件，确保数据同步
      eventBus.emit(websocketEvents.accountUpdated, account.accountId)

      // 刷新团队数据，确保 accountCapacity 更新
      void queryClient.invalidateQueries({
        queryKey: ['getTeamDetail', currentTeam.id],
      })
    }
  }

  async function openSpace(): Promise<void> {
    if (account.isPublishable() && account instanceof SpiderAccount) {
      try {
        setOpening(true)
        await authorizeService.openAccountSpace(account)
      } catch (e) {
        if (e instanceof Error) notifyService.error(e.message)
        throw e
      } finally {
        setOpening(false)
      }
    }
  }

  const isWechatGongZhongHao = account instanceof WechatGongZhongHaoOpenAccount
  const isWechat3rdParty = account instanceof Wechat3rdPartyAccount
  const isSpiderAccount = account instanceof SpiderAccount
  const isWechatShiPinHao3rdPartySubAccount = account instanceof WechatShiPinHao3rdPartySubAccount

  return (
    <>
      <div className="h-[84px]">
        <div
          className={`group relative flex w-full flex-col items-center rounded-lg bg-[#F8F8FA] hover:z-40 hover:shadow-lg ${!isWechatGongZhongHao && !isWechat3rdParty && account.isPublishable() ? 'cursor-pointer' : ''} ${
            opening ? 'cursor-wait bg-gray-200' : ''
          }`}
          onClick={openSpace}
        >
          {account.operable && (
            <DropdownMenu>
              <DropdownMenuTrigger
                className="absolute right-2 top-2 z-40"
                onClick={(e) => {
                  e.stopPropagation()
                }}
              >
                <div className="cursor-pointer rounded border bg-card p-0.5 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
                  <Ellipsis size={16} />
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                side="right"
                align="start"
                onClick={(e) => {
                  e.stopPropagation()
                }}
                className="w-[168px] space-y-1"
              >
                {!isWechat3rdParty && (
                  <DropdownMenuItem
                    onClick={() => {
                      setGroupingDialogOpen(true)
                    }}
                  >
                    <GroupSettings />
                    <span>账号分组</span>
                  </DropdownMenuItem>
                )}

                <DropdownMenuItem
                  onClick={() => {
                    setRemarkDialogOpen(true)
                  }}
                >
                  <RemarkIcon></RemarkIcon>
                  <span>设置备注</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => deleteAccount()}>
                  <RemoveIcon></RemoveIcon>
                  <span className="text-red-500">删除</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          <div className="relative flex h-[84px] w-full min-w-10 grow p-4">
            <AccountProfile
              account={account}
              showState={true}
              iconSize="lg"
              disabled={!account.isPublishable()}
            />
            <div className="absolute right-2 top-11 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
              {account.isFreeze && (
                <Button
                  className={'z-40 h-[24px] w-[38px]'}
                  variant="outline"
                  onClick={() => UnFreezeAccount()}
                >
                  恢复
                </Button>
              )}
            </div>
          </div>
          {/* {account instanceof SpiderAccount && account.isPublishable() && (
            <div
              className="hidden w-full cursor-default group-hover:block"
              onClick={(e) => {
                e.stopPropagation()
              }}
            >
              <Favorites
                opening={opening}
                onOpening={() => {
                  setOpening(true)
                }}
                onOpened={() => {
                  setOpening(false)
                }}
                account={account}
              />
            </div>
          )} */}
        </div>

        <RemarkDialog
          open={remarkDialogOpen}
          openChange={() => {
            setRemarkDialogOpen(false)
          }}
          account={account}
          onSaveSuccess={() => {
            // 主动触发账号更新事件，确保数据同步
            eventBus.emit(websocketEvents.accountUpdated, account.accountId)
          }}
        />

        <SetGroupingDialog
          account={account}
          open={groupingDialogOpen}
          openChange={() => {
            setGroupingDialogOpen(false)
          }}
          onSuccess={() => {
            setGroupingDialogOpen(false)
            // 主动触发账号更新事件，确保数据同步
            eventBus.emit(websocketEvents.accountUpdated, account.accountId)
          }}
        />

        {isSpiderAccount && (
          <SetAccountProxyDialog
            account={account}
            open={proxyDialogOpen}
            openChange={setProxyDialogOpen}
          ></SetAccountProxyDialog>
        )}

        {isWechatGongZhongHao && (
          <WechatGongZhongHaoDetailDialog
            open={wechatGongZhongHaoDetailDialogOpen}
            openChange={() => {
              setWechatGongZhongHaoDetailDialogOpen(false)
            }}
            account={account}
          />
        )}

        {isWechatShiPinHao3rdPartySubAccount && wechat3rdShiPinHaoDetailDialogOpen && (
          <WechatShiPinHao3rdPartyDetailDialog
            open={wechat3rdShiPinHaoDetailDialogOpen}
            openChange={() => {
              setWechat3rdShiPinHaoDetailDialogOpen(false)
            }}
            account={account}
          />
        )}
      </div>

      <Sheet open={openSelectedAccounts} onOpenChange={setOpenSelectedAccounts}>
        <SheetContent className="flex flex-col gap-0 p-0">
          <SheetHeader className="border-b px-6 py-4">
            <SheetTitle>设置运营人</SheetTitle>
            <VisuallyHidden>
              <SheetDescription>请选择运营人</SheetDescription>
            </VisuallyHidden>
          </SheetHeader>
          <SettingOperator platformAccountId={account.accountId} />
        </SheetContent>
      </Sheet>
    </>
  )
}

import Youtube1 from '@renderer/assets/overseas/youtube.png'
import Youtube2 from '@renderer/assets/overseas/youtube2.png'
import Youtube3 from '@renderer/assets/overseas/youtube3.png'
export function YouTuBeGuide() {
  return (
    <>
      <div className={'flex flex-row gap-4 rounded-xl bg-[#FAFBFE] p-6'}>
        <div>
          <div
            className={'h-[110px] w-[180px]'}
            style={{ boxShadow: '0 2px 4px 0 rgba(0,0,0,0.02)' }}
          >
            <img src={Youtube1} alt="" className={'h-full w-full object-cover'} />
          </div>
          <div className={'mt-3 text-xs'}>
            1. 打开网络，点击授权按钮，跳转 YouTube授权（建议使用Chrome浏览器访问）
          </div>
        </div>

        <div>
          <div
            className={'h-[110px] w-[180px]'}
            style={{ boxShadow: '0 2px 4px 0 rgba(0,0,0,0.02)' }}
          >
            <img src={Youtube2} alt="" className={'h-full w-full object-cover'} />
          </div>
          <div className={'mt-3 text-xs'}>2. 选择需要授权的频道</div>
        </div>

        <div>
          <div
            className={'h-[110px] w-[180px]'}
            style={{ boxShadow: '0 2px 4px 0 rgba(0,0,0,0.02)' }}
          >
            <img src={Youtube3} alt="" className={'h-full w-full object-cover'} />
          </div>
          <div className={'mt-3 text-xs'}>3.勾选全部权限点击「允许」按钮完成授权。</div>
        </div>
      </div>
    </>
  )
}

import Instagram1 from '@renderer/assets/overseas/instagram.png'
import Instagram2 from '@renderer/assets/overseas/instagram2.png'
export function InstagramGuide() {
  return (
    <>
      <div className={'flex flex-row gap-4 rounded-xl bg-[#FAFBFE] p-6'}>
        <div>
          <div
            className={'h-[110px] w-[220px]'}
            style={{ boxShadow: '0 2px 4px 0 rgba(0,0,0,0.02)' }}
          >
            <img src={Instagram1} alt="" className={'h-full w-full object-cover'} />
          </div>
          <div className={'mt-3 text-xs'}>
            1. 打开网络，点击授权按钮，跳转instagram授权（建议使用Chrome浏览器访问）
          </div>
        </div>

        <div>
          <div
            className={'h-[110px] w-[220px]'}
            style={{ boxShadow: '0 2px 4px 0 rgba(0,0,0,0.02)' }}
          >
            <img src={Instagram2} alt="" className={'h-full w-full object-cover'} />
          </div>
          <div className={'mt-3 text-xs'}>
            2.勾选所有权限点击完成（如果未勾选完整权限可能导致部分功能无法使用）。
          </div>
        </div>
      </div>
    </>
  )
}

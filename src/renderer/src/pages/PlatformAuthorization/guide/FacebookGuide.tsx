import Facebook1 from '@renderer/assets/overseas/facebook.png'
import Facebook2 from '@renderer/assets/overseas/facebook2.png'
import Facebook3 from '@renderer/assets/overseas/facebook3.png'
export function FacebookGuide() {
  return (
    <>
      <div className={'flex flex-row gap-4 rounded-xl bg-[#FAFBFE] p-6'}>
        <div>
          <div
            className={'h-[110px] w-[180px]'}
            style={{ boxShadow: '0 2px 4px 0 rgba(0,0,0,0.02)' }}
          >
            <img src={Facebook1} alt="" className={'h-full w-full object-cover'} />
          </div>
          <div className={'mt-3 text-xs'}>
            1. 打开网络，点击授权按钮，跳转 YouTube授权（建议使用Chrome浏览器访问）
          </div>
        </div>

        <div>
          <div
            className={'h-[110px] w-[184px]'}
            style={{ boxShadow: '0 2px 4px 0 rgba(0,0,0,0.02)' }}
          >
            <img src={Facebook2} alt="" className={'h-full w-full object-cover'} />
          </div>
          <div className={'mt-3 text-xs'}>2. 勾选需要授权的Page账号</div>
        </div>

        <div>
          <div
            className={'h-[110px] w-[181px]'}
            style={{ boxShadow: '0 2px 4px 0 rgba(0,0,0,0.02)' }}
          >
            <img src={Facebook3} alt="" className={'h-full w-full object-cover'} />
          </div>
          <div className={'mt-3 text-xs'}>
            3.勾选所有权限点击完成（如果未勾选完整权限可能导致部分功能无法使用）。
          </div>
        </div>
      </div>
    </>
  )
}

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@renderer/shadcn-components/ui/dialog'
import { Button } from '@renderer/shadcn-components/ui/button'
import { notifyService } from '@renderer/hooks/use-notify'
import { VisuallyHidden } from '@radix-ui/react-visually-hidden'
import { Checkbox } from '@renderer/shadcn-components/ui/checkbox'
import { useState } from 'react'
import { useFacebookPageStore } from '@renderer/store/useFacebookPageStore'
import { useMutation } from '@tanstack/react-query'
import { useAuthorizeService } from '@renderer/infrastructure/services'
import { OverseasAccountAuthorizedStatus } from '@renderer/hooks/preload/team/useSocketPreload'
import { CircleAlert } from 'lucide-react'

export function FaceBookSelectPageDialog() {
  const { selectPageOpen, setSelectPageOpen } = useFacebookPageStore()
  const authorizeService = useAuthorizeService()
  const [selectedAccounts, setSelectedAccounts] = useState<number[]>([])
  const handleSelect = (index: number) => {
    if (selectedAccounts.includes(index)) {
      setSelectedAccounts(selectedAccounts.filter((i) => i !== index))
    } else {
      setSelectedAccounts([...selectedAccounts, index])
    }
  }

  const mutate = useMutation({
    mutationFn: async () => {
      await authorizeService.setOverseasAuthorized(
        OverseasAccountAuthorizedStatus.CapacityExceed,
        [],
      )
      setSelectPageOpen(false)
    },
    onSuccess: () => {
      notifyService.success('授权成功！')
    },
    onError: () => {
      notifyService.error('添加失败！')
    },
  })

  const onSubmit = async () => {
    mutate.mutate()
    setSelectPageOpen(false)
  }

  return (
    <>
      <Dialog open={selectPageOpen} onOpenChange={setSelectPageOpen}>
        <DialogContent
          className="w-fit max-w-none bg-white"
          onOpenAutoFocus={(e) => e.preventDefault()}
        >
          <VisuallyHidden>
            <DialogHeader>
              <DialogTitle></DialogTitle>
              <DialogDescription></DialogDescription>
            </DialogHeader>
          </VisuallyHidden>
          <div>
            <div
              className={
                'text-[#2222221 inline-flex items-center gap-2 rounded-md border border-orange-200 bg-[#fffadd] px-3 py-2 text-[16px] font-medium'
              }
            >
              <CircleAlert size={18} color={'#ff9f00'} />
              当前团队可添加账号点数不足，请选择要添加的Page账号
            </div>
            <div className={'mt-3 text-sm'}>可添加账号数量：1</div>

            <div className={'mt-4 max-h-[400px] overflow-y-auto'}>
              <div className={'grid grid-cols-[repeat(4,minmax(160px,1fr))] gap-4'}>
                {Array.from(Array(10).keys()).map((_, i) => (
                  <div
                    key={i}
                    className={
                      'flex cursor-pointer items-center gap-2 rounded-md bg-[#F4F6F8] px-4 py-2'
                    }
                    onClick={() => handleSelect(i)}
                  >
                    <Checkbox id="terms" checked={selectedAccounts.includes(i)} />
                    <img
                      src="https://i2.hdslb.com/bfs/face/4052f41b9589cde754ee9848f11f46db120f9abc.jpg"
                      className={'h-6 w-6 rounded-full'}
                      alt=""
                    />
                    账号
                  </div>
                ))}
              </div>
            </div>
          </div>
          <DialogFooter className={'mt-5'}>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setSelectPageOpen(false)
                notifyService.warning('取消授权！')
              }}
            >
              取消
            </Button>
            <Button type="button" onClick={onSubmit}>
              确认添加
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

import { LoadingButton } from '@renderer/components/LoadingButton'
import { useWechat3rdPartyApi } from '@renderer/infrastructure/services/entity-service'
import { Input } from '@renderer/shadcn-components/ui/input'
import { useMutation } from '@tanstack/react-query'
import { useRef } from 'react'

interface Props {
  appId: string
  uuid: string
  onError: () => void
  onFinished: () => void
}

export function WxAuthCode({ appId, uuid, onError, onFinished }: Props) {
  const inputRef = useRef<HTMLInputElement>(null)

  const { isLogin } = useWechat3rdPartyApi()

  const authMutate = useMutation({
    mutationFn: ({
      appId,
      uuid,
      captchCode,
    }: {
      appId: string
      uuid: string
      captchCode: string
    }) => isLogin(uuid, appId, captchCode),
    onError: () => {
      onError()
    },
    onSuccess: (res) => {
      if (res.status === 2) {
        onFinished()
      }
    },
  })

  return (
    <div className="flex flex-col gap-2">
      <div className="flex items-center space-x-2">
        <div className="grid flex-1 gap-2">
          <Input ref={inputRef} type="text" placeholder="请输入安全验证码(选填)" />
        </div>
        <LoadingButton
          disabled={authMutate.isPending}
          isPending={authMutate.isPending}
          type="submit"
          onClick={() => {
            authMutate.mutate({
              appId: appId,
              uuid: uuid,
              captchCode: inputRef.current?.value || '',
            })
          }}
          className="h-9"
        >
          提 交
        </LoadingButton>
      </div>
      <div className="text-xs text-muted-foreground">
        扫完授权码后如果有验证码需填写后点击提交，如果没有，则直接点击提交后完成授权
      </div>
    </div>
  )
}

import {
  Dialog,
  DialogContent,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
} from '@renderer/shadcn-components/ui/dialog'
import { useCallback, useEffect, useRef, useState } from 'react'
import type { Wechat3rdPartyAccount } from '@renderer/infrastructure/model'
import { useImmer } from 'use-immer'
import { immerable } from 'immer'
import {
  useAuthorizeService,
  useProxyQuery,
  useWechat3rdPartyInvalidAccountsQuery,
} from '@renderer/infrastructure/services'
import { useNotify } from '@renderer/hooks/use-notify'
import { useWechat3rdPartyApi } from '@renderer/infrastructure/services/entity-service/wechat-3rd-party-api'
import { VisuallyHidden } from '@radix-ui/react-visually-hidden'
import { useAuthorizeSubAccountFromWechat3rdPartyDialog } from '@renderer/pages/PlatformAuthorization/hooks/use-authorize-sub-account-from-wechat-3rd-party-dialog'
import { AccountProfile } from '@renderer/components/AccountProfile'
import { useDialogStateStore } from '@renderer/store/dialogStateStore'
import { LoadingContainer } from '@renderer/components/LoadingContainer'
import AddIcon from '@renderer/assets/accounts/add.svg?react'
import RefreshIcon from '@renderer/assets/accounts/refresh.svg?react'
import ConfirmedIcon from '@renderer/assets/accounts/confirmed.svg?react'
import { Button } from '@renderer/shadcn-components/ui/button'
import { ScrollArea } from '@renderer/shadcn-components/ui/scroll-area'
import { TimeSpan } from '@renderer/infrastructure/model/utils/time-span'
import type { AreaItem } from '@renderer/infrastructure/types'
import { ProxySelector } from './SetProxyDialog'

class ViewModel {
  [immerable] = true
  constructor(public account: Wechat3rdPartyAccount | null) {}

  public step: '选择视频号' | '选择地区' | '扫码' = '选择视频号'
  public regionCode: string | null = null
}

export function Wechat3rdPartyAuthorizeDialog() {
  const { open, setOpen, paramAccount } = useDialogStateStore((x) => ({
    open: x.authorizeWechat3rdPartyDialogOpen,
    setOpen: x.setAuthorizeWechat3rdPartyDialogOpen,
    paramAccount: x.authorizeWechat3rdPartyDialogAccount,
  }))

  const [model, setModel] = useImmer(new ViewModel(null))

  const reset = () => {
    setModel(new ViewModel(null))
  }

  const close = () => {
    setOpen(false)
    reset()
  }

  useEffect(() => {
    if (open) {
      setModel((draft) => {
        draft.account = paramAccount
        if (paramAccount) {
          draft.step = '选择地区'
        }
      })
    }
  }, [open, paramAccount, setModel])

  const { account, regionCode, step } = model

  return (
    <Dialog
      open={open}
      onOpenChange={(value) => {
        setOpen(value)
        if (!value) {
          reset()
        }
      }}
    >
      <DialogHeader></DialogHeader>
      <DialogContent className="w-auto max-w-none bg-white">
        <VisuallyHidden>
          <DialogTitle></DialogTitle>
          <DialogDescription></DialogDescription>
        </VisuallyHidden>
        {step === '选择视频号' ? (
          <SelectAccountStep
            onAccountSelected={(account) => {
              setModel((draft) => {
                draft.account = account
                draft.step = '选择地区'
              })
            }}
          />
        ) : account !== null || regionCode !== null ? (
          <ScanQrCodeStep account={account} regionCode={regionCode} onFinished={close} />
        ) : (
          <SelectRegionStep
            onRegionSelected={(regionCode) => {
              setModel((draft) => {
                draft.regionCode = regionCode
                draft.step = '扫码'
              })
            }}
            onCancel={close}
          />
        )}
      </DialogContent>
    </Dialog>
  )
}

function SelectAccountStep({
  onAccountSelected,
}: {
  onAccountSelected: (account: Wechat3rdPartyAccount | null) => void
}) {
  const {
    query: { data: invalidAccounts, isFetching },
  } = useWechat3rdPartyInvalidAccountsQuery()

  useEffect(() => {
    // 如果没有失效账号则跳过步骤
    if (!isFetching && invalidAccounts.length === 0) {
      onAccountSelected(null)
    }
  }, [invalidAccounts.length, isFetching, onAccountSelected])

  return (
    <div className="flex min-h-[100px] items-center justify-center">
      {isFetching ? (
        <LoadingContainer className="min-w-[100px]"></LoadingContainer>
      ) : (
        <div className="flex w-[762px] flex-col gap-6">
          <DialogTitle className="text-[#222222]">添加微信号</DialogTitle>
          <div
            className="flex h-[86px] cursor-pointer flex-col items-center justify-center gap-2 rounded-lg border border-dashed bg-background p-[21px]"
            onClick={() => {
              onAccountSelected(null)
            }}
          >
            <div className="flex items-center gap-2 font-semibold">
              <AddIcon className="h-4 w-4" />
              登录新账号
            </div>
            <div className="text-xs text-[#A5A5A5]">已登录过的账号，在这里授权会导致掉线。</div>
          </div>
          <div className="flex flex-col gap-3">
            <div className="font-semibold text-[#222222]">重新登录</div>
            <ScrollArea className="max-h-[270px]">
              <div className="grid grid-cols-3 gap-4">
                {invalidAccounts.map((account) => (
                  <div
                    className="group relative flex h-[72px] items-center justify-center rounded-lg bg-background px-[16px] py-[14px]"
                    key={account.accountId}
                  >
                    <AccountProfile account={account} disabled={!account.isPublishable()} />
                    {/* <div className="absolute bottom-2 right-2 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
                      <Button
                        className={'z-40 h-[24px] w-[64px]'}
                        variant="outline"
                        onClick={() => {
                          onAccountSelected(account)
                        }}
                      >
                        重新授权
                      </Button>
                    </div> */}
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        </div>
      )}
    </div>
  )
}

function SelectRegionStep({
  onRegionSelected,
  onCancel,
}: {
  onRegionSelected: (regionCode: string) => void
  onCancel: () => void
}) {
  const { data: areas, isFetching } = useProxyQuery()

  const [selection, setSelection] = useState<AreaItem[]>([])

  const onSubmit = async () => {
    if (selection.length === 0) {
      return
    } else {
      const lastItem = selection[selection.length - 1]
      onRegionSelected(lastItem.code)
    }
  }

  return (
    <div className="flex min-h-[100px] min-w-[100px] items-center justify-center">
      {isFetching ? (
        <LoadingContainer></LoadingContainer>
      ) : (
        <div className="flex w-[400px] flex-col gap-6">
          <DialogTitle className="text-[#222222]">选择授权账号的所属地区</DialogTitle>
          {
            <ProxySelector
              areas={areas}
              value={selection}
              onChange={setSelection}
              idGetter={(x) => x.code}
              textGetter={(x) => x.name}
              childrenGetter={(x) => (x.cities && x.cities.length > 0 ? x.cities : undefined)}
            />
          }
          <DialogFooter>
            <Button variant="outline" onClick={onCancel}>
              取消
            </Button>
            <Button onClick={onSubmit} disabled={selection.length === 0}>
              下一步
            </Button>
          </DialogFooter>
        </div>
      )}
    </div>
  )
}

function ScanQrCodeStep({
  account,
  regionCode,
  onFinished,
}: {
  account: Wechat3rdPartyAccount | null
  regionCode: string | null
  onFinished: () => void
}) {
  const { notifyService, modelNotify } = useNotify()
  const { getQrCode, isLogin } = useWechat3rdPartyApi()
  const authorizeService = useAuthorizeService()
  const aborted = useRef(false)

  const pollingTimeout = useRef<number | null>(null)
  const expiredTimeout = useRef<number | null>(null)

  const [getQrCodeError, setGetQrCodeError] = useState(false)
  const [qrCodeExpired, setQrCodeExpired] = useState(false)
  const [failed, setFailed] = useState(false)
  const [canceled, setCanceled] = useState(false)

  const [avatar, setAvatar] = useState<string | null>(null)
  const [nickname, setNickname] = useState<string | null>(null)
  const [confirmed, setConfirmed] = useState(false)

  const [isFetching, setIsFetching] = useState(false)

  const qrCodeFetched = useRef(false)

  const uuid = useRef<string | null>(null)
  const [base64, setBase64] = useState<string | null>(null)

  const { openDialog } = useAuthorizeSubAccountFromWechat3rdPartyDialog()

  const startQueryIsLogin = useCallback(() => {
    pollingTimeout.current = window.setTimeout(async () => {
      if (aborted.current) return
      if (uuid.current === null) return
      try {
        const response = await isLogin(uuid.current)
        if (response.status === 'scaned' && response.nickname && response.avatar) {
          setAvatar(response.avatar)
          setNickname(response.nickname)
        }
        if (response.status === 'success') {
          setConfirmed(true)
        }
        if (response.status === 'canceled') {
          setCanceled(true)
          return
        }
        if (response.isLogin && response.platformAccountId) {
          notifyService.success('登录成功！')

          const newAccount = await authorizeService.getWechat3rdPartyAccount(
            response.platformAccountId,
          )

          onFinished()

          if (account && account.accountId !== response.platformAccountId) {
            await modelNotify({
              title: '授权异常提醒',
              description: (
                <div>
                  <div>预期登录账号：{account.nickName} </div>
                  <div>实际登录账号：{newAccount.nickName}</div>
                </div>
              ),
              OKText: '我知道了',
            })
          }

          openDialog(newAccount)
          return
        }
        startQueryIsLogin()
      } catch (error) {
        setFailed(true)
        console.error('查询登录状态失败：', error)
      }
    }, 1000)
  }, [account, authorizeService, isLogin, modelNotify, notifyService, onFinished, openDialog])

  useEffect(() => {
    aborted.current = false
    return () => {
      aborted.current = true
    }
  }, [])

  const refresh = useCallback(async () => {
    try {
      if (expiredTimeout.current !== null) {
        window.clearTimeout(expiredTimeout.current)
      }
      if (pollingTimeout.current !== null) {
        window.clearTimeout(pollingTimeout.current)
      }
      setAvatar(null)
      setNickname(null)
      setConfirmed(false)
      setIsFetching(true)
      setQrCodeExpired(false)
      setFailed(false)
      setGetQrCodeError(false)
      setCanceled(false)
      const response = await getQrCode(account, regionCode)
      uuid.current = response.uuid
      setBase64(response.base64)
      expiredTimeout.current = window.setTimeout(
        () => {
          setQrCodeExpired(true)
        },
        TimeSpan.fromMinutes(4).milliseconds, // 4分钟
      )
      startQueryIsLogin()
    } catch (error) {
      setFailed(true)
      setGetQrCodeError(true)
      console.error('获取二维码失败：', error)
    } finally {
      setIsFetching(false)
    }
  }, [account, getQrCode, regionCode, startQueryIsLogin])

  useEffect(() => {
    if (qrCodeFetched.current) return
    void refresh()
    qrCodeFetched.current = true
  }, [refresh])

  return (
    <div className="flex min-h-[300px] min-w-[474px] flex-col gap-6">
      <div className="flex w-full flex-col gap-1">
        <DialogTitle className="text-[#222222]">授权微信</DialogTitle>
        <DialogDescription className="text-xs">
          <div>首次授权24小时内会掉线1次，重新扫码授权后可保持长期在线。</div>
          <div>
            若微信扫码后出现选择登录设备，请选择[iPad]，若未出现iPad选项则选择[以上都不是]。
          </div>
        </DialogDescription>
      </div>
      <div className="mb-4 flex flex-col items-center gap-2">
        <div className="h-[272px] w-[272px]">
          <div className="relative flex h-full w-full items-center justify-center rounded-lg bg-background p-2">
            {isFetching ? (
              <LoadingContainer></LoadingContainer>
            ) : (
              <>
                {base64 && <img src={base64} alt="" className="h-full w-full"></img>}
                {avatar && (
                  <div className="absolute inset-0 flex h-full w-full items-center justify-center gap-2 p-2">
                    <div className="relative h-[66px] w-[66px]">
                      <img src={avatar} alt="" className="h-full w-full rounded-full"></img>
                      {confirmed && (
                        <div className="absolute -bottom-1 -right-1 flex items-center justify-center rounded-full shadow-lg">
                          <ConfirmedIcon></ConfirmedIcon>
                        </div>
                      )}
                    </div>
                  </div>
                )}
                {(qrCodeExpired || failed || canceled) && (
                  <>
                    <div className="absolute inset-0 flex h-full w-full items-center justify-center bg-white opacity-90"></div>
                    <div className="absolute inset-0 flex h-full w-full items-center justify-center">
                      <div
                        className="flex h-16 w-16 cursor-pointer items-center justify-center rounded-full bg-white shadow-lg"
                        onClick={refresh}
                      >
                        <RefreshIcon className="h-9 w-9"></RefreshIcon>
                      </div>
                    </div>
                  </>
                )}
              </>
            )}
          </div>
        </div>
        <div className="flex items-center justify-center">
          {getQrCodeError ? (
            '获取二维码失败，点击重试'
          ) : qrCodeExpired ? (
            '二维码已过期, 点击刷新'
          ) : failed ? (
            '扫码遇到错误，请重试'
          ) : canceled ? (
            '扫码已取消'
          ) : nickname ? (
            <div className="flex flex-col items-center">
              <div>{nickname}</div>
              <div className="text-xs text-[#A5A5A5]">扫描成功，在微信中点击允许即可登录</div>
            </div>
          ) : (
            '使用微信扫一扫'
          )}
        </div>
      </div>
    </div>
  )
}

import { useEffect, type ReactNode } from 'react'
import { useSocketPreload } from '@renderer/hooks/preload/team/useSocketPreload'
import { useNewMessageCheck } from '@renderer/hooks/preload/team/useNewMessageCheck'
import { useMemberPreload } from '@renderer/hooks/preload/team/useMemberPreload'
import { LoadingScreen } from '@renderer/components/LoadingScreen'
import { useBrowserSessionDirectoryCleanup } from '@renderer/hooks/preload/team/use-browser-session-directory-cleanup'
import { useSpaceFavIconHandler } from '@renderer/hooks/preload/team/use-space-fav-icon-handler'
import { useAccountAuthorizeHandler } from '@renderer/hooks/preload/team/use-account-authorize-handler'
import { useAccountSessionHandler } from '@renderer/hooks/preload/team/use-account-session-handler'
import { usePushingHandler } from '@renderer/hooks/preload/team/use-pushing-handler'
import { useUserEventHandler } from '@renderer/hooks/preload/team/use-user-event-handler'
import { useTeamsPreload } from '@renderer/hooks/preload/team/use-teams-preload'
import { useDexieUtils } from '@renderer/utils/dexie'
import { useUserExitHandler } from '@renderer/hooks/preload/team/use-user-exit-handler'
import { useBrowserManage } from '@renderer/hooks/preload/team/use-browser-manage'
import { useWebSpaceManage } from '@renderer/hooks/preload/team/use-space-manage'
import { useAccountSpaceManage } from '@renderer/hooks/preload/team/use-account-space-manage'
import { useTeamVersionPreload } from '@renderer/hooks/preload/team/use-team-version-preload'
import { useSpaceFavoritePreload } from '@renderer/hooks/preload/team/use-space-favorite-preload'
import { useStorePreload } from '@renderer/hooks/preload/team/use-store-preload'
import { useAssertLibraryHandler } from '@renderer/hooks/preload/team/use-assert-library-handler'
import { useCurrentTeamPreload } from '@renderer/hooks/preload/team/use-current-team-preload'

import { useAppTaskPublish } from '@renderer/hooks/preload/team/use-app-task-publish'
import { useNotificationMessageHandler } from '@renderer/hooks/preload/team/use-notification-message-handler'
import { useOverviewReport } from '@renderer/hooks/preload/team/use-overview-report'
import { useIndexPagePreload } from '@renderer/hooks/preload/team/use-index-page-preload'
import { useSessionUtils } from '@renderer/utils/session'
import { eventBus } from '@renderer/infrastructure/event-bus/buses'
import { authenticationEvents } from '@renderer/infrastructure/event-bus/business-events'
import { useVipDialog } from '@renderer/pages/Vip/vipDialogProvider'
import { alertBaseManager } from '../alertBase'
import { useSystemNotifyHandler } from '@renderer/hooks/preload/team/use-system-notify-handler'
import { useGuider } from '@renderer/hooks/preload/team/use-guider'
import { useEventBusUtils } from '@renderer/utils/event-bus'
import { useWebSpaceAuthorizeHandler } from '@renderer/hooks/preload/team/use-web-space-authorize-handler'

export function CurrentTeamContextPreload({ children }: { children: ReactNode }) {
  const { ready: memberReady } = useMemberPreload()

  const { ready: currentTeamReady } = useCurrentTeamPreload()

  useStorePreload()

  useUserExitHandler()

  useDexieUtils()

  useSessionUtils()

  useEventBusUtils()

  useSocketPreload()

  useNewMessageCheck()

  useBrowserSessionDirectoryCleanup()

  useSpaceFavIconHandler()

  useSpaceFavoritePreload()

  useAccountAuthorizeHandler()

  useWebSpaceAuthorizeHandler()

  useOverviewReport()

  useAccountSessionHandler()

  usePushingHandler()

  useNotificationMessageHandler()

  useSystemNotifyHandler()

  useUserEventHandler()

  useTeamsPreload()

  useAppTaskPublish()

  useBrowserManage()

  useWebSpaceManage()

  useAccountSpaceManage()

  useTeamVersionPreload()

  useAssertLibraryHandler()

  useIndexPagePreload()

  useGuider()

  const { show } = useVipDialog()

  useEffect(() => {
    return eventBus.on(authenticationEvents.needVip, async (message: string) => {
      alertBaseManager.open({
        title: 'VIP权限',
        description: message,
        okText: '去开通',
        onSubmit: show,
      })
    })
  }, [show])

  return memberReady && currentTeamReady ? children : <LoadingScreen tips="正在加载团队数据" />
}

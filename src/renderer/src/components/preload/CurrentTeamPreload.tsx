import type { ReactNode } from 'react'
import { useEffect } from 'react'
import { useTeamsStore } from '@renderer/store/teamsStore'
import { useContextStore } from '@renderer/store/contextStore'
import { LoadingScreen } from '@renderer/components/LoadingScreen'
import { useUserService } from '@renderer/infrastructure/services'

export function CurrentTeamPreload({ children }: { children: ReactNode }) {
  const userService = useUserService()
  const teams = useTeamsStore((state) => state.teams)
  const { userInfo, setCurrentTeamId, currentTeamId } = useContextStore((state) => ({
    userInfo: state.userInfo,
    setCurrentTeamId: state.setCurrentTeamId,
    currentTeamId: state.currentTeamId,
  }))

  useEffect(() => {
    async function init() {
      if (!userInfo) return

      // 从当前登录用户信息中获取 latestTeamId（用户最后一次使用的团队）
      const latestTeamId = userInfo.latestTeamId
      if (latestTeamId) {
        const team = teams.find((x) => x.id === latestTeamId)
        if (team) {
          setCurrentTeamId(team.id)
          return
        }
      }

      //默认选择第一个团队
      if (teams.length > 0) {
        await userService.switchTeam(teams[0].id)
        return
      }

      setCurrentTeamId(null)
    }

    void init()

    return () => {
      setCurrentTeamId(null)
    }
  }, [userInfo, setCurrentTeamId, teams, userService])

  const ready = !!currentTeamId

  return ready ? children : <LoadingScreen tips="正在加载当前团队" />
}

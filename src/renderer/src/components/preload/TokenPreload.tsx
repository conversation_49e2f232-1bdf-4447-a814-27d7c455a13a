import type { ReactNode } from 'react'
import { useEffect } from 'react'
import { localStorageService } from '@renderer/infrastructure/services/storage-service'
import { useContextStore } from '@renderer/store/contextStore'
import { LoadingScreen } from '@renderer/components/LoadingScreen'

export function TokenPreload({ children }: { children: ReactNode }) {
  const { setToken, token } = useContextStore((state) => ({
    setToken: state.setToken,
    token: state.token,
  }))

  useEffect(() => {
    ;(async () => {
      // 直接使用 localStorageService 而不是 globalStorageService，避免依赖 userInfo
      const token = localStorageService.getItem<string>('token')
      setToken(token)
    })()

    return () => {
      setToken(null)
    }
  }, [setToken])
  const ready = !!token

  return ready ? children : <LoadingScreen tips="正在加载Token" />
}

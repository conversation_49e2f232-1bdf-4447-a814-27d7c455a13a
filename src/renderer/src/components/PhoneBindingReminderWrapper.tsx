import { PhoneBindingReminder } from './PhoneBindingReminder'
import { usePhoneBindingReminder } from '@renderer/hooks/usePhoneBindingReminder'

export const PhoneBindingReminderWrapper = () => {
  const { showReminder, hideReminder } = usePhoneBindingReminder()

  const handleNavigateToSettings = () => {
    // 这里可以添加导航到用户设置页面并选中账号安全菜单的逻辑
    // 由于应用使用特性管理器，具体的菜单选择逻辑需要在个人设置页面内部处理
  }

  return (
    <PhoneBindingReminder
      open={showReminder}
      onOpenChange={hideReminder}
      onNavigateToSettings={handleNavigateToSettings}
    />
  )
}

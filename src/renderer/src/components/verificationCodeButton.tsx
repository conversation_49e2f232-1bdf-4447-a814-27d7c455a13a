import { Button } from '@renderer/shadcn-components/ui/button'
import { useMemo, useState, useCallback, useEffect } from 'react'
import { ReloadIcon } from '@radix-ui/react-icons'
import { phoneRegex } from '@renderer/utils/zod'
import { cn } from '@renderer/lib/utils'
import { useFormContext, useWatch } from 'react-hook-form'
import { useAliyunCaptcha } from '@renderer/hooks/useAliyunCaptcha'

export function VerificationCodeButton({
  sence = 'auth',
  phoneName = 'phone',
  className,
}: {
  sence?:
    | 'auth'
    | 'resetPassword'
    | 'changePhone'
    | 'checkPhone'
    | 'bindPhone'
    | 'bindAccount'
    | 'wxBindAccount'
  className?: string
  phoneName?: string
}) {
  const [countdown, setCountdown] = useState(0) // 倒计时秒数
  const [isButtonDisabled, setIsButtonDisabled] = useState(false) // 按钮状态
  const { control } = useFormContext()
  const phone = useWatch({ control, name: phoneName })

  // 生成唯一的按钮ID - 使用更稳定的ID生成方式
  const buttonId = useMemo(() => {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 11)
    return `captcha-btn-${timestamp}-${random}`
  }, [])

  // 使用阿里云验证码 hook
  const { isSending, isReady, triggerCaptcha } = useAliyunCaptcha({
    buttonId,
    phone,
    sence,
    onSuccess: (result) => {
      // 测试验证码自动填充等逻辑可以在这里处理
      console.log('验证码发送成功，测试验证码:', result)
    },
    onCountdownStart: (seconds) => {
      setIsButtonDisabled(true)
      setCountdown(seconds)
    },
    enabled: true,
  })

  const disabled = useMemo(
    () => !phoneRegex.test(phone) || isButtonDisabled || isSending || !isReady,
    [phone, isButtonDisabled, isSending, isReady],
  )

  // 倒计时逻辑
  useEffect(() => {
    if (countdown > 0) {
      const timer = setInterval(() => {
        setCountdown((prev) => prev - 1)
      }, 1000) // 每秒更新一次
      return () => clearInterval(timer) // 清除定时器
    } else {
      setIsButtonDisabled(false) // 倒计时结束，启用按钮
    }
    return undefined
  }, [countdown]) // 当 countdown 变化时运行

  const requestVerificationCode = useCallback(() => {
    console.log('点击获取验证码按钮，disabled:', disabled)

    if (disabled) {
      console.log('按钮被禁用，取消操作')
      return
    }

    // 使用 hook 提供的触发方法
    triggerCaptcha()
  }, [disabled, triggerCaptcha])
  return (
    <>
      {/* 验证码容器元素 */}
      <div id="captcha-element" style={{ display: 'none' }}></div>

      <div
        className={cn('absolute right-2 top-0 flex h-full items-center justify-center', className)}
      >
        <Button
          id={buttonId}
          onClick={(e) => {
            e.preventDefault()
            e.stopPropagation()
            console.log('按钮被点击，事件:', e)
            requestVerificationCode()
          }}
          disabled={disabled}
          type="button"
          className="h-full px-2"
          variant="link"
        >
          {(isSending || !isReady) && <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />}
          {isButtonDisabled ? `重新获取 (${countdown}s)` : !isReady ? '初始化中...' : '获取验证码'}
        </Button>
      </div>
    </>
  )
}

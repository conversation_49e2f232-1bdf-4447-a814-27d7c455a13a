import { Avatar, AvatarImage, AvatarFallback } from '@renderer/shadcn-components/ui/avatar'
import { useState } from 'react'
import { useUserService } from '@renderer/infrastructure/services'
import { useNavigate } from 'react-router-dom'
import './user-avatar.css'
import { useContextStore } from '@renderer/store/contextStore'
// import { updateDialogManager } from '@renderer/pages/Update'

// import { useUpdateStore } from '@renderer/store/updateStore'

import { useVipDialog } from '@renderer/pages/Vip/vipDialogProvider'

import { useFeatureManager } from '@renderer/infrastructure/services'
import { features } from '@renderer/infrastructure/model/features/features'
import { useConfirm } from '@renderer/hooks/useConfirm'
import { VipExchangeDialog } from '../vip/VipExchangeDialog'
import { useVipDetail } from '@renderer/hooks/preload/use-vip'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@renderer/shadcn-components/dropdown-menu'
import VIP1Icon from '@renderer/assets/vip/VIP1.svg'
import VIP2Icon from '@renderer/assets/vip/VIP2.svg'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@renderer/shadcn-components/ui/tooltip'
import { DateUtils } from '@renderer/utils/date-utils'
import Free2Icon from '@renderer/assets/vip/free.svg?react'
import { TeamSwitch } from '@renderer/pages/TeamManager/components/team/TeamSwitch'

export function UserAvatar({ expanded }: { expanded: boolean }) {
  const currentTeam = useContextStore((state) => state.currentTeam)
  const userService = useUserService()
  const navigate = useNavigate()
  const { openFeature } = useFeatureManager()
  const userInfo = useContextStore((state) => state.userInfo)
  const [open, setOpen] = useState(false)
  const { confirm } = useConfirm()
  const { isVip, expiredAt } = useVipDetail()
  const { show, upgrade } = useVipDialog()
  /**
   * 退出登录
   */
  async function logout() {
    if (
      await confirm({
        title: '退出登录',
        type: 'destructive',
        description: '确定要退出登录吗？',
        confirmText: '退出登录',
        cancelText: '取消',
      })
    ) {
      userService.logout()
      navigate('/login')
    }
  }

  // const status = useUpdateStore((state) => state.status)

  const onGoUserInfo = () => {
    setOpen(false)
    openFeature(features.个人设置)
  }

  return (
    <VipExchangeDialog>
      {() => (
        <DropdownMenu open={open} onOpenChange={setOpen}>
          <DropdownMenuTrigger asChild>
            <div className="user-avatar-wrapper relative">
              <Avatar className="z-10 h-8 w-8 overflow-hidden">
                <AvatarImage src={userInfo?.avatarUrl} />
                <AvatarFallback></AvatarFallback>
              </Avatar>
              {isVip && !expanded && <img src={VIP1Icon} className="absolute bottom-0 z-10" />}
            </div>
          </DropdownMenuTrigger>
          {isVip && expanded && (
            <TooltipProvider>
              <Tooltip delayDuration={0}>
                <TooltipTrigger asChild>
                  <div onClick={upgrade} className="cursor-pointer">
                    <img src={VIP2Icon} alt="" />
                  </div>
                </TooltipTrigger>
                <TooltipContent className="bg-[#000000b5] text-[#ffffff]" side="bottom">
                  到期时间:{DateUtils.formatDate(expiredAt, 'yyyy-MM-dd')}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
          {!isVip && expanded && <Free2Icon onClick={show} className="cursor-pointer" />}
          <DropdownMenuContent
            align="start"
            side="right"
            onPointerDownOutside={() => {
              console.log('onPointerDownOutside')
            }}
            className="electron-no-drag w-60 space-y-2 overflow-visible rounded-xl !bg-profile bg-[length:100%_50%] bg-right-top bg-no-repeat px-3 py-2.5 text-secondary-foreground"
          >
            <div className="flex flex-col gap-3 py-2">
              <DropdownMenuLabel className="">
                <div className="flex gap-2">
                  <Avatar className="h-12 w-12">
                    <AvatarImage
                      src={userInfo?.avatarUrl}
                      style={{
                        transform: 'scale(1.1)',
                        transformOrigin: '50% 50%',
                      }}
                    />
                    <AvatarFallback>{userInfo?.nickName?.substring(0, 2)}</AvatarFallback>
                  </Avatar>
                  <div className="flex flex-1 flex-col justify-between">
                    <div className="flex items-center text-base">
                      <span className="w-0 flex-1 overflow-hidden text-ellipsis text-nowrap">
                        {userInfo?.nickName}
                      </span>
                    </div>
                    <div className="flex items-center justify-between gap-1 text-sm font-normal text-muted-foreground">
                      <span className="line-clamp-1 flex-1">{userInfo?.phone}</span>
                    </div>
                  </div>
                </div>
              </DropdownMenuLabel>
              {/* <VipComponent /> */}
            </div>

            {currentTeam && (
              <div className="px-2 py-1.5">
                <div className="flex w-full items-center">
                  <TeamSwitch />
                </div>
              </div>
            )}

            <DropdownMenuItem onClick={onGoUserInfo}>个人设置</DropdownMenuItem>
            {/* {render} */}
            {/* <DropdownMenuItem
              className="relative"
              onClick={() => {
                updateDialogManager.open(true)
              }}
            >
              检测更新
              {['pending', 'downloading'].includes(status) && (
                <div className="absolute left-[60px] top-1 h-2.5 w-2.5 rounded-full border border-white bg-red-600"></div>
              )}
            </DropdownMenuItem> */}
            <DropdownMenuSeparator />
            <DropdownMenuItem variant="destructive" onClick={logout}>
              退出登录
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </VipExchangeDialog>
  )
}

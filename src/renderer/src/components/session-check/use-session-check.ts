import { useAuthorizeService } from '@renderer/infrastructure/services'
import { useImmer } from 'use-immer'
import type { SessionState } from '@common/structure'
import { useCallback, useMemo, useRef } from 'react'
import type { SpiderAccount } from '@renderer/infrastructure/model'
import pLimit from 'p-limit'

const limit = pLimit(20)

type CheckingState = 'checking' | 'error' | 'finished'

export function useSessionCheck() {
  const authorizeService = useAuthorizeService()
  const [checkingStates, setCheckingStates] = useImmer<Map<string, CheckingState>>(new Map())
  const checkingResults = useRef<Map<string, SessionState | null>>(new Map())

  const check = useCallback(
    (accounts: SpiderAccount[]) => {
      const accountsToCheck = accounts.filter((account) => {
        // 跳过已检测正常的账号
        const state = checkingResults.current.get(account.accountId)
        return state !== '正常'
      })

      setCheckingStates(new Map(accountsToCheck.map((account) => [account.accountId, 'checking'])))

      return accountsToCheck.map((account) =>
        limit(async () => {
          try {
            const { loginStatus } = await authorizeService.checkAccount(account.accountId)

            checkingResults.current?.set(account.accountId, loginStatus ? '正常' : '已失效')

            setCheckingStates((draft) => {
              draft.set(account.accountId, 'finished')
            })
            return { account, result: loginStatus }
          } catch (error) {
            setCheckingStates((draft) => {
              draft.set(account.accountId, 'error')
            })
            throw error
          }
        }),
      )
    },
    [authorizeService, setCheckingStates],
  )

  const checkingCount = useMemo(
    () => Array.from(checkingStates.values()).filter((state) => state === 'checking').length,
    [checkingStates],
  )

  const finishedCount = checkingStates.size - checkingCount

  return useMemo(
    () => ({
      check,
      isChecking: checkingStates.size !== finishedCount,
      totalCount: checkingStates.size,
      finishedCount: finishedCount,
    }),
    [check, checkingStates.size, finishedCount],
  )
}

import MessageMenuIcon from '@renderer/assets/messages/menu-icon.svg?react'
import { Button } from '@renderer/shadcn-components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@renderer/shadcn-components/ui/popover'
import SauryTooltip from '@renderer/components/tooltip'
import { ScrollArea } from '@renderer/shadcn-components/ui/scroll-area'
import { datetimeService, useTeamService } from '@renderer/infrastructure/services'
import { useMessageManagerService } from '@renderer/infrastructure/services/application-service/message/message-manager-service'
import { useInfiniteQuery, useQueryClient } from '@tanstack/react-query'
import { useMessageStore } from '@renderer/store/messageStore'
import type { Message } from '@renderer/infrastructure/model/message'
import { useEffect, useRef } from 'react'
import MessageIcon from '@renderer/assets/messages/message-icon.png'
import EmptyMessage from '@renderer/assets/messages/empty.svg?react'

export function MessagesPopover({ expanded }: { expanded: boolean }) {
  const { hasNewMessage, setHasNewMessage } = useMessageStore((state) => ({
    hasNewMessage: state.hasNewMessage,
    setHasNewMessage: state.setHasNewMessage,
  }))

  return (
    <Popover>
      <PopoverTrigger asChild>
        <div className="flex items-center">
          <SauryTooltip tooltip="客服">
            <Button
              variant="ghost"
              size={expanded ? 'sm' : 'small_icon'}
              className="gap-1 px-2 hover:bg-mainAccent"
              onClick={() => setHasNewMessage(false)}
            >
              <div className="relative">
                <MessageMenuIcon />
                {hasNewMessage && (
                  <div className="absolute right-1 top-1 h-2.5 w-2.5 rounded-full border border-white bg-red-600"></div>
                )}
              </div>
            </Button>
          </SauryTooltip>
        </div>
      </PopoverTrigger>
      <PopoverContent side="right" align="end" className="w-[440px] p-0">
        <div className="px-5 py-4 font-semibold">消息通知</div>
        <div className="h-[1px] bg-gray-200"></div>
        <ScrollArea className="h-[500px] grow overflow-y-auto">
          <MessageList />
        </ScrollArea>
      </PopoverContent>
    </Popover>
  )
}

export function MessageList() {
  const teamService = useTeamService()
  const messageManagerService = useMessageManagerService()
  const queryClient = useQueryClient()
  const { setShouldRefresh } = useMessageStore((state) => ({
    setShouldRefresh: state.setShouldRefresh,
  }))

  const fetchMessages = async ({
    pageParam = 1,
  }): Promise<{ data: Message[]; cursor: number; end: boolean }> => {
    const result = await messageManagerService.getMessageList({
      page: pageParam,
      size: 10,
    })
    return { data: result.data, cursor: result.page, end: result.totalPage === pageParam }
  }

  const { data, fetchNextPage, hasNextPage, isFetching } = useInfiniteQuery({
    queryKey: ['messageList'],
    queryFn: fetchMessages,
    getNextPageParam: (lastPage) => {
      if (lastPage.end) return undefined // 如果是最后一页，则不再获取下一页
      return lastPage.cursor + 1 // 否则，返回下一页的页码
    },
    initialPageParam: 1, // 初始页码
  })

  const messageList = data?.pages.flatMap((page) => page.data) ?? []

  const observerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(async (entries) => {
      if (entries[0].isIntersecting && !isFetching) {
        await fetchNextPage()
      }
    })

    const current = observerRef.current
    if (current) {
      observer.observe(current)
    }

    return () => {
      if (current) {
        observer.unobserve(current)
      }
      observer.disconnect()
    }
  }, [fetchNextPage, hasNextPage, isFetching])

  const RefreshMessageList = async () => {
    await queryClient.invalidateQueries({ queryKey: ['messageList'] })
  }

  // 同意加入团队 发送事件刷新团队列表
  const handleApprove = async (message: Message) => {
    await teamService.approveTeamJoinApply(message.bizArgs.proposalId)
    await RefreshMessageList()
    setShouldRefresh(true)
  }

  const handleReject = async (message: Message) => {
    await teamService.rejectTeamJoinApply(message.bizArgs.proposalId)
    await RefreshMessageList()
  }

  const handleApproveJoinTeam = async (message: Message) => {
    await teamService.approveJoinTeam(message.bizArgs.invitationId)
    await RefreshMessageList()
  }

  const handleRejectJoinTeam = async (message: Message) => {
    await teamService.rejectJoinTeam(message.bizArgs.invitationId)
    await RefreshMessageList()
  }

  return (
    <div className="flex h-[500px] grow flex-col px-4">
      {messageList.length === 0 && (
        <div className="flex h-full flex-col justify-center text-center text-sm text-gray-500">
          <div className={'text-center'}>
            <EmptyMessage className={'inline-block'} />
          </div>
          <div className={'pt-2'}>暂无消息通知</div>
        </div>
      )}

      {messageList.map((item, index) => (
        <div key={item.id} className={`${index !== messageList.length - 1 ? 'border-b' : ''}`}>
          <div className="flex items-center rounded-md py-4 transition duration-300">
            <div className="h-8 w-8 shrink-0 rounded-full bg-[#e6e5f3]">
              <img
                style={{ margin: '0 auto', transform: 'translateY(25%)' }}
                width={20}
                src={MessageIcon}
                alt="message icon"
              />
            </div>
            <div className="ml-3 grow overflow-auto">
              <div className="text-sm font-medium text-gray-800">{item.title}</div>
              <div className="mt-1 text-xs font-normal text-gray-600">
                {datetimeService.formatToSeconds(item.createdAt)}
              </div>
            </div>
            <div className="ml-2 flex shrink-0 flex-row items-center gap-2">
              {item.type === 'invitation' && (
                <>
                  {item.bizState === 'approved' && <JoinTeamButton />}
                  {item.bizState === 'rejected' && <RejectedButton />}
                  {item.bizState !== 'approved' && item.bizState !== 'rejected' && (
                    <>
                      <Button size={'sm'} onClick={() => handleApproveJoinTeam(item)}>
                        加入
                      </Button>
                      <Button
                        variant="outline"
                        size={'sm'}
                        onClick={() => handleRejectJoinTeam(item)}
                      >
                        拒绝
                      </Button>
                    </>
                  )}
                </>
              )}
              {item.type === 'proposal' && (
                <>
                  {item.bizState === 'approved' && <ApprovedButton />}
                  {item.bizState === 'rejected' && <RejectedButton />}
                  {item.bizState !== 'approved' && item.bizState !== 'rejected' && (
                    <>
                      <Button size={'sm'} onClick={() => handleApprove(item)}>
                        同意
                      </Button>
                      <Button variant="outline" size={'sm'} onClick={() => handleReject(item)}>
                        拒绝
                      </Button>
                    </>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      ))}
      {messageList.length > 0 && (
        <div ref={observerRef} style={{ height: '1px', width: '1px' }}></div>
      )}
    </div>
  )
}

function RejectedButton() {
  return <span className="rounded bg-[#eeeeee] px-[12px] py-[6px] text-xs">已拒绝</span>
}

function ApprovedButton() {
  return <span className="rounded bg-[#eeeeee] px-[12px] py-[6px] text-xs">已同意</span>
}

function JoinTeamButton() {
  return <span className="rounded bg-[#eeeeee] px-[12px] py-[6px] text-xs">已加入</span>
}

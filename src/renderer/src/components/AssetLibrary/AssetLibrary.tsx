import { useConfirm } from '@renderer/hooks/useConfirm'
import {
  EAssetLibraryFileState,
  useAssetLibraryService,
} from '@renderer/infrastructure/services/application-service/assetLibrary-service'
import { useSystem } from '@renderer/pages/context'
import { Button } from '@renderer/shadcn-components/ui/button'
import { CardFooter } from '@renderer/shadcn-components/ui/card'
import { Upload } from 'lucide-react'
import { Fragment, useCallback, useEffect, useRef, useState, useSyncExternalStore } from 'react'
import { AssetLibraryMenu } from './AssetLibraryMenu'
import { AssetLibraryItem } from './AssetLibraryItem'
import EmptyIcon from '@renderer/assets/empty.png'
import { AssetLibraryTask } from './AssetLibraryTask'
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
} from '@renderer/shadcn-components/ui/pagination'
import type { IAssetLibraryItem } from '@renderer/infrastructure/model'
import { uiEvents } from '@common/events/ui-events'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@renderer/shadcn-components/ui/dialog'
import type { EAssetLibraryFileType } from '@renderer/components/AssetLibrary/AssetLibraryMenuTypes'
import { useCurrentFeatureInstanceActivated } from '@renderer/infrastructure/services/application-service/feature/use-current-feature-instance-activated'
import { eventBus } from '@renderer/infrastructure/event-bus/buses'
import { assertLibraryEvents } from '@renderer/infrastructure/event-bus/business-events'
import { Checkbox } from '@renderer/shadcn-components/ui/checkbox'
import { useApiMutation } from '@renderer/hooks/useApiQuery'
import { ScrollArea } from '@renderer/shadcn-components/ui/scroll-area'

const pageSize = 50
{
  /* <div className="flex h-full w-full flex-col bg-[#fff] text-black"> */
}
const AssetLibraryCardWapper = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="flex h-full flex-col text-black">
      <div className="relative flex h-full w-full flex-col rounded-lg bg-white shadow-sm">
        {children}
      </div>
    </div>
  )
}

const AssetLibraryWapper = ({ children }: { children: React.ReactNode }) => {
  return (
    <Fragment>
      <DialogTitle className="hidden" />
      {children}
    </Fragment>
  )
}

interface BatchDeleteDialogProps {
  open: boolean
  setOpen: (open: boolean) => void
  successCount: number
  errorCount: number
}
const BatchDeleteDialog = ({ open, setOpen, successCount, errorCount }: BatchDeleteDialogProps) => {
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="w-[416px] max-w-none bg-white">
        <DialogHeader>
          <DialogTitle>操作结果</DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>
        {errorCount === 0 ? (
          <div>删除成功</div>
        ) : (
          <div>
            <div>
              成功<span className={'px-1 text-green-500'}>{successCount}</span>个
              <span className={'ml-3 px-1 text-red-500'}>{errorCount}</span>
              个操作失败。
            </div>
            <div className={'mt-1 text-orange-500'}>联系管理员或创建人，删除失败的素材。</div>
            <div className="mt-4 flex justify-end">
              <Button className="gap-2" onClick={() => setOpen(false)}>
                <span>确定</span>
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}

export function AssetLibrary({
  isMultiple,
  type,

  onSelect,
  onClose,
}: {
  isMultiple?: boolean
  type?: EAssetLibraryFileType

  onSelect?: (value: IAssetLibraryItem[]) => void
  onClose?: () => void
}) {
  const containerRef = useRef({} as HTMLDivElement)

  const assetLibraryService = useAssetLibraryService()
  const { onSetDialogSub } = useSystem()

  const [isDownload, setIsDownload] = useState(false)
  const [list, setList] = useState<IAssetLibraryItem[]>([])
  const [checkMap, setCheckMap] = useState<Record<string, IAssetLibraryItem>>({})
  const [actionType, setActionType] = useState<'default' | 'batch'>('default')
  const [batchChecked, setBatchChecked] = useState<boolean | 'indeterminate'>(false)
  const [batchDeleteDialogOpen, setBatchDeleteDialogOpen] = useState(false)
  const [batchDeleteResult, setBatchDeleteResult] = useState<{
    deleteSuccess: number
    deleteFail: number
  }>({ deleteSuccess: 0, deleteFail: 0 })

  const { confirm } = useConfirm()

  useCurrentFeatureInstanceActivated(
    useCallback(() => {
      eventBus.emit(assertLibraryEvents.capacityUpdated)
    }, []),
  )

  const isManger = !!onSelect || isDownload

  const pageInfo = useRef({
    size: pageSize,
    page: 1,
    totalPage: 1,
  })

  const currentSelect = useRef({
    type: type ?? '',
    groupId: '',
  })

  const uploadList = useSyncExternalStore(
    assetLibraryService.subscribe,
    assetLibraryService.getUplodaFiles,
  )

  const getList = useCallback(
    async (data: Parameters<typeof assetLibraryService.getAssetMeterials>[0]) => {
      const res = await assetLibraryService.getAssetMeterials(data)
      pageInfo.current.totalPage = res.totalPage
      pageInfo.current.page = res.page

      setList(res.data)
    },
    [assetLibraryService],
  )

  useEffect(() => {
    const init = async () => {
      await getList({
        groupId: '',
        type: type ?? '',
        page: 1,
        size: pageSize,
      })
    }

    void init()
  }, [assetLibraryService, getList, type])

  useEffect(() => {
    const res = uploadList.filter(
      (item) =>
        item.state !== EAssetLibraryFileState.Idle &&
        item.state !== EAssetLibraryFileState.Uploading,
    )

    if (res.length === uploadList.length) {
      void getList({
        ...currentSelect.current,
        size: pageSize,
        page: 1,
      })
    }
  }, [getList, uploadList])

  const onPreview = (item: IAssetLibraryItem) => {
    onSetDialogSub((dialogMap) => ({
      ...dialogMap.assetLibraryPreview,
      elementProps: {
        list,
        initIndex: list.findIndex((x) => x.id === item.id),
      },
      dialogContentProps: {
        className:
          'AssetLibraryPreview-Root bg-[#040404] p-0 border-none overflow-hidden outline-none max-w-none w-[auto]',
      },
    }))
  }

  const onUploadAsset = () => {
    onSetDialogSub((dialogMap) => ({
      ...dialogMap.assetLibraryUpload,
      dialogContentProps: {
        className: 'w-[auto]',
      },
    }))
  }

  const onCheckItem = (item: IAssetLibraryItem, check: boolean) => {
    if (isMultiple || isDownload) {
      if (check) {
        checkMap[item.id] = item
      } else {
        delete checkMap[item.id]
      }
      setCheckMap({ ...checkMap })
    } else {
      setCheckMap({ [item.id]: item })
    }
  }

  const onChangeMenu = useCallback(
    (inType: string, groupId: string) => {
      currentSelect.current.type = type ?? inType
      currentSelect.current.groupId = groupId
      pageInfo.current.page = 1
      void getList({
        groupId,
        type: type ?? inType,
        page: pageInfo.current.page,
        size: pageSize,
      })
    },
    [getList, type],
  )

  const onDownload = async (items: IAssetLibraryItem[]) => {
    const [dirPath] = (await window.api.invoke(uiEvents.showOpenDialogSync, {
      properties: ['openDirectory'],
    })) as string[]

    if (!dirPath) {
      return
    }

    onSetDialogSub((dialogMap) => ({
      ...dialogMap.assetLibraryDownload,
      elementProps: {
        list: items,
        onDone(list) {
          list.forEach((item) => {
            void window.api.invoke(uiEvents.copyFileByDirectory, item.filePath, dirPath)
          })
        },
      },
      dialogContentProps: {
        onInteractOutside(event) {
          event.preventDefault()
        },
      },
    }))
  }
  const generatePageNumbers = () => {
    const pages: number[] = []
    const maxVisiblePages = 5

    if (pageInfo.current.totalPage <= maxVisiblePages) {
      for (let i = 1; i <= pageInfo.current.totalPage; i++) {
        pages.push(i)
      }
    } else {
      pages.push(1)

      if (pageInfo.current.page > 3) {
        pages.push(-1)
      }

      for (
        let i = Math.max(2, pageInfo.current.page - 1);
        i <= Math.min(pageInfo.current.page + 1, pageInfo.current.totalPage - 1);
        i++
      ) {
        pages.push(i)
      }

      if (pageInfo.current.page < pageInfo.current.totalPage - 2) {
        pages.push(-1)
      }

      pages.push(pageInfo.current.totalPage)
    }

    return pages
  }

  const Wapper = !onSelect ? AssetLibraryCardWapper : AssetLibraryWapper

  const onBatchCheckedChange = (checked: boolean) => {
    if (checked) {
      setCheckMap((prevCheckMap) => {
        return {
          ...prevCheckMap,
          ...Object.fromEntries(list.map((item) => [item.id, item])),
        }
      })
      setBatchChecked(true)
    } else {
      setCheckMap((prevCheckMap) => {
        const newCheckMap = { ...prevCheckMap }
        list.forEach((item) => {
          delete newCheckMap[item.id]
        })
        return newCheckMap
      })
      setBatchChecked(false)
    }
  }

  useEffect(() => {
    if (list.every((item) => checkMap[item.id])) {
      setBatchChecked(true)
    } else if (list.some((item) => checkMap[item.id])) {
      setBatchChecked('indeterminate')
    } else {
      setBatchChecked(false)
    }

    if (Object.keys(checkMap).length === 0) {
      setBatchChecked(false)
    }
  }, [checkMap, list, list.length])

  const getCheckedCount = () => {
    const count = Object.keys(checkMap).length
    return count === 0 ? '' : `(${count})`
  }

  const batchDelete = useApiMutation(
    (ids: string[]) => ({
      url: `/material/batch`,
      method: 'delete',
      params: { materialIds: ids },
    }),
    {
      onSuccess: (result: { deleteSuccess: number; deleteFail: number }) => {
        void getList({
          groupId: currentSelect.current.groupId,
          type: currentSelect.current.type,
          page: pageInfo.current.page,
          size: pageSize,
        })

        setCheckMap({})
        setBatchDeleteResult(result)
        setBatchDeleteDialogOpen(true)
      },
    },
  )

  return (
    <Wapper>
      <div className="electron-drag-region flex h-14 items-center px-5 font-medium">素材库</div>

      <div className={`flex flex-1 gap-[20px] overflow-hidden pl-5 ${onSelect ? '' : 'pb-5'}`}>
        <AssetLibraryMenu isSelect={!!onSelect} onChange={onChangeMenu} />
        <div className="flex flex-1 flex-col overflow-hidden">
          {!onSelect && (
            <div className="pb-[16px] pr-6">
              {actionType === 'default' ? (
                <div className="flex items-center gap-2">
                  <Button className="gap-2" onClick={onUploadAsset}>
                    <Upload className="h-4 w-4" />
                    <span>上传素材</span>
                  </Button>
                  <Button
                    variant="outline"
                    className="gap-2"
                    onClick={() => {
                      setActionType('batch')
                      setIsDownload((prev) => !prev)
                    }}
                  >
                    <span>批量操作</span>
                  </Button>
                </div>
              ) : (
                <div className="flex items-center justify-between gap-2">
                  <div className="flex items-center gap-1">
                    <Checkbox
                      id="terms"
                      checked={batchChecked}
                      onCheckedChange={onBatchCheckedChange}
                    />
                    <label htmlFor="terms" className="cursor-pointer text-sm">
                      全选
                    </label>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      className="gap-2"
                      onClick={() => {
                        setIsDownload(false)
                        setCheckMap({})
                        setActionType('default')
                      }}
                    >
                      <span>取消</span>
                    </Button>

                    <Button
                      className="gap-2"
                      disabled={!Object.keys(checkMap).length}
                      onClick={() => void onDownload(Object.values(checkMap))}
                    >
                      <span>下载{getCheckedCount()}</span>
                    </Button>

                    <Button
                      variant="destructive"
                      className="gap-2"
                      disabled={!Object.keys(checkMap).length}
                      onClick={async () => {
                        if (
                          await confirm({
                            title: '删除素材',
                            description: `删除已选的${Object.keys(checkMap).length}个素材`,
                            confirmText: '确定删除',
                            cancelText: '取消',
                            type: 'destructive',
                          })
                        ) {
                          const deleteId = Object.keys(checkMap)
                          await batchDelete.mutateAsync(deleteId)
                        }
                      }}
                    >
                      <span>删除{getCheckedCount()}</span>
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}

          {list.length ? (
            <ScrollArea ref={containerRef} className="relative flex-1 pr-5">
              <div className="AssetLibrary-list">
                {list.map((item) => (
                  <AssetLibraryItem
                    isSelect={isManger}
                    check={!!checkMap[item.id]}
                    onCheck={onCheckItem}
                    key={item.createdAt}
                    value={item}
                    onPreview={onPreview}
                    onDownload={(value) => {
                      void onDownload([value])
                    }}
                    onEdite={() => {
                      onSetDialogSub((dialogMap) => ({
                        ...dialogMap.AssetLibraryGroupDialog,
                        elementProps: {
                          initGroupId: item.groupId,
                          materialId: item.id,
                          onCallback: () => {
                            void getList({
                              ...currentSelect.current,
                              size: 100,
                              page: 1,
                            })
                          },
                        },
                      }))
                    }}
                    onDelete={async () => {
                      if (
                        await confirm({
                          title: '删除素材',
                          description: '删除后不可恢复',
                          confirmText: '确定删除',
                          cancelText: '取消',
                          type: 'destructive',
                        })
                      ) {
                        await assetLibraryService.deleteAsset(item.id)
                        setList(list.filter((x) => x.id !== item.id))
                      }
                    }}
                  />
                ))}
              </div>
            </ScrollArea>
          ) : (
            <div className="flex h-full w-full flex-col items-center justify-center gap-1 pb-20">
              <img src={EmptyIcon} alt="" className="w-[164px]" />
              <span className="text-[14px] text-[#757575]">暂无素材</span>
            </div>
          )}

          {!!list.length && (
            <Pagination className="pt-4">
              <PaginationContent>
                <PaginationItem>
                  <PaginationLink
                    onClick={() => {
                      if (pageInfo.current.page > 1) {
                        pageInfo.current.page -= 1
                        void getList({
                          ...currentSelect.current,
                          size: pageSize,
                          page: pageInfo.current.page,
                        })
                      }
                    }}
                    className="w-[58px] cursor-pointer"
                  >
                    上一页
                  </PaginationLink>
                </PaginationItem>
                {generatePageNumbers().map((page, index) => (
                  <PaginationItem key={index}>
                    {page === -1 ? (
                      <PaginationEllipsis />
                    ) : (
                      <PaginationLink
                        onClick={() => {
                          pageInfo.current.page = page
                          void getList({
                            ...currentSelect.current,
                            size: pageSize,
                            page: pageInfo.current.page,
                          })
                        }}
                        isActive={pageInfo.current.page === page}
                        className="cursor-pointer"
                      >
                        {page}
                      </PaginationLink>
                    )}
                  </PaginationItem>
                ))}
                <PaginationItem className="">
                  <PaginationLink
                    onClick={() => {
                      if (pageInfo.current.page < pageInfo.current.totalPage) {
                        pageInfo.current.page += 1
                        void getList({
                          ...currentSelect.current,
                          size: pageSize,
                          page: pageInfo.current.page,
                        })
                      }
                    }}
                    className="w-[58px] cursor-pointer"
                  >
                    下一页
                  </PaginationLink>
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          )}
        </div>
      </div>

      {onSelect && (
        <CardFooter className="flex justify-end gap-2 border-t border-[#00000011] p-[12px]">
          <Button variant="outline" onClick={onClose}>
            取消
          </Button>
          <Button
            onClick={() => {
              const assets = Object.values(checkMap)

              onSelect(assets)
              onClose?.()
            }}
          >
            确认
          </Button>
        </CardFooter>
      )}

      {!isDownload && !onSelect && <AssetLibraryTask />}
      <BatchDeleteDialog
        open={batchDeleteDialogOpen}
        setOpen={setBatchDeleteDialogOpen}
        successCount={batchDeleteResult.deleteSuccess}
        errorCount={batchDeleteResult.deleteFail}
      />
    </Wapper>
  )
}

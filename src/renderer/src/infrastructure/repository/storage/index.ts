import type { MemberTableDeclaration } from './memberDBv2'
import { createMemberDB } from './memberDBv2'
import type { <PERSON><PERSON> } from 'dexie'
import { useDatabaseStore } from '@renderer/store/preloadStateStore'
import type { UserTableDeclaration } from './userDBv1'
import { createUserDB } from './userDBv1'

export class MemberDatabase {
  public instance: Dexie & MemberTableDeclaration

  constructor(userId: string, teamId: string) {
    this.instance = createMemberDB(`${teamId}_${userId}`)
  }
}

export function useMemberDatabase() {
  const { memberDatabase: database } = useDatabaseStore((state) => ({
    memberDatabase: state.memberDatabase,
  }))
  if (!database) {
    throw new Error('MemberDatabase not initialized')
  }
  return database
}

export class UserDatabase {
  public instance: <PERSON>ie & UserTableDeclaration

  constructor(userId: string) {
    this.instance = createUserDB(userId)
  }
}

export function useUserDatabase() {
  const { userDatabase: database } = useDatabaseStore(
    (state) =>
      ({
        userDatabase: state.userDatabase,
      }) as const,
  )
  if (!database) {
    throw new Error('UserDatabase not initialized')
  }
  return database
}

import type { EditContentType } from '@common/model/content-type'
import type {
  Platform,
  PushingTaskSetIdentifier,
  PushingTaskSetState,
} from '@renderer/infrastructure/model'

export type PushingTaskSetViewModel = {
  taskSetId: PushingTaskSetIdentifier
  brief: string
  thumbUrl: string
  state: PushingTaskSetState
  platforms: Platform[]
  contentType: EditContentType
  createTime: Date
  operatorId: string
  operatorName: string
  isFromApp: boolean
  isDraft: boolean
  isCloudTaskSet: boolean
}

import type { EditContentType, PushContentType } from '@common/model/content-type'
import type { Platform } from '@renderer/infrastructure/model'

import type { QueryStatus } from '@common/structure'
import {
  PlatformResultStage,
  PlatformResultStageStatus,
} from '@renderer/infrastructure/model/pushing-task/platform-audit-result'
import type { PublishTaskResponse } from '@renderer/infrastructure/types'

export interface PushingTaskViewModel {
  brief: string
  thumbUrl: string
  progress: number
  queryState: QueryStatus | null
  taskId: string
  accountName?: string //undefined为账号被删除时的情况
  accountAvatarUrl?: string //undefined为账号被删除或者本地历史数据的情况
  accountId?: string //undefined为账号被删除时的情况
  platform: Platform
  editContentType: EditContentType
  createTime: Date
  publishId: string | null
  message: string
  deliveryTime: number
  auditTime: number
  pushContentType: PushContentType | undefined
  stageStatus: PlatformResultStageStatus | null //为null是服务端没有被上报状态情况下的初始值
  stages: PlatformResultStage | null //为null是服务端没有被上报状态情况下的初始值
  statistic?: PublishTaskResponse['statistic']
  urls: string[]
  IsDeleted: boolean
  IsCanceled: boolean
}

export function needQueryAuditResult(viewModel: PushingTaskViewModel) {
  //TODO 这段代码看起来有点难以理解，看看逻辑是否可以优化
  if (viewModel.stages === null || viewModel.stageStatus === null) {
    return false
  }
  if (
    viewModel.stages === PlatformResultStage.SUCCESS &&
    viewModel.stageStatus === PlatformResultStageStatus.SUCCESS
  ) {
    return false
  }
  if (
    (viewModel.stages !== PlatformResultStage.PUSH &&
      viewModel.stages !== PlatformResultStage.UPLOAD) ||
    (viewModel.stages === PlatformResultStage.PUSH &&
      viewModel.stageStatus === PlatformResultStageStatus.SUCCESS)
  ) {
    if (viewModel.stageStatus !== PlatformResultStageStatus.FAIL) {
      return true
    }
  }
  return false
}

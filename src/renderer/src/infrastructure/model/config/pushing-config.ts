import { EditContentType, type PushContentType } from '@common/model/content-type'
import type { MusicPlatformDataItem } from '@common/structure'
import type {
  ArticleTags,
  VideoCategories,
  VideoContentViewModel,
  VideoPlatformConfigs,
} from '@renderer/infrastructure/model'
import { newVideoPlatformConfigs } from '@renderer/infrastructure/model'
import {
  type ImageFileInfo,
  type Platform,
  type VideoFileInfo,
} from '@renderer/infrastructure/model'
import type { TimeStamp } from '@renderer/infrastructure/types/brand'
import {
  newArticlePlatformConfigs,
  type ArticlePlatformConfigs,
} from '@renderer/infrastructure/model/config/article-platforms'

/**
 * 推送配置
 * @description 用于存储发布视频的配置信息，从这里开始，将任务来源进行关注点分离，后续不应该出现具体界面的Config
 */
export interface PushingConfig {
  configId: string
  accountId: string
  platform: Platform
}

export interface CloudPushingConfig extends PushingConfig {
  appTaskId: string
  token: string
}

export class LocalVideoPushingConfig implements PushingConfig, VideoPushingConfig {
  constructor(
    public configId: string,
    public accountId: string,
    public platform: Platform,
    public locationKeyword: string,
    public location: VideoContentViewModel['location'] | null,
    public isOriginal: boolean,
    public pushContentType: PushContentType,
    public title: string,
    public description: string,
    public video: VideoFileInfo,
    public cover: ImageFileInfo,
    public tags: string[],
    public toDraft: boolean,
    public timing?: TimeStamp | undefined,
    public platformConfigs: VideoPlatformConfigs = newVideoPlatformConfigs(),
  ) {}

  public editContentType: EditContentType = EditContentType.Video

  static fromViewModel(
    configId: string,
    accountId: string,
    platform: Platform,
    locationKeyword: string,
    location: VideoContentViewModel['location'],
    isOriginal: boolean,
    pushContentType: PushContentType,
    title: string,
    description: string,
    video: VideoFileInfo,
    cover: ImageFileInfo,
    tags: string[],
    toDraft: boolean,
    timing: TimeStamp | undefined,
    categories: VideoCategories,
  ) {
    const config = new LocalVideoPushingConfig(
      configId,
      accountId,
      platform,
      locationKeyword,
      location,
      isOriginal,
      pushContentType,
      title,
      description,
      video,
      cover,
      tags,
      toDraft,
      timing,
    )

    for (const platformName in categories) {
      // @ts-ignore 临时方案，后续存储和视图的模型应该会改为一致的
      config.platformConfigs[platformName].categories = categories[platformName]
    }

    return config
  }

  get videoUrl() {
    return (this.video?.filePath as string) || ''
  }

  get coverUrl() {
    return (this.cover?.path as string) || ''
  }
}

export class LocalImageTextPushingConfig implements PushingConfig {
  constructor(
    public configId: string,
    public accountId: string,
    public platform: Platform,
    public locationKeyword: string,
    public location: VideoContentViewModel['location'] | null,
    public pushContentType: PushContentType,
    public title: string,
    public description: string,
    public music: {
      [key: string]: MusicPlatformDataItem | null
    },
    public images: ImageFileInfo[],
    public cover: ImageFileInfo,
    public timing?: TimeStamp,
  ) {}

  public editContentType: EditContentType = EditContentType.ImageText
}

// 文章推送配置
export class LocalArticlePushingConfig implements PushingConfig {
  constructor(
    public configId: string,
    public accountId: string,
    public platform: Platform,
    public pushContentType: PushContentType,
    public title: string,
    public cover: ImageFileInfo | null,
    public content: string,
    public isFirst: boolean,
    public category: string[],
    public verticalCover: ImageFileInfo | null,
    public locationKeyword: string,
    public timing: TimeStamp | null,
    public toDraft: boolean,
    public platformConfigs: ArticlePlatformConfigs = newArticlePlatformConfigs(),
  ) {}

  public editContentType: EditContentType = EditContentType.Article

  static fromViewModel(
    configId: string,
    accountId: string,
    platform: Platform,
    pushContentType: PushContentType,
    title: string,
    cover: ImageFileInfo | null,
    content: string,
    isFirst: boolean,
    category: string[],
    verticalCover: ImageFileInfo | null,
    locationKeyword: string,
    timing: TimeStamp | null,
    topic: ArticleTags,
    toDraft: boolean,
  ) {
    const config = new LocalArticlePushingConfig(
      configId,
      accountId,
      platform,
      pushContentType,
      title,
      cover,
      content,
      isFirst,
      category,
      verticalCover,
      locationKeyword,
      timing,
      toDraft,
    )
    for (const platformName in topic) {
      // @ts-ignore 临时方案，后续存储和视图的模型应该会改为一致的
      config.platformConfigs[platformName].topic = topic[platformName]
    }
    return config
  }
}

export class CloudVideoPushingConfig implements CloudPushingConfig, VideoPushingConfig {
  constructor(
    public configId: string,
    public accountId: string,
    public platform: Platform,
    public token: string,
    public appTaskId: string,
    public videoUrl: string,
    public coverUrl: string,
    public pushContentType: PushContentType,
    public title: string,
    public description: string,
    public isOriginal: boolean,
    public locationKeyword: string,
    public cover: ImageFileInfo,
    public timing?: TimeStamp,
  ) {}

  public editContentType: EditContentType = EditContentType.Video
}

export interface VideoPushingConfig {
  videoUrl: string
  coverUrl: string
  editContentType: EditContentType
  pushContentType: PushContentType
  title: string
  description: string
  isOriginal: boolean
  locationKeyword: string
}

// 联合类型
export type LocalPushingConfig =
  | LocalVideoPushingConfig
  | LocalImageTextPushingConfig
  | LocalArticlePushingConfig

export type AllPushingConfig = LocalPushingConfig | CloudVideoPushingConfig

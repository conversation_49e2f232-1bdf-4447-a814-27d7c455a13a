import type { TimeStamp } from '@renderer/infrastructure/types/brand'
import type { CascadingPlatformDataItem } from '@common/structure'

export interface AiQiYiPlatformFormViewModel {
  title: string // 标题字段，必填
  description: string // 描述字段，必填
  tags: string[] // 标签字段，必填
  category: CascadingPlatformDataItem[] // 分类字段，选填
  declaration: number // 声明字段，必填，默认为0（无需声明）
  type: number // 类型字段，必填，默认1（原创类型）
  scheduledTime?: TimeStamp // 定时发布字段，选填
}

export function createAiQiYiPlatformFormViewModel(): AiQiYiPlatformFormViewModel {
  return {
    title: '',
    description: '',
    tags: [],
    category: [],
    declaration: 0,
    type: 1,
  }
}

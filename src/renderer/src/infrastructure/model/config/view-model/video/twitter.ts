
import type { ImageFileInfo } from '@renderer/infrastructure/model'

// Twitter 评论权限类型
export type TwitterReplyPermissionType = 'public' | 'iFollow'

export interface TwitterPlatformFormViewModel {
  // 描述字段，0-2800字符
  description: string
  // 封面字段，可选 - 改为 ImageFileInfo 类型以支持独立封面
  thumbnail: ImageFileInfo | null
  whoComment: TwitterReplyPermissionType // 谁可以评论，默认所有人
  fps: number

  // 视频上传后的key 表单中无需填写
  videoKey: string | null
}

export function createTwitterPlatformFormViewModel(): TwitterPlatformFormViewModel {
  return {
    description: '',
    thumbnail: null,
    whoComment: 'public',
    fps: 1000,
    videoKey: null,
  }
}

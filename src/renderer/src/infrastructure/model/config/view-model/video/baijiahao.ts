import type { TimeStamp } from '@renderer/infrastructure/types/brand'
import type { PlatformDataItem } from '@common/structure'

export interface BaiJiaHaoPlatformFormViewModel {
  title: string // 标题字段，必填
  description: string // 描述字段，必填
  tags: string[] // 标签字段，必填
  declaration: number // 声明字段，必填，默认为0（无需声明）
  location?: PlatformDataItem // 位置字段，选填
  scheduledTime?: TimeStamp // 定时发布字段，选填
}

export function createBaiJiaHaoPlatformFormViewModel(): BaiJiaHaoPlatformFormViewModel {
  return {
    title: '',
    description: '',
    tags: [],
    declaration: 0,
  }
}

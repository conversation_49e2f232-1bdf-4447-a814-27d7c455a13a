import type { ImageFileInfo } from '@renderer/infrastructure/model'

export interface FacebookPlatformFormViewModel {
  title: string
  description: string
  // 封面字段，可选 - 改为 ImageFileInfo 类型以支持独立封面
  thumbnail: ImageFileInfo | null

  // 视频上传后的key 表单中无需填写
  videoKey: string | null
}

export function createFacebookPlatformFormViewModel(): FacebookPlatformFormViewModel {
  return {
    title: '',
    description: '',
    thumbnail: null,
    videoKey: null,
  }
}

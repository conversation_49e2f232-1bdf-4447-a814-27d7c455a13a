import type { TimeStamp } from '@renderer/infrastructure/types/brand'
import type { CascadingPlatformDataItem } from '@common/structure'

export interface ZhiHuPlatformFormViewModel {
  title: string
  description: string
  tags: string[] // 标签字段，必填
  category: CascadingPlatformDataItem[] // 分类字段，必填
  declaration: number // 声明字段，必填，默认为0（无需声明）
  type: number // 类型字段，必填，默认1（原创）
  scheduledTime?: TimeStamp // 定时发布字段，可选
}

export function createZhiHuPlatformFormViewModel(): ZhiHuPlatformFormViewModel {
  return {
    title: '',
    description: '',
    tags: [],
    category: [],
    declaration: 0,
    type: 1,
  }
}

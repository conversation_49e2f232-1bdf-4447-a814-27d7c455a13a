import type { PlatformDataItem } from '@common/structure'
import type { TimeStamp } from '@renderer/infrastructure/types/brand'

export interface WeiXinShiPinHaoPlatformFormViewModel {
  title: string
  description: string
  location?: PlatformDataItem // 位置字段，可选
  scheduledTime?: TimeStamp // 定时发布字段，可选
  type: number // 原创类型字段，默认1（非原创）
}

export function createWeiXinShiPinHaoPlatformFormViewModel(): WeiXinShiPinHaoPlatformFormViewModel {
  return {
    title: '',
    description: '',
    type: 1,
  }
}

import type { PlatformDataItem } from '@common/structure'
import type { TimeStamp } from '@renderer/infrastructure/types/brand'

export interface XiaoHongShuPlatformFormViewModel {
  title: string
  description: string
  declaration: number // 声明字段，默认为0（无需声明）
  location?: PlatformDataItem // 位置字段，可选
  scheduledTime?: TimeStamp // 定时发布字段，可选
}

export function createXiaoHongShuPlatformFormViewModel(): XiaoHongShuPlatformFormViewModel {
  return {
    title: '',
    description: '',
    declaration: 0,
  }
}

import { localClient } from '@renderer/infrastructure/services/application-service/local-client'

/**
 * 文件源类型联合类型
 */
export type FileSourceType =
  /** File对象类型 - 用户通过input选择的文件 */
  | 'file_object'
  /** 本地绝对路径类型 - 需要通过外部服务转换成url */
  | 'local_path'
  /** 网络URL类型 - 标准HTTP/HTTPS资源，支持可选的业务元数据 */
  | 'network_url'

/**
 * 文件源类型常量
 */
export const FileSourceType = {
  /** File对象类型 - 用户通过input选择的文件 */
  FILE_OBJECT: 'file_object' as const,
  /** 本地绝对路径类型 - 需要通过外部服务转换成url */
  LOCAL_PATH: 'local_path' as const,
  /** 网络URL类型 - 标准HTTP/HTTPS资源，支持可选的业务元数据 */
  NETWORK_URL: 'network_url' as const,
} as const

/**
 * 文件源基础接口
 * 定义所有文件源类型的通用方法
 */
interface BaseFileSource {
  /** 文件源类型标识 */
  readonly type: FileSourceType

  /** 用于UI显示的URL */
  readonly displayUrl: string
}

/**
 * File对象文件源接口
 * 用于处理用户通过input选择的本地文件
 */
export interface FileObjectSource extends BaseFileSource {
  readonly type: typeof FileSourceType.FILE_OBJECT

  /** File对象 */
  readonly fileObject: File
}

/**
 * 本地路径文件源接口
 * 用于处理本地文件系统的绝对路径
 */
export interface LocalPathSource extends BaseFileSource {
  readonly type: typeof FileSourceType.LOCAL_PATH

  /** 本地绝对路径 */
  readonly absolutePath: string
}

/**
 * 网络URL文件源接口
 * 用于处理标准的HTTP/HTTPS网络资源，支持可选的业务元数据
 */
export interface NetworkUrlSource extends BaseFileSource {
  readonly type: typeof FileSourceType.NETWORK_URL

  /** 网络URL */
  readonly url: string

  /** 超级ID（可选，用于超级编导文件） */
  readonly superId: string | undefined

  /** 媒体ID（可选，用于超级编导文件） */
  mediaId: string | undefined
  // readonly mediaId: string | undefined 后续应该是readonly，只因为目前没有合适的方法创建mediaId

  /** 超级锁定ID（可选，用于超级编导文件） */
  readonly superLockId: string | undefined
}

/**
 * 文件源联合类型 - 判别式联合
 * 这是实际使用的FileSource类型，支持TypeScript的类型推断
 */
export type FileSource = FileObjectSource | LocalPathSource | NetworkUrlSource

/**
 * 文件源结构化数据类型
 * 用于序列化和反序列化
 */
export type FileSourceStructure =
  | FileObjectSourceStructure
  | LocalPathSourceStructure
  | NetworkUrlSourceStructure

/**
 * File对象文件源结构
 */
export interface FileObjectSourceStructure {
  type: typeof FileSourceType.FILE_OBJECT
  file: File
}

/**
 * 本地路径文件源结构
 */
export interface LocalPathSourceStructure {
  type: typeof FileSourceType.LOCAL_PATH
  absolutePath: string
}

/**
 * 网络URL文件源结构
 */
export interface NetworkUrlSourceStructure {
  type: typeof FileSourceType.NETWORK_URL
  url: string
  superId?: string
  mediaId?: string
  superLockId?: string
}

/**
 * 文件源创建参数
 */
export interface FileSourceCreateParams {
  /** 输入源（File对象或字符串） */
  input: string | File
  /** 超级编导元数据（可选） */
  metadata?: {
    superId?: string
    mediaId?: string
    superLockId?: string
  }
}

/**
 * FileSource工厂函数
 * 提供创建不同类型FileSource的统一接口
 */
export const FileSourceFactory = {
  /**
   * 创建FileObjectSource
   * @param file File对象
   * @returns FileObjectSource实例
   */
  createFileObjectSource(file: File): FileObjectSource {
    return {
      type: FileSourceType.FILE_OBJECT,
      displayUrl: URL.createObjectURL(file),
      fileObject: file,
    }
  },

  /**
   * 创建LocalPathSource
   * @param absolutePath 本地绝对路径
   * @returns LocalPathSource实例
   */
  async createLocalPathSource(absolutePath: string): Promise<LocalPathSource> {
    const displayUrl = await localClient.getFileUrl(absolutePath)

    return {
      type: FileSourceType.LOCAL_PATH,
      displayUrl,
      absolutePath,
    }
  },

  /**
   * 创建NetworkUrlSource
   * @param url 网络URL
   * @param superId 超级ID（可选）
   * @param mediaId 媒体ID（可选）
   * @param superLockId 超级锁定ID（可选）
   * @returns NetworkUrlSource实例
   */
  createNetworkUrlSource(
    url: string,
    superId?: string,
    mediaId?: string,
    superLockId?: string,
  ): NetworkUrlSource {
    return {
      type: FileSourceType.NETWORK_URL,
      displayUrl: url,
      url,
      superId,
      mediaId,
      superLockId,
    }
  },

  /**
   * 智能创建FileSource（自动识别输入类型）
   * @param params 创建参数
   * @returns 对应的FileSource实例
   */
  async createFromInput(params: FileSourceCreateParams): Promise<FileSource> {
    const { input, metadata } = params

    // 如果是File对象
    if (input instanceof File) {
      return this.createFileObjectSource(input)
    }

    // 如果是字符串
    if (typeof input === 'string') {
      // 如果是HTTP/HTTPS URL，创建NetworkUrlSource（可能带有元数据）
      if (input.startsWith('http://') || input.startsWith('https://')) {
        return this.createNetworkUrlSource(
          input,
          metadata?.superId,
          metadata?.mediaId,
          metadata?.superLockId,
        )
      }

      // 否则作为本地路径处理
      return await this.createLocalPathSource(input)
    }

    throw new Error('Invalid input type for FileSource creation')
  },
} as const

import type { ImageFileInfoStructure } from '@common/model/image-file-info'
import { ByteSize } from '@renderer/infrastructure/model/utils/byte-size'
import { PixelSize } from '@renderer/infrastructure/model/utils/pixel-size'

export class ImageFileInfo implements ImageFileInfoStructure {
  constructor(url: string | File, size: number, width: number, height: number, format: string) {
    this.width = width
    this.height = height
    this.size = size
    this.format = format
    this.path = url
  }

  static of(
    width: number,
    height: number,
    size: number,
    format: string,
    url: string | File,
  ): ImageFileInfo {
    return new ImageFileInfo(url, size, width, height, format)
  }

  get byteSize() {
    return ByteSize.fromB(this.size)
  }

  get pixelSize() {
    return PixelSize.from(this.width, this.height)
  }

  height: number
  size: number
  format: string
  path: string | File
  width: number

  static fromObject(cover: ImageFileInfoStructure): ImageFileInfo {
    return new ImageFileInfo(cover.path, cover.size, cover.width, cover.height, cover.format)
  }
}

import type { VideoFileInfoStructure } from '@common/model/video-file-info'
import { ByteSize } from '@renderer/infrastructure/model/utils/byte-size'
import { TimeSpan } from '@renderer/infrastructure/model/utils/time-span'

export class VideoFileInfo implements VideoFileInfoStructure {
  constructor(
    public filePath: string | File,
    public fileSize: number,
    public fileFormat: string,
    public fileDuration: number,
    public videoWidth: number,
    public videoHeight: number,
    public videoEncoding: string,
    public videoDepth: number,
    public fileName: string,
    public hasAudioTrack: boolean,
    public superId?: string,
    public superLockId?: string,
    public mediaId?: string,
  ) {
    this.fileByteSize = ByteSize.fromB(this.fileSize)
    this.fileDurationTimeSpan = TimeSpan.fromSeconds(this.fileDuration)
  }

  public readonly fileByteSize: ByteSize

  public readonly fileDurationTimeSpan: TimeSpan

  get aspectRatio() {
    return this.videoWidth / this.videoHeight
  }

  static fromObject(result: VideoFileInfoStructure) {
    return new VideoFileInfo(
      result.filePath,
      result.fileSize,
      result.fileFormat,
      result.fileDuration,
      result.videoWidth,
      result.videoHeight,
      result.videoEncoding,
      result.videoDepth,
      result.fileName,
      result.hasAudioTrack === undefined || result.hasAudioTrack, //result.hasAudioTrack === undefined是因为某些本地老数据可能没有这个字段，需要做兼容
      result.mediaId,
    )
  }
}

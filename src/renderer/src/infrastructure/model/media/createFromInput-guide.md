# createFromInput 方法使用指南

## 问题背景

由于 `FileSourceFactory.createFromInput` 现在是异步方法（需要调用 `getFileUrl` 处理本地路径），而构造函数无法使用 `await`，因此我们重新设计了 `VideoFileInfo` 和 `ImageFileInfo` 的构造方式：

1. **构造函数简化**：现在只接受 `FileSource` 对象和其他基本属性，进行简单赋值
2. **新增 `createFromInput` 方法**：用于代替原构造函数的使用，处理 `FileSource` 的异步创建

## 解决方案

### VideoFileInfo 新 API

#### 1. `VideoFileInfo.createFromInput()` - 主要异步工厂方法

```typescript
// 替代构造函数的异步创建方法
const videoInfo = await VideoFileInfo.createFromInput(
  filePath,        // string | File
  fileSize,        // number
  fileFormat,      // string
  fileDuration,    // number
  videoWidth,      // number
  videoHeight,     // number
  videoEncoding,   // string
  videoDepth,      // number
  fileName,        // string
  hasAudioTrack,   // boolean
  metadata?: {     // 可选的元数据
    superId?: string
    mediaId?: string
    superLockId?: string
  }
)
```

#### 2. 新的构造函数

```typescript
// 现在构造函数只做简单赋值
const videoInfo = new VideoFileInfo(
  fileSource,      // FileSource 对象
  fileSize,        // number
  fileFormat,      // string
  fileDuration,    // number
  videoWidth,      // number
  videoHeight,     // number
  videoEncoding,   // string
  videoDepth,      // number
  fileName,        // string
  hasAudioTrack    // boolean
)
```

### ImageFileInfo 新 API

#### 1. `ImageFileInfo.createFromInput()` - 主要异步工厂方法

```typescript
// 替代构造函数的异步创建方法
const imageInfo = await ImageFileInfo.createFromInput(
  url,      // string | File
  size,     // number
  width,    // number
  height,   // number
  format,   // string
  metadata?: {     // 可选的元数据
    superId?: string
    mediaId?: string
    superLockId?: string
  }
)
```

#### 2. 新的构造函数

```typescript
// 现在构造函数只做简单赋值
const imageInfo = new ImageFileInfo(
  fileSource,      // FileSource 对象
  size,            // number
  width,           // number
  height,          // number
  format           // string
)
```

## 使用场景

### 1. 处理本地文件路径（推荐使用 createFromInput）

```typescript
// 旧方式（现在会报错）
const videoInfo = new VideoFileInfo('/path/to/video.mp4', ...)

// 新方式（异步，推荐）
const videoInfo = await VideoFileInfo.createFromInput('/path/to/video.mp4', ...)
```

### 2. 处理 File 对象

```typescript
// 使用 createFromInput（推荐）
const videoInfo = await VideoFileInfo.createFromInput(fileObject, ...)

// 或者先创建 FileSource，再使用构造函数
const fileSource = FileSourceFactory.createFileObjectSource(fileObject)
const videoInfo = new VideoFileInfo(fileSource, ...)
```

### 3. 处理网络 URL

```typescript
// 使用 createFromInput（推荐）
const videoInfo = await VideoFileInfo.createFromInput('https://example.com/video.mp4', ...)

// 或者先创建 FileSource，再使用构造函数
const fileSource = FileSourceFactory.createNetworkUrlSource('https://example.com/video.mp4')
const videoInfo = new VideoFileInfo(fileSource, ...)
```

### 4. 带元数据的创建

```typescript
// 使用 createFromInput 传入元数据
const videoInfo = await VideoFileInfo.createFromInput(
  'https://example.com/video.mp4',
  fileSize,
  fileFormat,
  fileDuration,
  videoWidth,
  videoHeight,
  videoEncoding,
  videoDepth,
  fileName,
  hasAudioTrack,
  {
    superId: 'super123',
    mediaId: 'media456',
    superLockId: 'lock789'
  }
)
```

## 向后兼容性

### 保留的同步方法

以下同步方法仍然保留，但只能处理 File 对象和网络 URL：

- `VideoFileInfo.fromObject()` - 反序列化（注意：如果包含本地路径会抛出错误）
- `VideoFileInfo.createWithMetadata()` - 带元数据创建（仅支持网络 URL）
- `ImageFileInfo.of()` - 创建方法（注意：如果是本地路径会抛出错误）
- `ImageFileInfo.fromObject()` - 反序列化（注意：如果包含本地路径会抛出错误）

### 新增的异步方法

- `VideoFileInfo.fromObjectAsync()` - 异步反序列化
- `ImageFileInfo.fromObjectAsync()` - 异步反序列化

## 迁移指南

### 1. 替换直接构造函数调用

```typescript
// 旧代码
function createVideoInfo(filePath: string, ...otherParams) {
  return new VideoFileInfo(filePath, ...otherParams)
}

// 新代码
async function createVideoInfo(filePath: string, ...otherParams) {
  return await VideoFileInfo.createFromInput(filePath, ...otherParams)
}
```

### 2. 处理现有的同步代码

```typescript
// 如果确定只处理网络 URL，可以继续使用同步方法
const videoInfo = VideoFileInfo.createWithMetadata('https://example.com/video.mp4', ...)

// 如果可能包含本地路径，必须使用异步方法
const videoInfo = await VideoFileInfo.createFromInput(filePath, ...)
```

## 注意事项

1. **本地路径必须使用异步方法**：当处理本地文件路径时，必须使用 `createFromInput` 方法
2. **错误处理**：异步方法可能抛出异常，需要适当的错误处理
3. **性能考虑**：异步方法会有轻微的性能开销，但对于本地文件路径是必需的
4. **类型安全**：新的构造函数确保了 `FileSource` 的类型安全性

## 实际使用示例

### 在组件中使用

```typescript
import { VideoFileInfo } from '@renderer/infrastructure/model'

async function handleFileUpload(file: File) {
  try {
    const videoInfo = await VideoFileInfo.createFromInput(
      file,
      file.size,
      'mp4',
      120, // duration in seconds
      1920, // width
      1080, // height
      'h264',
      24,
      file.name,
      true // has audio track
    )
    
    // 使用 videoInfo...
  } catch (error) {
    console.error('Failed to create video info:', error)
  }
}
```

### 在服务中使用

```typescript
import { VideoFileInfo } from '@renderer/infrastructure/model'

class VideoService {
  async processLocalVideo(filePath: string, metadata: VideoMetadata) {
    const videoInfo = await VideoFileInfo.createFromInput(
      filePath,
      metadata.fileSize,
      metadata.format,
      metadata.duration,
      metadata.width,
      metadata.height,
      metadata.encoding,
      metadata.depth,
      metadata.fileName,
      metadata.hasAudioTrack,
      {
        superId: metadata.superId,
        superLockId: metadata.superLockId,
        mediaId: metadata.mediaId
      }
    )
    
    return videoInfo
  }
}
```

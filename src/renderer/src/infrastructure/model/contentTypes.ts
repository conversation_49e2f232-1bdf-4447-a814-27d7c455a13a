import { EditContentType, PushContentType } from '@common/model/content-type'

export function getEditContentTypeByName(name: string): EditContentType {
  switch (name) {
    case EditContentType.Video:
      return EditContentType.Video
    case EditContentType.ImageText:
      return EditContentType.ImageText
    case EditContentType.Article:
      return EditContentType.Article
    default:
      return EditContentType.Video // 历史数据兼容处理
  }
}

export function getPushContentTypeByName(name: string): PushContentType {
  switch (name) {
    case PushContentType.VerticalVideo:
      return PushContentType.VerticalVideo
    case PushContentType.HorizonVideo:
      return PushContentType.HorizonVideo
    case PushContentType.Video:
      return PushContentType.Video
    case PushContentType.ImageText:
      return PushContentType.ImageText
    case PushContentType.Article:
      return PushContentType.Article
    default:
      return PushContentType.VerticalVideo // 历史数据兼容处理
  }
}

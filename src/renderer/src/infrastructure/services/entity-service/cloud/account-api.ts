import type {
  AccountFilterItem,
  AuthorizingAccount,
  Platform,
  SpaceFavorite,
  Account,
  AccountState,
  MergedAccount,
} from '@renderer/infrastructure/model'
import {
  spaceColors,
  WebSpace,
  Wechat3rdPartyAccount,
  WechatShiPinHao3rdPartySubAccount,
} from '@renderer/infrastructure/model'
import { WechatGongZhongHaoOpenAccount } from '@renderer/infrastructure/model'
import { SpiderAccount } from '@renderer/infrastructure/model'
import { getPlatformByName } from '@renderer/infrastructure/model'
import { apiConverter } from '@renderer/infrastructure/services/network-service/api-converter'
import type { AccountResponse } from '@renderer/infrastructure/types/account-response'
import type { PagedResponse } from '@renderer/infrastructure/types'
import { PagedResponse2InfiniteQueryResponse } from '@renderer/infrastructure/types'
import type { Group } from '@renderer/infrastructure/model/group/group'
import { useUserApiService } from '@renderer/infrastructure/services/network-service'
import { useCallback, useMemo } from 'react'
import type { SpaceFavoriteResponse } from '@renderer/infrastructure/types/space-response'
import type { SessionState } from '@common/structure'
import { platformNames } from '@common/model/platform-name'

export function Response2Favorite(favorite: SpaceFavoriteResponse) {
  return {
    id: favorite.id,
    name: favorite.name,
    url: favorite.websiteUrl,
  } satisfies SpaceFavorite as SpaceFavorite
}

function response2Account(response: AccountResponse): MergedAccount {
  if (response.platformName === platformNames.Other) {
    return new WebSpace(
      response.id,
      response.platformAccountName,
      response.spaceUrl ?? '',
      response.color ?? spaceColors[0],
      response.isOperate,
      response.groups,
      null,
      response.updatedAt ? new Date(response.updatedAt) : null,
      response.favorites?.map(Response2Favorite) ?? [],
      response.checksum,
      response.isFreeze,
    )
  } else if (response.platformName === platformNames.WeiXin) {
    return new Wechat3rdPartyAccount(
      response.id,
      getPlatformByName(response.platformName),
      response.platformAuthorId,
      response.platformAccountName,
      response.platformAvatar,
      response.isOperate,
      apiConverter.sessionState2Model(response.status),
      response.remark,
      response.groups,
      response.isFreeze,
      response.platformType,
    )
  } else if (response.platformName === platformNames.WeiXinShiPinHao && !!response.parentId) {
    return new WechatShiPinHao3rdPartySubAccount(
      response.id,
      response.platformAuthorId,
      response.platformAccountName,
      response.platformAvatar,
      response.spaceId,
      response.isOperate,
      apiConverter.sessionState2Model(response.status),
      response.remark,
      response.groups,
      response.isFreeze,
      response.platformType,
      response.favorites?.map(Response2Favorite) ?? [],
      response.parentId,
      response.kuaidailiArea ? response.kuaidailiArea.toString() : null,
      response.isLock,
      response.color ?? spaceColors[0],
    )
  } else if (response.platformType === 1) {
    return new WechatGongZhongHaoOpenAccount(
      response.id,
      getPlatformByName(response.platformName),
      response.platformAuthorId,
      response.platformAccountName,
      response.platformAvatar,
      response.isOperate,
      apiConverter.sessionState2Model(response.status),
      response.remark,
      response.groups,
      response.isFreeze,
      response.platformType,
      response.serviceTypeId,
      response.verifyTypeInfo,
      response.accountStatus,
    )
  } else {
    return new SpiderAccount(
      response.id,
      getPlatformByName(response.platformName),
      response.platformAuthorId,
      response.platformAccountName,
      response.platformAvatar,
      response.isRealNameVerified === undefined || response.isRealNameVerified,
      response.spaceId,
      response.isOperate,
      apiConverter.sessionState2Model(response.status),
      response.remark,
      response.groups,
      response.isFreeze,
      response.platformType,
      response.favorites?.map(Response2Favorite) ?? [],
      response.kuaidailiArea ? response.kuaidailiArea.toString() : null,
      response.color ?? spaceColors[0],
    )
  }
}

function response2Wechat3rdPartyAccount(response: AccountResponse) {
  if (response.platformName !== platformNames.WeiXin) {
    throw new Error('response不是一个微信账号')
  } else {
    return new Wechat3rdPartyAccount(
      response.id,
      getPlatformByName(response.platformName),
      response.platformAuthorId,
      response.platformAccountName,
      response.platformAvatar,
      response.isOperate,
      apiConverter.sessionState2Model(response.status),
      response.remark,
      response.groups,
      response.isFreeze,
      response.platformType,
    )
  }
}

export function useAccountApi() {
  const userApiService = useUserApiService()

  const checkAccount = useCallback(
    (platformAccountId: string) => {
      return userApiService.get<{ loginStatus: boolean }>(
        `/platform-accounts/${platformAccountId}/validate`,
      )
    },
    [userApiService],
  )

  const getAccountsCursor = useCallback(
    async function (
      cursor: Date | null,
      {
        platform,
        group,
        name,
        accountState,
        platforms,
      }: {
        platform?: Platform | null
        group?: Group | null
        name?: string | null
        accountState: AccountState | null
        platforms?: Platform[] | null
      },
      operableOnly: boolean = false,
    ) {
      const response = await userApiService.get<PagedResponse<AccountResponse>>(
        `/platform-accounts`,
        {
          platform: platform?.name,
          platforms: platforms?.map((x) => x.name),
          group: group?.id,
          name: name ?? undefined,
          isRealNameVerified: accountState === '未实名' ? 'false' : undefined,
          loginStatus:
            accountState === '正常' || accountState === '已失效'
              ? apiConverter.sessionState2API(accountState).toString()
              : undefined,
          time: apiConverter.date2APINumber(cursor),
          isolation: operableOnly.toString(),
          size: '20',
        },
      )

      return PagedResponse2InfiniteQueryResponse(response, cursor, response2Account, (response) =>
        apiConverter.apiNumber2Date(
          response.data.reduce((prev, current) =>
            prev.createdAt < current.createdAt ? prev : current,
          ).createdAt,
        ),
      )
    },
    [userApiService],
  )

  const getAccountAll = useCallback(
    async function () {
      const response = await userApiService.get<PagedResponse<AccountResponse>>(
        `/platform-accounts`,
        {
          isolation: 'true',
          page: '1',
          size: '9999',
        },
      )

      return response.data.filter((x) => x.platformType === 0).map(response2Account)
    },
    [userApiService],
  )

  const getSpiderAccountAll = useCallback(
    async function (sessionState?: SessionState) {
      const response = await userApiService.get<PagedResponse<AccountResponse>>(
        `/platform-accounts`,
        {
          loginStatus: sessionState
            ? apiConverter.sessionState2API(sessionState).toString()
            : undefined,
          isolation: 'true',
          page: '1',
          size: '9999',
        },
      )

      return response.data
        .filter((x) => x.platformType === 0)
        .map(response2Account) as SpiderAccount[] // 只返回爬虫账号
    },
    [userApiService],
  )

  const putSpiderAccount = useCallback(
    async function (account: AuthorizingAccount, spaceColor: string) {
      return response2Account(
        await userApiService.put<AccountResponse>(`/platform-accounts`, {
          platformName: account.platform.name,
          platformAuthorId: account.authorId,
          platformAccountName: account.nickName,
          platformAvatar: account.avatar,
          token: '',
          localStorage: '',
          identityVerified: account.identityVerified,
          status: apiConverter.sessionState2API(account.sessionState),
          color: spaceColor,
        }),
      ) as SpiderAccount // 这种方式添加的一定是爬虫账号，如果不是爬虫账号，请不要使用这种方式添加
    },
    [userApiService],
  )

  const patchAccountRemark = useCallback(
    async function (accountId: string, remark: string) {
      await userApiService.patch<AccountResponse>(`/platform-accounts/${accountId}`, {
        remark: remark,
      })
    },
    [userApiService],
  )

  const patchAccountGroups = useCallback(
    async function (accountId: string, groups: string[]) {
      await userApiService.patch<AccountResponse>(`/platform-accounts/${accountId}`, {
        groups: groups,
      })
    },
    [userApiService],
  )

  const patchAccountState = useCallback(
    async function (accountId: string, sessionState: SessionState, statusChecksum: string) {
      const response = await userApiService.patch<AccountResponse>(
        `/platform-accounts/${accountId}`,
        {
          status: apiConverter.sessionState2API(sessionState),
          statusChecksum: statusChecksum,
        },
      )
      // 只有SpiderAccount才有sessionState，所以返回的必定是SpiderAccount
      return response2Account(response) as SpiderAccount
    },
    [userApiService],
  )

  const deleteAccount = useCallback(
    async function (account: MergedAccount) {
      await userApiService.delete(`/platform-accounts/${account.accountId}`)
    },
    [userApiService],
  )

  const getAccount = useCallback(
    async function (accountId: string) {
      return response2Account(
        await userApiService.get<AccountResponse>(`/platform-accounts/${accountId}`),
      )
    },
    [userApiService],
  )

  const unFreezeAccount = useCallback(
    function (account: Account) {
      return userApiService.put(`/platform-accounts/${account.accountId}/freeze`, {
        isFreeze: false,
      })
    },
    [userApiService],
  )

  const unFreezeSpaceAccount = useCallback(
    function (id: string) {
      return userApiService.put(`/platform-accounts/${id}/freeze`, {
        isFreeze: false,
      })
    },
    [userApiService],
  )

  const getAccountsByPage = useCallback(
    async function (
      platform: string,
      size: number,
      page: number,
      filters?: {
        nameKeyword?: string
        accountState?: AccountState | null
        groupId?: string | null
      },
    ): Promise<PagedResponse<MergedAccount>> {
      const result = await userApiService.get<PagedResponse<AccountResponse>>(
        `/platform-accounts`,
        {
          platform,
          size: size.toString(),
          page: page.toString(),
          isolation: 'true',
          ...(filters?.nameKeyword && { name: filters.nameKeyword }),
          ...(filters?.accountState && {
            isRealNameVerified: filters.accountState === '未实名' ? 'false' : undefined,
            loginStatus:
              filters.accountState === '正常' || filters.accountState === '已失效'
                ? apiConverter.sessionState2API(filters.accountState).toString()
                : undefined,
          }),
          ...(filters?.groupId && { group: filters.groupId }),
        },
      )
      return {
        ...result,
        data: result.data.map(response2Account),
      }
    },
    [userApiService],
  )

  const getAccountFilters = useCallback(
    async function (platformName?: string) {
      const result = await userApiService.get<PagedResponse<AccountResponse>>(
        `/platform-accounts`,
        {
          isolation: 'true',
          platform: platformName,
          size: '10000',
          page: '1',
        },
      )
      return result.data.map(
        (x) =>
          ({
            id: x.id,
            name: x.platformAccountName,
          }) satisfies AccountFilterItem as AccountFilterItem,
      )
    },
    [userApiService],
  )

  const getInvalidAccounts = useCallback(
    async function (platforms: string[]) {
      const result = await userApiService.get<PagedResponse<AccountResponse>>(
        `/platform-accounts`,
        {
          platforms: platforms,
          loginStatus: '2',
          size: '10000',
          page: '1',
        },
      )
      return result.data.map(response2Account)
    },
    [userApiService],
  )

  const getSubAccounts = useCallback(
    async function (account: Wechat3rdPartyAccount) {
      const response = await userApiService.get<PagedResponse<AccountResponse>>(
        `/platform-accounts`,
        {
          parentId: account.accountId,
          size: '10000',
          page: '1',
        },
      )
      return response.data.map(response2Account)
    },
    [userApiService],
  )

  const getParentAccount = useCallback(
    async function (account: WechatShiPinHao3rdPartySubAccount) {
      return response2Wechat3rdPartyAccount(
        await userApiService.get<AccountResponse>(`/platform-accounts/${account.parentAccountId}`),
      )
    },
    [userApiService],
  )

  const hasAnyAccounts = useCallback(async () => {
    const result = await userApiService.get<PagedResponse<AccountResponse>>(`/platform-accounts`)
    return result.data.length > 0
  }, [userApiService])

  const setIdentityVerified = useCallback(
    async (accountId: string, identityVerified: boolean) => {
      await userApiService.patch<AccountResponse>(`/platform-accounts/${accountId}`, {
        isRealNameVerified: identityVerified,
      })
    },
    [userApiService],
  )

  const setProxy = useCallback(
    async (account: Account, areaCode: string | null) => {
      return await userApiService.patch<AccountResponse>(
        `/platform-accounts/${account.accountId}`,
        {
          kuaidailiArea: areaCode ?? '',
        },
      )
    },
    [userApiService],
  )

  return useMemo(
    () => ({
      checkAccount,
      getAccountsCursor,
      getAccountAll,
      getSpiderAccountAll,
      putSpiderAccount,
      patchAccountRemark,
      patchAccountGroups,
      patchAccountState,
      deleteAccount,
      getAccount,
      unFreezeAccount,
      getAccountsByPage,
      getAccountFilters,
      getInvalidAccounts,
      getSubAccounts,
      getParentAccount,
      hasAnyAccounts,
      setIdentityVerified,
      setProxy,
      unFreezeSpaceAccount,
    }),
    [
      checkAccount,
      getAccountsCursor,
      getAccountAll,
      getSpiderAccountAll,
      putSpiderAccount,
      patchAccountRemark,
      patchAccountGroups,
      patchAccountState,
      deleteAccount,
      getAccount,
      unFreezeAccount,
      getAccountsByPage,
      getAccountFilters,
      getInvalidAccounts,
      getSubAccounts,
      getParentAccount,
      hasAnyAccounts,
      setIdentityVerified,
      setProxy,
      unFreezeSpaceAccount,
    ],
  )
}

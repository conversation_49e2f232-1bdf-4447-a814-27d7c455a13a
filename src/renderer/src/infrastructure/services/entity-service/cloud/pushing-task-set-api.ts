import type {
  Operator,
  PushingTaskSetIdentifier,
  PushingTaskSetViewModel,
} from '@renderer/infrastructure/model'
import { getEditContentTypeByName } from '@renderer/infrastructure/model'
import { getPlatformByName } from '@renderer/infrastructure/model'
import { apiConverter } from '@renderer/infrastructure/services/network-service/api-converter'

import { useCallback, useMemo } from 'react'
import { useUserApiService } from '@renderer/infrastructure/services/network-service'

import type { PushSetStateFilterOption } from '@renderer/pages/Publish/components/PushStateFilter'
import type {
  AccountForm,
  PagedResponse,
  PushingTaskSetResponse,
  TaskSetCreateRequest,
  TaskSetForm,
} from '@renderer/infrastructure/types'
import {
  type InfiniteQueryResponse,
  PagedResponse2InfiniteQueryResponse,
} from '@renderer/infrastructure/types'
import type { ContentTypeFilterOption } from '@renderer/pages/Publish/components/ContentTypeFilter'
import type { CloudPublishTaskVideoFromLite } from '@yixiaoer/platform-service'

export function usePushingTaskSetApi() {
  const userApiService = useUserApiService()

  const publishTaskSet = useCallback(
    (body: TaskSetCreateRequest<TaskSetForm>) => {
      return userApiService.post<string>(`/taskSets`, body)
    },
    [userApiService],
  )

  const getPublishForm = useCallback(
    (taskSetId: string) => {
      return userApiService.get<CloudPublishTaskVideoFromLite>(`/taskSets/${taskSetId}/publishForm`)
    },
    [userApiService],
  )

  const getLocal = useCallback(
    (platformAccountId: string, { keyWord, nextPage }: { keyWord?: string; nextPage?: string }) => {
      return userApiService.get(`/platform-accounts/${platformAccountId}/location`, {
        keyWord,
        nextPage,
      })
    },
    [userApiService],
  )

  const getTaskSets = useCallback(
    async (
      pushState: PushSetStateFilterOption,
      operators: Operator[],
      contentType: ContentTypeFilterOption,
      cursor: Date | null,
    ): Promise<InfiniteQueryResponse<PushingTaskSetViewModel, Date | null>> => {
      const response = await userApiService.get<PagedResponse<PushingTaskSetResponse>>(
        `/taskSets`,
        {
          userIds: operators.map((x) => x.id),
          time: apiConverter.date2APINumber(cursor),
          taskSetStatus: pushState !== '全部' ? pushState : undefined,
          publishType: contentType !== '全部' ? contentType : undefined,
          size: '50',
        },
      )
      return PagedResponse2InfiniteQueryResponse(
        response,
        cursor,
        (x) =>
          ({
            taskSetId: x.id,
            brief: x.desc ?? '',
            thumbUrl: x.coverUrl ?? '',
            state: apiConverter.apiTaskSetStatus2PushingSetState(x.taskSetStatus),
            platforms: x.platforms.map((platformName) => getPlatformByName(platformName)),
            contentType: getEditContentTypeByName(x.publishType),
            createTime: apiConverter.apiNumber2Date(x.createdAt),
            operatorId: x.userId,
            operatorName: x.nickName,
            isFromApp: x.isAppContent,
            isDraft: x.isDraft,
            isCloudTaskSet: x.publishChannel === 'cloud',
          }) satisfies PushingTaskSetViewModel as PushingTaskSetViewModel,
        (response) =>
          response.data.length === 0
            ? cursor
            : apiConverter.apiNumber2Date(
                response.data.reduce((prev, current) =>
                  prev.createdAt < current.createdAt ? prev : current,
                ).createdAt,
              ),
      )
    },
    [userApiService],
  )

  const getTaskSet = useCallback(
    async (taskSetId: PushingTaskSetIdentifier) => {
      const response = await userApiService.get<PushingTaskSetResponse>(`/taskSets/${taskSetId}`)
      return {
        taskSetId,
        brief: response.desc ?? '',
        thumbUrl: response.coverUrl ?? '',
        state: apiConverter.apiTaskSetStatus2PushingSetState(response.taskSetStatus),
        platforms: response.platforms.map((platformName) => getPlatformByName(platformName)),
        contentType: getEditContentTypeByName(response.publishType),
        createTime: apiConverter.apiNumber2Date(response.createdAt),
        operatorId: response.userId,
        operatorName: response.nickName,
        isFromApp: response.isAppContent,
        isDraft: response.isDraft,
        isCloudTaskSet: response.publishChannel === 'cloud',
      } satisfies PushingTaskSetViewModel as PushingTaskSetViewModel
    },
    [userApiService],
  )

  const getFormData = useCallback(
    async (taskSetId: PushingTaskSetIdentifier) => {
      const response = await userApiService.get<{ formData: TaskSetForm }>(
        `/taskSets/${taskSetId}/publishForm`,
      )
      //TODO 这里后续会在formData的同级下的data下保存完整的accounts表单，而不是从formData中获取
      return {
        commonForm: response.formData,
        accountForms: response.formData.accounts.map((item) => {
          const { accountId, ...accountFormData } = item
          return {
            accountId: accountId as string,
            formData: accountFormData as AccountForm,
          } satisfies {
            accountId: string
            formData: AccountForm
          }
        }),
      }
    },
    [userApiService],
  )

  return useMemo(
    () => ({
      getLocal,
      getTaskSets,
      getTaskSet,
      publishTaskSet,
      getPublishForm,
      getFormData,
    }),
    [getLocal, getTaskSets, getTaskSet, publishTaskSet, getPublishForm, getFormData],
  )
}

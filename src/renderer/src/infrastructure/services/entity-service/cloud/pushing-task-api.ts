import type { PushingTaskSetIdentifier } from '@renderer/infrastructure/model'
import {
  getEditContentTypeByName,
  getPlatformByName,
  type PushingTaskViewModel,
} from '@renderer/infrastructure/model'
import { apiConverter } from '@renderer/infrastructure/services/network-service/api-converter'
import type { PublishTaskResponse } from '@renderer/infrastructure/types'
import { useMemo } from 'react'
import { useUserApiService } from '@renderer/infrastructure/services/network-service'
import type { ApiService } from '@renderer/infrastructure/services/network-service/api-service'
import type { PushContentType } from '@common/model/content-type'
import { EditContentType } from '@common/model/content-type'
import { platformNames } from '@common/model/platform-name'

export function usePushingTaskApi() {
  const userApiService = useUserApiService()
  return useMemo(() => new PushingTaskApi(userApiService), [userApiService])
}

export class PushingTaskApi {
  constructor(private userApiService: ApiService) {}

  reportBrowserTask() {
    return this.userApiService.post('/tasks/browser-publish', {
      publishType: EditContentType.Video,
    })
  }

  cancelTask(taskId: string) {
    return this.userApiService.post(`/tasks/${taskId}/cancel`)
  }

  deleteTask(taskId: string) {
    return this.userApiService.delete(`/tasks/${taskId}/publish`)
  }

  async getTasksByTaskSetId(taskSetId: PushingTaskSetIdentifier) {
    const responses = await this.userApiService.get<PublishTaskResponse[]>(
      `/taskSets/${taskSetId}/tasks`,
    )
    return responses.map(
      (x) =>
        ({
          brief: x.desc,
          thumbUrl: x.cover,
          progress: 100,
          queryState: null,
          taskId: x.taskId,
          accountId: x.platformAccountId,
          accountName: x.platformAccountName,
          accountAvatarUrl: x.platformAvatar,
          platform: getPlatformByName(x.platformName),
          editContentType: getEditContentTypeByName(x.publishType),
          pushContentType: x.mediaType as PushContentType | undefined,
          createTime: apiConverter.apiString2Date(x.createdAt),
          publishId: x.publishId,
          message: x.errorMessage,
          auditTime: x.auditTime,
          deliveryTime: x.deliveryTime,
          stageStatus: x.stageStatus,
          statistic: x.statistic,
          stages: x.stages,
          urls: x.openUrl
            ? x.platformName === platformNames.WeiXinGongZhongHao
              ? (JSON.parse(x.openUrl) as { idx: number; article_url: string }[]).map(
                  (x) => x.article_url,
                )
              : [x.openUrl]
            : [],
          IsDeleted: x.taskStatus === 'deleted',
          IsCanceled: x.taskStatus === 'cancel',
        }) satisfies PushingTaskViewModel as PushingTaskViewModel,
    )
  }
}

import { useUserApiService } from '@renderer/infrastructure/services/network-service'
import { useCallback, useMemo } from 'react'

/**
 * FAQ问题详情
 */
export interface QuestionDetail {
  /**
   * 问题id
   */
  id: string
  /**
   * 跳转地址
   */
  questionUrl: string
  /**
   * 标题
   */
  title: string
}

/**
 * FAQ列表响应数据
 */
export interface QuestionListResponse {
  data: QuestionDetail[]
  page: number
  size: number
  totalPage: number
  totalSize: number
}

/**
 * FAQ API服务hook
 */
export function useFAQApi() {
  const userApiService = useUserApiService()

  const getQuestions = useCallback(
    async (page = 1, size = 20): Promise<QuestionListResponse> => {
      try {
        const result = await userApiService.get<QuestionListResponse>('/question', {
          page: page.toString(),
          size: size.toString(),
        })
        return result
      } catch (error) {
        console.error('FAQ API: 接口调用失败', error)
        throw error
      }
    },
    [userApiService],
  )

  return useMemo(
    () => ({
      getQuestions,
    }),
    [getQuestions],
  )
}

import type {
  TeamDetailResponse,
  TeamListItemResponse,
} from '@renderer/infrastructure/types/TeamResponse'
import { Team, TeamDetail } from '@renderer/infrastructure/model/team-manage/team'
import type { PagedResponse } from '@renderer/infrastructure/types'
import { useMemo } from 'react'
import type { ApiService } from '@renderer/infrastructure/services/network-service/api-service'
import { useUserApiService } from '@renderer/infrastructure/services/network-service'
import { ByteSize } from '@renderer/infrastructure/model/utils/byte-size'

const _DetailResponse2Model = (data: TeamDetailResponse) =>
  new TeamDetail(
    data.id,
    data.name,
    data.logoUrl,
    data.logoKey,
    data.code,
    data.accountCountLimit,
    data.accountCount,
    data.accountCapacity,
    data.accountCapacityLimit,
    data.memberCountLimit,
    data.memberCount,
    data.createdAt,
    data.isVip,
    data.expiredAt,
    ByteSize.fromB(data.capacity),
    ByteSize.fromB(data.usedCapacity),
    data.interestCount,
    data.remainingDay,
    data.publishAccountLimit,
    data.corporateTransfer,
    ByteSize.fromB(data.networkTraffic),
    ByteSize.fromB(data.useNetworkTraffic),
    data.salesType,
    data.components,
  )

const _ListItemResponse2Model = (data: TeamListItemResponse) =>
  new Team(data.id, data.name, data.logoUrl, data.logoKey, data.isVip, data.expiredAt)

export function useTeamApi() {
  const userApiService = useUserApiService()
  return useMemo(() => new TeamApi(userApiService), [userApiService])
}
export class TeamApi {
  constructor(private userApiService: ApiService) {}

  async createTeam(name: string, logoKey: string) {
    const result = await this.userApiService.post<TeamDetailResponse>('/teams', {
      name: name,
      logoKey: logoKey,
      scope: 'login',
    })
    return _DetailResponse2Model(result)
  }

  async getTeamList() {
    const result = await this.userApiService.get<PagedResponse<TeamListItemResponse>>('/teams', {
      size: '9999',
      page: '1',
    })
    return result.data.map((x) => _ListItemResponse2Model(x))
  }

  async getTeamInfo(teamId: string) {
    const result = await this.userApiService.get<TeamDetailResponse>(`/teams/${teamId}`)
    return _DetailResponse2Model(result)
  }

  async setTeamPlugin(
    teamId: string,
    data: { name: string; componentArgs: Record<string, unknown>; enable: boolean },
  ) {
    const result = await this.userApiService.put<TeamDetailResponse>(
      `/teams/${teamId}/components`,
      data,
    )
    return result
  }

  async updateTeamName(teamId: string, name: string) {
    const result = await this.userApiService.patch<TeamDetailResponse>(`/teams/${teamId}`, {
      name: name,
    })
    return _DetailResponse2Model(result)
  }

  async updateTeamLogo(teamId: string, logoKey: string) {
    const result = await this.userApiService.patch<TeamDetailResponse>(`/teams/${teamId}`, {
      logoKey: logoKey,
    })
    return _DetailResponse2Model(result)
  }

  dissolveTeam(teamId: string) {
    return this.userApiService.delete(`/teams/${teamId}`)
  }
}

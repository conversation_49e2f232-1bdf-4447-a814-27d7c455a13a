import { browserChannel } from '@common/events/browser-events'
import { useMemo } from 'react'
import { useContextStore } from '@renderer/store/contextStore'
import type { BrowserContextIdentifier } from '@common/structure/browser/contextIdentifier'
import type { SpaceIdentifier } from '@common/structure/space/space-identifier'
import type { TeamIdentifier } from '@common/structure/team-identifier'
import type { Collect, SpiderAccount } from '@renderer/infrastructure/model'
import { toAccountInfoStructure, type Platform } from '@renderer/infrastructure/model'
import { useCurrentTeamId } from '@renderer/hooks/preload/use-current-team-id'

import type { ContextFavorite } from '@common/structure/space/context-favorite'
import type { AccountSession } from '@common/structure'
import type { ImageTask, VideoTask } from '../application-service/rpa-service'
import { localClient } from '../application-service/local-client'

export class BrowserService {
  constructor(
    private userid: string,
    private teamId: string,
  ) {}

  teamIdentifier() {
    return {
      userid: this.userid,
      teamId: this.teamId,
    } satisfies Team<PERSON>dentifier as TeamIdentifier
  }

  contextIdentifierOf(contextId: string) {
    return {
      userid: this.userid,
      teamId: this.teamId,
      contextId,
    } satisfies BrowserContextIdentifier as BrowserContextIdentifier
  }

  spaceIdentifierOf(spaceId: string) {
    return {
      userid: this.userid,
      teamId: this.teamId,
      spaceId,
    } satisfies SpaceIdentifier as SpaceIdentifier
  }

  openTab(contextId: string, urls: string[]) {
    return window.api.invoke<void>(
      browserChannel.openTab,
      this.contextIdentifierOf(contextId),
      urls,
    )
  }

  newOpenTab(contextId: string, urls: string[], ident: string) {
    return window.api.invoke<void>(
      browserChannel.newOpenTab,
      this.contextIdentifierOf(contextId),
      urls,
      ident,
    )
  }

  createAuthTab(platform: Platform, data: { contextId?: string; accountSession?: AccountSession }) {
    return window.api.invoke<void>(browserChannel.createAuthTab, data, platform.name)
  }

  openAccountTab(
    contextId: string,
    platform: Platform,
    name: string,
    accountSession: AccountSession | null = null,
    urls?: Collect[],
  ) {
    return window.api.invoke<void>(
      browserChannel.openAccountTab,
      contextId,
      platform.name,
      name,
      accountSession,
      urls,
    )
  }

  openFavorite(
    url: string,
    contextId: string,
    title: string,
    accountSession: AccountSession | null = null,
    urls?: Collect[],
  ) {
    return window.api.invoke<void>(
      browserChannel.openFavorite,
      url,
      contextId,
      title,
      accountSession,
      urls,
    )
  }

  openAccountTabInNewWindow(
    windowIdentifier: string,
    contextId: string,
    platform: Platform,
    accountId: string | null = null,
    urls?: string[],
  ) {
    return window.api.invoke<void>(
      browserChannel.openAccountTabInNewWindow,
      windowIdentifier,
      this.contextIdentifierOf(contextId),
      platform.name,
      accountId,
      urls,
    )
  }

  isContextCreated(contextId: string) {
    return window.api.invoke<boolean>(
      browserChannel.isContextCreated,
      this.contextIdentifierOf(contextId),
    )
  }

  isContextOpened(contextId: string) {
    return window.api.invoke<boolean>(
      browserChannel.isContextOpened,
      this.contextIdentifierOf(contextId),
    )
  }

  openVideoRpaTab(data: { video: string; cover: string }, config: VideoTask) {
    return localClient.startVideoRpa(data, config)
  }

  openImageTextRpa(config: ImageTask) {
    return localClient.startImageRpa(config)
  }

  cleanupSessionDirectory(contextIds: string[]) {
    // return window.api.invoke<void>(
    //   browserChannel.cleanupSessionDirectory,
    //   this.userid,
    //   this.teamId,
    //   contextIds,
    // )
    return
  }

  hasIcon(spaceId: string): Promise<boolean> {
    return window.api.invoke<boolean>(browserChannel.hasSpaceIcon, this.spaceIdentifierOf(spaceId))
  }

  async updateIcon(spaceId: string, favicon: string) {
    return window.api.invoke<void>(
      browserChannel.updateSpaceIcon,
      this.spaceIdentifierOf(spaceId),
      favicon,
    )
  }

  getIcon(spaceId: string) {
    return window.api.invoke<string | null>(
      browserChannel.getSpaceIcon,
      this.spaceIdentifierOf(spaceId),
    )
  }

  async restoreContext(
    contextId: string,
    color: string,
    contextName: string,
    startUrl: string,
    session: AccountSession | null,
    urls?: Collect[],
  ) {
    return window.api.invoke<void>(
      browserChannel.restoreContext,
      contextId,
      color,
      contextName,
      startUrl,
      session,
      urls,
    )
  }

  async createAuthContext(
    contextId: string,
    color: string,
    contextName: string,
    url: string,
    unsaved: boolean = false,
  ) {
    return window.api.invoke<void>(
      browserChannel.createAuthContext,
      contextId,
      color,
      url,
      contextName,
      unsaved,
    )
  }

  async createNewContext(
    contextId: string,
    color: string,
    contextName: string,
    url: string,
    unsaved: boolean = false,
  ) {
    return window.api.invoke<void>(
      browserChannel.createNewContext,
      contextId,
      color,
      url,
      contextName,
      unsaved,
    )
  }

  async createAccountContext(
    contextId: string,
    color: string,
    platform: Platform,
    account: SpiderAccount | null = null,
    accountSession: AccountSession | null = null,
  ) {
    return window.api.invoke<void>(
      browserChannel.createAccountContext,
      this.contextIdentifierOf(contextId),
      color,
      platform.name,
      account ? toAccountInfoStructure(account) : null,
      accountSession,
    )
  }

  async updateAccountContext(contextId: string, account: SpiderAccount | null) {
    return window.api.invoke<void>(
      browserChannel.updateAccountContext,
      this.contextIdentifierOf(contextId),
      account ? toAccountInfoStructure(account) : null,
    )
  }

  closeAllTab(contextId: string) {
    return window.api.invoke<void>(browserChannel.closeAllTab, this.contextIdentifierOf(contextId))
  }

  closeBrowser() {
    // return window.api.invoke<void>(browserChannel.closeBrowser)
    return
  }

  async anyWebSpaceTabOpened(contextId: string): Promise<boolean> {
    return window.api.invoke<boolean>(
      browserChannel.anyWebSpaceTabOpened,
      this.contextIdentifierOf(contextId),
    )
  }

  async anyAccountSpaceTabOpened(contextId: string): Promise<boolean> {
    return window.api.invoke<boolean>(
      browserChannel.anyAccountSpaceTabOpened,
      this.contextIdentifierOf(contextId),
    )
  }

  getOpenedUrls(contextId: string) {
    return window.api.invoke<string[]>(
      browserChannel.getOpenedUrls,
      this.contextIdentifierOf(contextId),
    )
  }

  getAccountCollects(data: { platformName: string; accountId: string; id: string }[]) {
    return window.api.invoke<Collect[]>(browserChannel.getAccountUrl, data)
  }

  syncFavorites(contextFavorites: Record<string, ContextFavorite[]>) {
    return window.api.invoke<void>(
      browserChannel.syncFavorites,
      this.teamIdentifier(),
      contextFavorites,
    )
  }

  getAccountSession(contextId: string, platform: Platform) {
    return window.api.invoke<AccountSession>(
      browserChannel.getAccountSession,
      this.contextIdentifierOf(contextId),
      platform.name,
    )
  }

  getWebSession(contextId: string, url: string) {
    return window.api.invoke<AccountSession>(
      browserChannel.getWebSession,
      this.contextIdentifierOf(contextId),
      url,
    )
  }
}

export function useBrowserService() {
  const { userInfo } = useContextStore((state) => ({
    userInfo: state.userInfo,
  }))
  const currentTeamId = useCurrentTeamId()
  if (userInfo === null) throw new Error('userInfo is null')
  return useMemo(() => new BrowserService(userInfo.id, currentTeamId), [currentTeamId, userInfo.id])
}

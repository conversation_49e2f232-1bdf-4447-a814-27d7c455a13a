import { type PlatformName } from '@common/model/platform-name'
import { deviceId, version } from '@renderer/infrastructure/utils/version'
import type { Socket } from 'socket.io-client'
import { io } from 'socket.io-client'

export const socketLocalClient = new (class SocketLocalClient {
  public socketLocal: Socket | null = null

  public async createAuth(platformName: PlatformName | '未知'): Promise<boolean | null> {
    if (!this.socketLocal) {
      return false
    }

    return new Promise((resolve) => {
      if (!this.socketLocal) {
        return resolve(false)
      }
      this.socketLocal.send({ type: 'create-auth-view', data: { platformName } }, () => {
        resolve(true)
      })
    })
  }

  public async connectLocal({ openId, appId }: { openId: string; appId: string }): Promise<void> {
    const startPort = 30000
    const endPort = 30010

    this.socketLocal?.disconnect()
    this.socketLocal = null

    for (let port = startPort; port <= endPort; port++) {
      const isConnected = await new Promise((resolve) => {
        this.socketLocal = io(`ws://localhost:${port}`, {
          transports: ['websocket'],
          auth: {
            openId,
            appId,
          },
          timeout: 500,
        })

        this.socketLocal.on('connect', () => {
          resolve(true)
        })

        this.socketLocal.on('message', (message, ack) => {
          console.log(message, ack)
          ack()
        })

        this.socketLocal.on('connect_error', () => {
          this.socketLocal?.removeAllListeners()
          this.socketLocal?.disconnect()
          this.socketLocal = null
          resolve(false)
        })
      })

      if (isConnected) {
        break
      }
    }
  }
})()

export class SocketClient<TMessage> {
  public socket: Socket | null = null

  constructor(
    private token: string,
    private handleMessage: (data: TMessage) => void,
  ) {}

  private setupListeners(): void {
    if (!this.socket) return
    this.socket.on('connect', () => {
      console.debug('Connected to socket server')
    })

    this.socket.on('disconnect', () => {
      console.debug('Disconnected from socket server')
    })

    this.socket.on('error', (error) => {
      console.error('Socket error:', error)
    })

    this.socket.on('messages', (data: TMessage) => {
      console.debug('Received message:', data)
      this.handleMessage(data)
    })
  }

  public disconnect(): void {
    if (!this.socket) return
    this.socket.disconnect()
  }

  public connect(): void {
    this.socket = io(import.meta.env.VITE_WSS_URL, {
      transports: ['websocket'],
      path: import.meta.env.VITE_WSS_PATH,
      query: { authorization: this.token, deviceId, version },
    })
    this.setupListeners()
  }
}

import type { VideoFileInfo } from '@renderer/infrastructure/model'
import { ImageFileInfo } from '@renderer/infrastructure/model'
import { electronService } from './electron-service'
import { localPath2Url } from '@common/protocol'
import { isElectron } from '@common/isElectron'

class MediaService {
  captureVideo(videoInfo: VideoFileInfo, second: number = 0) {
    return new Promise<ImageFileInfo>((resolve, reject) => {
      const video = document.createElement('video')
      video.style.display = 'none' // 确保视频不会在页面上显示
      document.body.appendChild(video) // 将视频元素添加到 DOM 中
      video.crossOrigin = 'anonymous' // 关键！
      video.src = localPath2Url(videoInfo.filePath)
      video.addEventListener('loadeddata', () => {
        video.currentTime = second
        video.addEventListener('seeked', () => {
          const canvas = document.createElement('canvas')
          const context = canvas.getContext('2d')!

          canvas.width = video.videoWidth
          canvas.height = video.videoHeight
          context.drawImage(video, 0, 0, canvas.width, canvas.height)
          canvas.toBlob(async (blob) => {
            if (blob === null) {
              return
            }
            try {
              // 压缩质量 (mb) 小于512kb 默认80
              const buffer = await blob.arrayBuffer()
              const qualityNumber =
                buffer.byteLength < 512 * 1024 ? 80 : buffer.byteLength / 1024 / 1024 <= 5 ? 50 : 20

              if (isElectron()) {
                const compressed = await electronService.bufferCompression(buffer, qualityNumber)

                const path = await electronService.saveImageFile(compressed, 'jpg')
                const imageFileInfo = await electronService.getImageFileInfo(path)
                resolve(imageFileInfo)
                return
              }

              resolve(
                ImageFileInfo.of(
                  video.videoWidth,
                  video.videoHeight,
                  buffer.byteLength,
                  'jpg',
                  new File([blob], `${Date.now()}.jpg`, {
                    type: blob.type,
                    lastModified: Date.now(),
                  }),
                ),
              )
              return
            } catch (e) {
              console.error(e)
              reject(e)
            }
          })
          document.body.removeChild(video) // 截图完成后，将视频元素从 DOM 中移除
        })
      })
      video.load()
    })
  }
}

export const mediaService = new MediaService()

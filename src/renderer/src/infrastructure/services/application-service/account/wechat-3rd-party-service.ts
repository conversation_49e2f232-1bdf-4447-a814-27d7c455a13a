import { useCallback, useContext, useMemo } from 'react'
import { useWechat3rdPartyApi } from '@renderer/infrastructure/services/entity-service'
import type { SpiderAccount } from '@renderer/infrastructure/model'
import { WechatShiPinHao3rdPartySubAccount } from '@renderer/infrastructure/model'
import { platformService } from '@renderer/infrastructure/services'
import {
  BusinessError,
  BusinessErrorCode,
} from '@renderer/infrastructure/model/error/businessError'
import { ScenarioContext, type AccountsWithTokens } from '@renderer/context/scenario-context'
import { useNotify } from '@renderer/hooks/use-notify'

/**
 * 微信第三方ipad平台服务
 * @returns
 */
export function useWechat3rdPartyService() {
  const {
    getAccountSession: apiGetAccountSession,
    refreshAccountSession,
    lockWechatAccount,
    unlockWechatAccount,
    keepTokenAlive,
  } = useWechat3rdPartyApi()

  const { notifyService } = useNotify()

  const scenario = useContext(ScenarioContext)

  const getAccountSession = useCallback(
    async (account: WechatShiPinHao3rdPartySubAccount, waitForAccountFree: boolean) => {
      try {
        const token = (await lockWechatAccount([account.parentAccountId]))[0].wxkey
        if (!token) {
          throw new Error('获取账号session失败，token不存在')
        }
        try {
          let accountSession = await apiGetAccountSession(account, token)
          const result = await platformService.sessionDetect(account, accountSession, '', true)
          console.log('微信cookie检测结果', result)
          if (result.state !== '正常') {
            accountSession = await refreshAccountSession(account, token)
          }
          return accountSession
        } finally {
          await unlockWechatAccount(account.parentAccountId, token)
        }
      } catch (e) {
        if (e instanceof BusinessError && e.code === BusinessErrorCode.AccountLocked) {
          if (waitForAccountFree) {
            await new Promise((resolve) => setTimeout(resolve, 3000))
            return await getAccountSession(account, waitForAccountFree)
          } else throw new BusinessError(BusinessErrorCode.AccountLocked, '账号正在使用中')
        } else throw e
      }
    },
    [apiGetAccountSession, lockWechatAccount, refreshAccountSession, unlockWechatAccount],
  )

  const getAccountSessionWithScenarioContext = useCallback(
    async (account: WechatShiPinHao3rdPartySubAccount, waitForAccountFree: boolean) => {
      if (!scenario) throw new Error('获取账号session失败，场景上下文不存在')
      const token = scenario.unlockTokens.get(account.parentAccountId)
      if (!token) {
        throw new Error('获取账号session失败，token不存在')
      }
      try {
        let accountSession = await apiGetAccountSession(account, token)
        const result = await platformService.sessionDetect(account, accountSession, '', true)
        console.log('微信cookie检测结果', result)
        if (result.state !== '正常') {
          accountSession = await refreshAccountSession(account, token)
        }
        return accountSession
      } catch (e) {
        if (e instanceof BusinessError && e.code === BusinessErrorCode.AccountLocked) {
          if (waitForAccountFree) {
            await new Promise((resolve) => setTimeout(resolve, 3000))
            return await getAccountSessionWithScenarioContext(account, waitForAccountFree)
          } else throw new BusinessError(BusinessErrorCode.AccountLocked, '账号正在使用中')
        } else throw e
      }
    },
    [apiGetAccountSession, refreshAccountSession, scenario],
  )

  const getAccountSessionWithToken = useCallback(
    async (account: WechatShiPinHao3rdPartySubAccount, token: string) => {
      try {
        let accountSession = await apiGetAccountSession(account, token)
        const result = await platformService.sessionDetect(account, accountSession, '', true)
        console.log('微信cookie检测结果', result)
        if (result.state !== '正常') {
          accountSession = await refreshAccountSession(account, token)
        }
        return accountSession
      } catch (e) {
        if (e instanceof BusinessError && e.code === BusinessErrorCode.AccountLocked) {
          throw new BusinessError(BusinessErrorCode.AccountLocked, '账号正在使用中')
        } else throw e
      }
    },
    [apiGetAccountSession, refreshAccountSession],
  )

  const lockParentAccount = useCallback(
    async (account: WechatShiPinHao3rdPartySubAccount[]) => {
      const accountIds = Array.from(new Set(account.map((item) => item.parentAccountId)))
      if (accountIds.length === 0) return []
      return await lockWechatAccount(accountIds)
    },
    [lockWechatAccount],
  )

  const updateAccountLocks = useCallback(
    async (
      accountsNew: SpiderAccount[],
      accountsWithTokensInUse: AccountsWithTokens = {
        tokens: new Map(),
        accounts: [],
      },
    ) => {
      // 从新账号列表提取父账号ID
      const newParentAccountIds = accountsNew
        .filter((x) => x instanceof WechatShiPinHao3rdPartySubAccount)
        .map((account) => account.parentAccountId)

      // 获取当前使用中的父账号ID
      const parentAccountIdsInUse = Array.from(accountsWithTokensInUse.tokens.keys())

      // 找出需要解锁的token（当前使用中但不在新账号列表中）
      const tokensToUnlock = new Map(
        Array.from(accountsWithTokensInUse.tokens.entries()).filter(
          ([id]) => !newParentAccountIds.includes(id),
        ),
      )

      // 创建新的token集合，保留新账号列表中存在的token
      const newTokens = new Map(
        Array.from(accountsWithTokensInUse.tokens.entries()).filter(([id]) =>
          newParentAccountIds.includes(id),
        ),
      )

      // 直接筛选出需要锁定的账号（在新列表中但其父账号不在当前使用中）
      const accountsToLock = accountsNew.filter(
        (x): x is WechatShiPinHao3rdPartySubAccount =>
          x instanceof WechatShiPinHao3rdPartySubAccount &&
          !parentAccountIdsInUse.includes(x.parentAccountId),
      )

      // 如果有需要锁定的账号
      if (accountsToLock.length > 0) {
        // 使用 lockParentAccount 锁定账号
        try {
          const newLocks = await lockParentAccount(accountsToLock)

          for (const item of newLocks) {
            newTokens.set(item.platformAccountId, item.wxkey)
          }
        } catch (e) {
          if (e instanceof BusinessError) {
            notifyService.error(e.message)
          }
          throw e
        }
      }

      // 解锁不再需要的token
      if (tokensToUnlock.size > 0) {
        for (const [id, token] of tokensToUnlock) {
          void unlockWechatAccount(id, token)
        }
      }

      return {
        accounts: accountsNew,
        tokens: newTokens,
      } satisfies AccountsWithTokens as AccountsWithTokens
    },
    [lockParentAccount, notifyService, unlockWechatAccount],
  )

  return useMemo(
    () => ({
      getAccountSession,
      getAccountSessionWithScenarioContext,
      getAccountSessionWithToken,
      lockParentAccount,
      unlockWechatAccount,
      keepTokenAlive,
      updateAccountLocks,
    }),
    [
      getAccountSession,
      getAccountSessionWithScenarioContext,
      getAccountSessionWithToken,
      lockParentAccount,
      unlockWechatAccount,
      keepTokenAlive,
      updateAccountLocks,
    ],
  )
}

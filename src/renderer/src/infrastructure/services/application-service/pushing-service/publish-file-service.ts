import { useUploadFileService } from '../upload-file-service'
import { electronService, identifierService } from '../infrastructure-service'
import { useCallback, useMemo } from 'react'

/**
 * 发布文件存储服务
 * @description 仅用于发布功能，目前无法公网获取！
 */
export function usePublishFileService() {
  const uploadFileService = useUploadFileService('tianyiyun')

  /**
   * 上传文件
   * @param filePath 文件路径
   * @returns 文件key
   */
  const uploadFile = useCallback(
    async (filePath: string | File) => {
      const file = await electronService.readFileBuffer(filePath)

      const { serviceUrl: putUrl, key } = await uploadFileService.getUploadUrl(
        'cloud-publish',
        identifierService.generateUUIDnew(),
      )

      await uploadFileService.putUint8Array(putUrl, file)

      return key
    },
    [uploadFileService],
  )

  return useMemo(
    () => ({
      uploadFile,
    }),
    [uploadFile],
  )
}

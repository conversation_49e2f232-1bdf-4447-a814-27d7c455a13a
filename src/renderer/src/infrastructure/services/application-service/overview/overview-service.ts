import { useMemo } from 'react'
import { type AuthorizeService, platformService, useAuthorizeService } from '../index'
import type {
  Account,
  AccountFilterItem,
  Operator,
  AccountState,
} from '@renderer/infrastructure/model'
import type { Group } from '@renderer/infrastructure/model/group/group'
import { SpiderAccount } from '@renderer/infrastructure/model'
import { type DataColumn } from '@yixiaoer/platform-service'
import type { OverviewApi } from '@renderer/infrastructure/services/entity-service'
import { useOverviewApi } from '@renderer/infrastructure/services/entity-service'
import { useAccountApi } from '@renderer/infrastructure/services/entity-service/cloud/account-api'
import type { OverviewContentType, PublishOverviewRawItem } from '@common/structure'
import type { DateRange } from '@renderer/infrastructure/model/utils/date-range'
import { BusinessError } from '@renderer/infrastructure/model/error/businessError'
import type { NotifyService } from '@renderer/hooks/use-notify'
import { useNotify } from '@renderer/hooks/use-notify'
import { type BotTestRequest, type BotRequest } from '@renderer/infrastructure/model/bot'

export function useOverviewService() {
  const overviewApi = useOverviewApi()
  const accountApi = useAccountApi()
  const authorizeService = useAuthorizeService()
  const { notifyService } = useNotify()
  return useMemo(
    () => new OverviewCloudService(accountApi, overviewApi, authorizeService, notifyService),
    [accountApi, authorizeService, notifyService, overviewApi],
  )
}

export class OverviewCloudService {
  constructor(
    private accountApi: ReturnType<typeof useAccountApi>,
    private overviewApi: OverviewApi,
    private authorizeService: AuthorizeService,
    private notifyService: NotifyService,
  ) {}

  async getAccounts({
    size,
    page,
    platform,
    nameKeyword,
    accountState,
    groupId,
  }: {
    platform: string
    size: number
    page: number
    nameKeyword?: string
    accountState?: AccountState | null
    groupId?: string | null
  }) {
    const response = await this.accountApi.getAccountsByPage(platform, size, page, {
      nameKeyword,
      accountState,
      groupId,
    })
    return { ...response, data: response.data.filter((item) => item instanceof SpiderAccount) }
  }

  getRobots() {
    return this.overviewApi.getRobots()
  }

  testRobots(data: BotTestRequest) {
    return this.overviewApi.testRobots(data)
  }

  saveRobots(data: BotRequest) {
    return this.overviewApi.putRobots(data)
  }

  async getAccountOverviews(accountIds: string[]) {
    return this.overviewApi.getAccountOverviews(accountIds)
  }

  async getAccountOverviewsSummary(platform: string) {
    return this.overviewApi.getAccountOverviewsSummary(platform)
  }

  async saveAccountOverview(
    accountId: string,
    info: {
      updateTime: number
      value: { video?: DataColumn[]; dynamic?: DataColumn[]; article?: DataColumn[] }
    },
  ) {
    return this.overviewApi.putAccountOverview(accountId, info)
  }

  async queryOverview(account: SpiderAccount) {
    try {
      const { session: accountSession, release } =
        await this.authorizeService.getAccountSession(account)
      try {
        const result = {
          accountOverview: await platformService.queryAccountOverview(
            account,
            account.platform.name,
            accountSession!,
          ),
          publishOverviews: await platformService.queryPublishOverviews(
            account,
            account.platform.name,
            accountSession!,
          ),
        }
        return result
      } finally {
        void release?.()
      }
    } catch (e) {
      if (e instanceof BusinessError) {
        this.notifyService.error(e.message)
      }
      throw e
    }
  }

  async savePublishOverviews(account: Account, publishOverviews: PublishOverviewRawItem[]) {
    return await this.overviewApi.putPublishOverviews(account, publishOverviews)
  }

  queryPublishOverviewsHeaders(platformName: string, value: OverviewContentType | undefined) {
    return platformService.queryPublishOverviewsHeaders(platformName, value)
  }

  async getPublishOverviews(
    platformName: string,
    contentType: OverviewContentType | undefined,
    titleKeyword: string,
    accountFilterItem: AccountFilterItem | null,
    operator: Operator | null,
    dateRange: DateRange | null,
    pageIndex: number,
    pageSize: number,
  ) {
    return this.overviewApi.searchPublishOverviews(
      platformName,
      contentType,
      titleKeyword,
      accountFilterItem,
      operator,
      dateRange,
      pageIndex,
      pageSize,
    )
  }

  async getPublishOverviewsSummary(
    platformName: string,
    contentType: OverviewContentType | undefined,
    titleKeyword: string,
    accountFilterItem: AccountFilterItem | null,
    operator: Operator | null,
    dateRange: DateRange | null,
  ) {
    return this.overviewApi.getPublishOverviewsSummary(
      platformName,
      contentType,
      titleKeyword,
      accountFilterItem,
      operator,
      dateRange,
    )
  }

  getPublishOverviewsExcel(
    platformName: string,
    contentType: OverviewContentType | undefined,
    titleKeyword: string,
    accountFilterItem: AccountFilterItem | null,
    operator: Operator | null,
    dateRange: DateRange | null,
  ) {
    return this.overviewApi.getPublishOverviewsExcel(
      platformName,
      contentType,
      titleKeyword,
      accountFilterItem,
      operator,
      dateRange,
    )
  }

  getAccountOverviewsExcel(
    platform: string,
    nameKeyword?: string,
    accountState?: AccountState | null,
    group?: Group | null,
  ) {
    return this.overviewApi.getAccountOverviewsExcel(platform, nameKeyword, accountState, group)
  }
}

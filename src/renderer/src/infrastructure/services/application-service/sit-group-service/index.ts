import { useMemo, useCallback } from 'react'
import { useUserApiService } from '../../network-service'
import { Response2Space } from '../../entity-service/cloud/space-api'
import type { PagedResponse } from '@renderer/infrastructure/types'
import type { SpaceResponse } from '@renderer/infrastructure/types/space-response'
import { useAccountApi } from '../../entity-service/cloud/account-api'
import { useFavoriteGroupApi } from '@renderer/infrastructure/services/entity-service/favorite-group-api'
import type { Collect, SpiderAccount } from '@renderer/infrastructure/model'
import { allPlatform, type WebSpace } from '@renderer/infrastructure/model'
import { useAuthorizeService, useWebSpaceManageService } from '@renderer/infrastructure/services'
import { platformNames } from '@common/model/platform-name'
// import { normalizeURL } from '@renderer/infrastructure/utils/serializable'

/**
 * GroupsListResponse
 */
export interface SitGroupsList {
  data: SitGroupsDTO[]
  page: number
  size: number
  totalPage: number
  totalSize: number
}

export interface CollGroupsList {
  data: CollGroupsDTO[]
  page: number
  size: number
  totalPage: number
  totalSize: number
}

export type CollGroupsDTOOriginal = {
  accountId: string
  browserId: string
  groupId: string
  originalId: string
  spaceName: string
  platformName: string
  color: string
  spaceUrl: string
  icon: string
  websiteUrl: string
  isAccount: boolean
}

export interface CollGroupsDTO {
  originals: CollGroupsDTOOriginal[]
  createdAt: number
  id: string
  name: string
}

/**
 * GroupsDTO
 */
export interface SitGroupsDTO {
  browsers: string[]
  createdAt: number
  id: string
  name: string
}

export function useWebGroupService() {
  const userApiService = useUserApiService()
  const accountApiService = useAccountApi()
  const authorizeService = useAuthorizeService()
  const webSpaceManageService = useWebSpaceManageService()
  const { getFavoriteGroup } = useFavoriteGroupApi()

  const addSitGroup = useCallback(
    (name: string) => {
      return userApiService.post('/site-spaces-groups', { name })
    },
    [userApiService],
  )

  const updateSitGroup = useCallback(
    (groupId: string, name?: string, browsers?: string[]) => {
      return userApiService.patch(`/site-spaces-groups/${groupId}`, { name, browsers })
    },
    [userApiService],
  )

  const deleteSitGroup = useCallback(
    (groupId: string) => {
      return userApiService.delete(`/site-spaces-groups/${groupId}`)
    },
    [userApiService],
  )

  const getSitGroup = useCallback(
    ({ page, name }: { page: number; name?: string }) => {
      return userApiService.get('/site-spaces-groups', {
        page: page.toString(),
        size: '20',
        name,
      }) as Promise<SitGroupsList>
    },
    [userApiService],
  )

  const getSitGroupAll = useCallback(() => {
    return userApiService.get('/site-spaces-groups', {
      page: '1',
      size: '9999',
    }) as Promise<SitGroupsList>
  }, [userApiService])

  const getSpace = useCallback(async () => {
    const spaceResponses = await userApiService.get<PagedResponse<SpaceResponse>>('/site-spaces', {
      page: '1',
      size: '9999',
      time: '0',
      isolation: 'true',
    })
    return spaceResponses.data.map((item) => Response2Space(item))
  }, [userApiService])

  const getAccountAll = useCallback(() => accountApiService.getAccountAll(), [accountApiService])

  const openFavoriteGroup = useCallback(
    async (id: string, originals?: CollGroupsDTOOriginal[]) => {
      // 这里采用兼容的方式，因为之前接口中，originals是列表中的缓存值，现在加入快捷入口，快捷入口只有id引用，获取完整信息的操作应该封装在这个服务内部。
      if (!originals) {
        const group = await getFavoriteGroup(id)
        originals = group.originals
      }

      const urls: Collect[] = []
      const pNames: { platformName: string; accountId: string; id: string }[] = []

      originals.forEach((item) => {
        if (item.platformName !== platformNames.Other && item.isAccount) {
          pNames.push({
            platformName: item.platformName,
            accountId: item.accountId,
            id: item.browserId,
          })
        } else {
          urls.push({
            url: item.websiteUrl || item.spaceUrl,
            accountId: item.accountId,
            id: item.originalId,
          })
        }
      })

      originals.forEach((original) => {
        if (original.platformName !== platformNames.Other && original.isAccount) {
          void authorizeService.openAccountSpace(
            {
              spaceId: original.browserId,
              accountId: original.accountId,
              displayName: original.spaceName,
              platform: allPlatform.find((item) => item.name === original.platformName),
            } as SpiderAccount,
            urls,
          )
        } else {
          void webSpaceManageService.openFavoriteByCollect(
            {
              displayName: original.spaceName || original.spaceUrl,
              accountId: original.accountId,
              id: original.accountId,
              name: original.spaceName,
              color: original.color,
              url: original.websiteUrl || original.spaceUrl,
              icon: original.icon,
            } as WebSpace,
            urls,
          )
        }
      })
    },
    [authorizeService, getFavoriteGroup, webSpaceManageService],
  )

  const openFavoriteGroupInNewWindow = useCallback(
    async (id: string, originals?: CollGroupsDTOOriginal[]) => {
      // 这里采用兼容的方式，因为之前接口中，originals是列表中的缓存值，现在加入快捷入口，快捷入口只有id引用，获取完整信息的操作应该封装在这个服务内部。
      if (!originals) {
        const group = await getFavoriteGroup(id)
        originals = group.originals
      }
      for (const original of originals) {
        if (original.platformName !== platformNames.Other) {
          try {
            await authorizeService.openAccountSpaceInNewWindow(id, {
              accountId: original.accountId,
              displayName: original.spaceName,
              platform: allPlatform.find((item) => item.name === original.platformName),
            } as SpiderAccount)
          } catch (error) {
            console.error(error)
          }
        } else {
          try {
            await webSpaceManageService.newOpenSpace(id, {
              id: original.accountId,
              name: original.spaceName,
              color: original.color,
              url: original.websiteUrl || original.spaceUrl,
              icon: original.icon,
            } as WebSpace)
          } catch (error) {
            console.error(error)
          }
        }
      }
    },
    [authorizeService, getFavoriteGroup, webSpaceManageService],
  )

  return useMemo(
    () => ({
      addSitGroup,
      updateSitGroup,
      deleteSitGroup,
      getSitGroup,
      getSitGroupAll,
      getSpace,
      getAccountAll,
      openFavoriteGroup,
      openFavoriteGroupInNewWindow,
    }),
    [
      addSitGroup,
      updateSitGroup,
      deleteSitGroup,
      getSitGroup,
      getSitGroupAll,
      getSpace,
      getAccountAll,
      openFavoriteGroup,
      openFavoriteGroupInNewWindow,
    ],
  )
}

import { useMemo } from 'react'
import { useUserApiService } from '../../network-service'
import type { ApiService } from '../../network-service/api-service'
import { useUploadFileService } from '../upload-file-service'
import { uiEvents } from '@common/events/ui-events'
import type { AssetLibraryItemResponse } from '@renderer/infrastructure/types/asset-library-item-response'
import type { PagedResponse } from '@renderer/infrastructure/types'

import type { IAssetLibraryItem } from '@renderer/infrastructure/model'
import { ByteSize } from '@renderer/infrastructure/model/utils/byte-size'
import { toast } from 'sonner'

export function useAssetLibraryService() {
  const userApiService = useUserApiService()
  const uploadFileService = useUploadFileService()
  return useMemo(
    () => new AssetLibraryService(uploadFileService, userApiService),
    [uploadFileService, userApiService],
  )
}

export interface IAssetGroup {
  id: string
  name: string
}

export enum EAssetLibraryFileState {
  Idle = 'Idle',
  Uploading = 'Uploading',
  Finished = 'Finished',
  Error = 'Error',
}

export interface IAssetLibraryFile {
  groupId: string
  fileName: string
  Icon: React.ReactNode
  file: File
  state: EAssetLibraryFileState
  progress: number
  isVideo: boolean
}

let listeners: Array<() => void> = []
let uploadFiles: IAssetLibraryFile[] = []
let currentUpdateFileNum = 0

export function transfromFileBuffer(file: File): Promise<Uint8Array> {
  return new Promise((resolve, reject) => {
    const fileReader = new FileReader()
    fileReader.onload = () => {
      if (fileReader.result instanceof ArrayBuffer) {
        resolve(new Uint8Array(fileReader.result))
      } else {
        reject(new Error('File reading did not result in ArrayBuffer.'))
      }
    }
    fileReader.readAsArrayBuffer(file)
  })
}

export function getVideoFirstFrame(
  file: File,
  filename: string,
): Promise<{
  fileWidth: number
  fileHeight: number
  fileLength: number
  fileThumbnail: File
}> {
  return new Promise(function (resolve) {
    let fileLength = 0
    const video = document.createElement('video')
    video.setAttribute('crossOrigin', 'Anonymous') // 处理跨域
    video.setAttribute('preload', 'metadata')
    video.setAttribute('src', URL.createObjectURL(file))

    video.currentTime = 3
    video.muted = true
    video.autoplay = true

    video.requestVideoFrameCallback(() => {
      video.pause()

      const { videoWidth, videoHeight } = video
      fileLength = Number.parseInt(`${video.duration}`, 10)
      video.setAttribute('width', `${video.videoWidth}px`)
      video.setAttribute('height', `${video.videoHeight}px`)
      const canvas = document.createElement('canvas')
      canvas.width = videoWidth
      canvas.height = videoHeight

      canvas.getContext('2d')!.drawImage(video, 0, 0, videoWidth, videoHeight)

      canvas.toBlob((blob) => {
        const fileThumbnail = new File([blob!], filename, {
          type: 'image/png',
          lastModified: Date.now(),
        })

        resolve({
          fileWidth: videoWidth,
          fileHeight: videoHeight,
          fileLength,
          fileThumbnail,
        })
      }, 'image/png')
    })
  })
}

function getImageDimensionsFromFile(file: File): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => {
      resolve({ width: img.width, height: img.height })
      URL.revokeObjectURL(img.src)
    }
    img.onerror = reject
    img.src = URL.createObjectURL(file)
  })
}

export class AssetLibraryService {
  constructor(
    private uploadFileService: ReturnType<typeof useUploadFileService>,
    private userApiService: ApiService,
  ) {}

  async readFile(filepath: string | File) {
    return typeof filepath === 'string'
      ? window.api.invoke<Uint8Array>(uiEvents.readFileBuffer, filepath)
      : filepath
  }

  async uploadImageAsset(fileName: string, file: File | Uint8Array) {
    try {
      const dump = file instanceof File ? await transfromFileBuffer(file) : file
      const { serviceUrl: putUrl, key } = await this.uploadFileService.getUploadUrl(
        'material-library',
        Date.now().toString() + fileName,
      )

      await this.uploadFileService.putUint8Array(putUrl, dump)

      return key
    } catch {
      return false
    }
  }

  async uploadImageWechat(fileName: string, file: File | Uint8Array) {
    const dump = file instanceof File ? await transfromFileBuffer(file) : file
    const { serviceUrl: putUrl, key } = await this.uploadFileService.getUploadUrl(
      'wechat-pubish',
      Date.now().toString() + fileName,
    )

    await this.uploadFileService.putUint8Array(putUrl, dump)
    return { key, url: new URL(putUrl).origin + '/' + key }
  }

  // 批量 uploadImageWechat 并且过滤图片超过10M
  async uploadImageWechatBatch(files: File[]) {
    const results = await Promise.all(
      files.map(async (file) => {
        if (file.size > ByteSize.fromMB(10).bytes) {
          return null
        }
        const res = await this.uploadImageWechat(file.name, file)
        return { ...res, file }
      }),
    )
    if (results.includes(null)) {
      toast.warning('已过滤超过10M的图片和视频')
    }
    return results.filter((result) => result !== null)
  }

  updateGroup(groupId: string, name: string) {
    return this.userApiService.put(`/material/groups/${groupId}`, { name })
  }

  deleteAsset(id: string) {
    return this.userApiService.delete(`/material/${id}`)
  }

  getAssetGroup() {
    return this.userApiService.get('/material/groups') as Promise<IAssetGroup[]>
  }

  createAssetGroup(name: string) {
    return this.userApiService.post('/material/groups', { name: name }) as Promise<IAssetGroup>
  }

  updateAssetGroup(groupId: string, name: string) {
    return this.userApiService.put(`/material/groups/${groupId}`, {
      name: name,
    }) as Promise<IAssetGroup>
  }

  updateMaterialGroup(materialId: string, groupId: string) {
    return this.userApiService.post(`/material/${materialId}/set-group`, { groupId })
  }

  deleteAssetGroup(groupId: string) {
    return this.userApiService.delete(`/material/groups/${groupId}`)
  }

  async getAssetMeterials({
    groupId,
    size,
    page,
    type,
  }: {
    groupId?: string
    type: 'system' | 'video' | 'image' | string
    size: number
    page: number
  }) {
    const response = await this.userApiService.get<PagedResponse<AssetLibraryItemResponse>>(
      '/material',
      {
        groupId: groupId,
        size: size.toString(),
        page: page.toString(),
        type,
      },
    )

    return {
      ...response,
      data: response.data.map((item) => {
        return {
          ...item,
          size: ByteSize.fromB(item.size),
        }
      }),
    } satisfies PagedResponse<IAssetLibraryItem> as PagedResponse<IAssetLibraryItem>
  }

  uplodaMaterial({
    fileName,
    groupId,
    thumbPath,
    filePath,
    type,
    width,
    height,
  }: {
    fileName: string
    groupId: string
    thumbPath: string
    filePath: string
    type: 'system' | 'video' | 'image'
    width: number
    height: number
  }) {
    return this.userApiService.post('/material', {
      fileName,
      groupId,
      thumbPath,
      filePath,
      type,
      width,
      height,
    })
  }

  subscribe(callback: () => void) {
    listeners.push(callback)
    return () => {
      listeners = listeners.filter((cb) => cb !== callback)
    }
  }

  async uploadFileTask(orgIndex: number, file: IAssetLibraryFile | IAssetLibraryFile[]) {
    const files = Array.isArray(file) ? file : [file]

    for (let index = 0; index < files.length; index++) {
      const item = file[index] as IAssetLibraryFile
      uploadFiles[orgIndex + index].state = EAssetLibraryFileState.Uploading
      let result = ''

      const res = await this.uploadImageAsset(item.fileName, item.file)

      let inWidth = 0
      let inHeight = 0

      if (!res) {
        uploadFiles[orgIndex + index].state = EAssetLibraryFileState.Error
        uploadFiles = [...uploadFiles]
        currentUpdateFileNum -= 1
        listeners.forEach((cb) => cb())
        continue
      } else {
        result = res
      }

      let thumbPath = ''
      const [baseName] = item.fileName.match(/([^/\\]+)\.([^.]+)$/) ?? []

      if (item.isVideo) {
        try {
          const thumbnailFilename = baseName + '_thumbnail.png'
          const { fileThumbnail, fileWidth, fileHeight } = await getVideoFirstFrame(
            item.file,
            thumbnailFilename,
          )

          inWidth = fileWidth
          inHeight = fileHeight

          const res = await this.uploadImageAsset(thumbnailFilename, fileThumbnail)
          if (res) {
            thumbPath = res
          }
        } catch {
          // ignore
        }
      } else {
        const { width, height } = await getImageDimensionsFromFile(item.file)
        inWidth = width
        inHeight = height
      }

      try {
        await this.uplodaMaterial({
          fileName: item.fileName,
          groupId: item.groupId,
          filePath: result,
          thumbPath,
          type: item.isVideo ? 'video' : 'image',
          width: inWidth,
          height: inHeight,
        })

        uploadFiles[orgIndex + index].state = EAssetLibraryFileState.Finished
      } catch {
        uploadFiles[orgIndex + index].state = EAssetLibraryFileState.Error
      }
      currentUpdateFileNum -= 1
      uploadFiles = [...uploadFiles]
      listeners.forEach((cb) => cb())
    }
  }

  addUploadFile(file: IAssetLibraryFile | IAssetLibraryFile[]) {
    const orgIndex = uploadFiles.length

    if (Array.isArray(file)) {
      uploadFiles = [...uploadFiles, ...file]
      currentUpdateFileNum += file.length
    } else {
      uploadFiles = [...uploadFiles, file]
      currentUpdateFileNum += 1
    }

    void this.uploadFileTask(orgIndex, file)
  }

  getCurrentUpdateFileNum() {
    return currentUpdateFileNum
  }

  getUplodaFiles() {
    return uploadFiles
  }
}

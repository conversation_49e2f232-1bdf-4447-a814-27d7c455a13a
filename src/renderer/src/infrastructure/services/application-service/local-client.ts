import { type PlatformName } from '@common/model/platform-name'
import { deviceId, version } from '@renderer/infrastructure/utils/version'
import { useLocalStore } from '@renderer/store/localStore'
import type { Socket } from 'socket.io-client'
import { io } from 'socket.io-client'
import { useInnerContextStore } from '@renderer/store/contextStore'
import { type AccountSession } from '@common/structure'
import type { ImageTask, VideoTask } from './rpa-service'
import { authorizeEvents, eventBus } from '@renderer/infrastructure/event-bus'
import { type Collect } from '@renderer/infrastructure/model'

/**
 * 统一的本地服务客户端
 * 整合了 HTTP API 调用和 WebSocket 连接功能
 */
export class LocalClient {
  // 私有属性
  private socketLocal: Socket | null = null
  private port: number | null = null
  private connectionPromise: Promise<void> | null = null
  private currentOpenId: string | null = null
  private cachedClientId: string | null = null

  // 公共接口
  get isConnected(): boolean {
    return this.socketLocal?.connected === true
  }

  /**
   * 检查连接状态，如果未连接则尝试连接
   * @returns Promise<boolean> 连接是否成功
   */
  async checkConnection(): Promise<boolean> {
    // 如果已连接，直接返回 true
    if (this.isConnected) {
      return true
    }

    // 如果正在连接中，等待连接结果
    if (this.connectionPromise) {
      try {
        await this.connectionPromise
        return this.isConnected
      } catch (error) {
        // 连接失败，返回 false
        return false
      }
    }

    // 开始新的连接尝试
    this.connectionPromise = this.attemptConnection()

    try {
      await this.connectionPromise
      return this.isConnected
    } catch (error) {
      console.debug('连接尝试失败:', error)
      return false
    } finally {
      this.connectionPromise = null
    }
  }

  // HTTP API 方法
  async getClientId(): Promise<{ clientId: string }> {
    // 如果有缓存的 clientId，直接返回
    if (this.cachedClientId) {
      console.debug('使用缓存的 ClientId:', this.cachedClientId)
      return { clientId: this.cachedClientId }
    }

    // 检查连接状态
    if (!(await this.checkConnection())) throw new Error('无法连接客户端，请确保服务已启动')

    // 连接成功后应该已经有缓存的 clientId
    if (this.cachedClientId) {
      return { clientId: this.cachedClientId }
    }

    throw new Error('无法找到ClientId，请确保服务已启动')
  }

  async accountUrls(data: { platformName: string; accountId: string }[]) {
    if (!(await this.checkConnection())) {
      throw new Error('无法找到可用客户端，请确保客户端已启动')
    }

    const response = await fetch(`http://127.0.0.1:${this.port}/account-urls`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ data }),
    })

    return response.json()
  }

  async startVideoRpa(data: { video: string; cover: string }, config: VideoTask) {
    if (!(await this.checkConnection())) {
      throw new Error('无法找到可用客户端，请确保客户端已启动')
    }

    const response = await fetch(`http://127.0.0.1:${this.port}/rpa-video`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ data, config }),
    })

    return response.json()
  }

  async startImageRpa(config: ImageTask) {
    if (!(await this.checkConnection())) {
      throw new Error('无法找到可用客户端，请确保客户端已启动')
    }

    const response = await fetch(`http://127.0.0.1:${this.port}/rpa-image`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ config }),
    })

    return response.json()
  }

  async uploadFile(file: File): Promise<{ filepath: string; fileId: string }> {
    if (!(await this.checkConnection())) {
      throw new Error('无法找到可用端口，请确保服务已启动')
    }

    // 生成文件唯一标识
    const fileId = `${file.size}_${file.lastModified}_${file.name}`
    const url = `http://127.0.0.1:${this.port}/upload?filename=${encodeURIComponent(fileId)}`

    const response = await fetch(url, {
      method: 'POST',
      body: file,
    })

    if (!response.ok) {
      throw new Error('文件上传失败')
    }

    const result = await response.json()
    return { ...result, fileId }
  }

  async getUint8Array(filePath: string): Promise<Uint8Array> {
    if (!(await this.checkConnection())) {
      throw new Error('无法找到可用端口，请确保服务已启动')
    }

    const response = await fetch(await this.getFileUrl(filePath), {
      method: 'GET',
    })

    if (!response.ok) {
      throw new Error('文件读取失败')
    }

    return new Uint8Array(await response.arrayBuffer())
  }

  async getFileUrl(filePath: string): Promise<string> {
    if (!(await this.checkConnection())) {
      throw new Error('无法找到可用端口，请确保服务已启动')
    }

    return `http://127.0.0.1:${this.port}/file?path=${encodeURIComponent(filePath)}`
  }

  async getPlatformUrl(platform: string): Promise<string | null> {
    if (!(await this.checkConnection())) {
      return null
    }

    try {
      const response = await fetch(
        `http://127.0.0.1:${this.port}/platform-url?platform=${platform}`,
        {
          method: 'GET',
        },
      )

      const res = await response.json()
      return res.platformEntryUrl
    } catch (error) {
      console.error('获取平台URL失败:', error)
      return null
    }
  }

  // WebSocket 方法
  async createAuth(
    platformName: PlatformName | '未知',
    data: { contextId: string; accountSession: AccountSession },
  ): Promise<boolean | null> {
    if (!(await this.checkConnection())) {
      return false
    }

    if (!this.socketLocal || !this.socketLocal.connected) {
      return null
    }

    return new Promise((resolve) => {
      if (!this.socketLocal) {
        return resolve(false)
      }
      this.socketLocal.send(
        {
          type: 'create-auth-view',
          data: {
            platformName,
            accountId: data.contextId,
            accountSession: {
              cookies: data.accountSession ? JSON.stringify(data.accountSession.cookies) : null,
              localStorage: data.accountSession
                ? JSON.stringify(data.accountSession.localStorage)
                : null,
            },
          },
        },
        () => {
          resolve(true)
        },
      )
    })
  }

  async createContextAuth(
    url: string,
    {
      accountId,
      color,
      spaceName,
      unsaved,
    }: {
      accountId: string
      color: string
      spaceName: string
      unsaved: boolean
    },
  ): Promise<boolean | null> {
    if (!(await this.checkConnection())) {
      return false
    }

    if (!this.socketLocal || !this.socketLocal.connected) {
      return null
    }

    return new Promise((resolve) => {
      if (!this.socketLocal) {
        return resolve(false)
      }
      this.socketLocal.send(
        { type: 'create-auth-context-view', data: { url, accountId, color, spaceName, unsaved } },
        () => {
          resolve(true)
        },
      )
    })
  }

  async openView({
    url,
    color,
    accountId,
    spaceName,
  }: {
    url: string
    color: string
    accountId: string
    spaceName: string
  }): Promise<boolean> {
    if (!(await this.checkConnection())) {
      return false
    }

    if (!this.socketLocal || !this.socketLocal.connected) {
      return false
    }

    return new Promise((resolve) => {
      if (!this.socketLocal) {
        return resolve(false)
      }
      this.socketLocal.send(
        {
          type: 'open-view',
          data: {
            url,
            color,
            accountId,
            spaceName,
          },
        },
        () => {
          resolve(true)
        },
      )
    })
  }

  async openAuthViewReset({
    url,
    accountId,
    cookies,
    localStorage,
    title,
    color,
    collects,
  }: {
    url: string
    accountId: string
    cookies: string
    localStorage: string
    title?: string
    color?: string
    collects?: Collect[]
  }): Promise<boolean> {
    if (!(await this.checkConnection())) {
      return false
    }

    if (!this.socketLocal || !this.socketLocal.connected) {
      return false
    }

    return new Promise((resolve) => {
      if (!this.socketLocal) {
        return resolve(false)
      }
      this.socketLocal.send(
        {
          type: 'open-auth-view',
          data: {
            url,
            accountId,
            cookies,
            localStorage,
            title,
            color,
            collects,
          },
        },
        () => {
          resolve(true)
        },
      )
    })
  }

  async openAuthView(
    {
      platform,
      accountId,
      cookies,
      localStorage,
      isUpdateAuth,
      collects,
      title,
      color,
    }: {
      platform: PlatformName | '未知'
      accountId: string
      cookies: string
      localStorage: string
      isUpdateAuth?: boolean
      collects?: Collect[]
      title?: string
      color?: string
    },
    url?: string,
  ): Promise<boolean> {
    if (!(await this.checkConnection())) {
      return false
    }

    if (!this.socketLocal || !this.socketLocal.connected) {
      return false
    }

    let _url = url
    if (!url) {
      _url = (await this.getPlatformUrl(platform as string)) || undefined
    }

    return new Promise((resolve) => {
      if (!this.socketLocal) {
        return resolve(false)
      }
      this.socketLocal.send(
        {
          type: 'open-auth-view',
          data: {
            url: _url,
            platformName: platform,
            accountId,
            cookies,
            localStorage,
            title,
            color,
            isUpdateAuth,
            collects,
          },
        },
        () => {
          resolve(true)
        },
      )
    })
  }

  // 连接管理
  async connect(openId: string, justTry: boolean = false): Promise<boolean> {
    console.debug('尝试连接本地Socket服务', openId)

    // 如果已经连接，直接返回成功
    if (this.isConnected && this.cachedClientId) {
      return true
    }

    // 查找可用端口
    const port = await this.findValidPort(justTry ? 2000 : 200)
    if (!port) {
      throw new Error('未找到可用的本地服务端口 (30020-30030)，请确保本地客户端已启动')
    }

    this.port = port

    try {
      // 先获取并缓存 clientId
      await this.fetchAndCacheClientId()

      // 然后建立 WebSocket 连接（连接成功后会在事件中上报状态）
      await this.connectSocket(openId)

      console.debug('本地Socket服务连接成功，ClientId已缓存', openId, this.cachedClientId)
      return true
    } catch (error) {
      // 连接失败时清理状态
      this.disconnect()
      const errorMessage = error instanceof Error ? error.message : String(error)
      throw new Error(`连接失败: ${errorMessage}`)
    }
  }

  disconnect(): void {
    if (this.socketLocal) {
      console.debug('断开本地Socket服务连接', this.socketLocal.id)
      this.socketLocal.disconnect()
      this.socketLocal = null
      this.port = null
      this.currentOpenId = null
    }
  }

  // 私有方法
  private async fetchAndCacheClientId(): Promise<void> {
    try {
      const response = await fetch(`http://127.0.0.1:${this.port}/client-id`)
      const result = await response.json()
      if (result?.clientId) {
        this.cachedClientId = result.clientId
        console.debug('ClientId 已缓存:', result.clientId)
      } else {
        throw new Error('服务端返回的 ClientId 为空')
      }
    } catch (error) {
      console.error('获取 ClientId 失败:', error)
      throw new Error('无法获取 ClientId，连接失败')
    }
  }

  private async attemptConnection(): Promise<void> {
    const token = useInnerContextStore.getState().token
    if (!token) {
      throw new Error('未找到用户认证信息，无法建立连接')
    }

    try {
      // 显示连接提示对话框
      useLocalStore.getState().setOpenCheck(true)
      useLocalStore.getState().setConnecting(true)

      await this.connect(token, false)
    } finally {
      useLocalStore.getState().setConnecting(false)
      if (this.isConnected) {
        useLocalStore.getState().setOpenCheck(false)
      }
    }
  }

  private async findValidPort(timeout: number): Promise<number | null> {
    const startPort = 30020
    const endPort = 30030

    for (let port = startPort; port <= endPort; port++) {
      try {
        const response = await fetch(`http://127.0.0.1:${port}/health`, {
          signal: AbortSignal.timeout(timeout),
        })

        if (response.ok) {
          return port
        }
      } catch (error) {
        console.log(`Port ${port} not available`, error)
      }
    }

    return null
  }

  private async connectSocket(openId: string): Promise<void> {
    // 存储当前的 openId 用于后续的状态上报
    this.currentOpenId = openId

    return new Promise((resolve, reject) => {
      this.socketLocal = io(`http://127.0.0.1:${this.port}`, {
        transports: ['websocket'],
        auth: {
          openId,
        },
        query: { deviceId, version },
      })

      this.socketLocal.on('connect', () => {
        console.debug('本地Socket服务连接成功', this.socketLocal?.id)

        this.reportConnectionStatus('connected', openId).catch((error) => {
          console.error('上报连接状态失败:', error)
        })

        // 每次连接成功都重新获取 clientId（处理重连情况）
        this.fetchAndCacheClientId().catch((error) => {
          console.error('重连后获取 ClientId 失败:', error)
        })

        resolve()
      })

      this.socketLocal.on('disconnect', () => {
        console.debug('本地Socket服务连接断开')
        // 连接断开后上报状态
        if (this.currentOpenId) {
          this.reportConnectionStatus('disconnected', this.currentOpenId).catch((error) => {
            console.error('上报断开状态失败:', error)
          })
        }

        this.cachedClientId = null
      })

      this.socketLocal.on('connect_error', (error) => {
        console.error('本地Socket服务连接失败:', error)
        reject(error)
      })

      // 添加消息处理逻辑
      this.socketLocal.on('message', (message, ack) => {
        try {
          switch (message.type) {
            case 'open-auth-view':
            case 'create-auth-view':
              eventBus.emit(authorizeEvents.accountAuthorizeSuccessV2, JSON.parse(message.data))
              break
            case 'create-auth-context-view':
              eventBus.emit(authorizeEvents.webSpaceAuthorizeSuccess, JSON.parse(message.data))
              break
            case 'cancel-collect':
              eventBus.emit(authorizeEvents.cancelCollect, message.data)
              break
            case 'add-collect':
              eventBus.emit(authorizeEvents.addCollect, message.data)
              break
          }
        } catch (error) {
          console.error('处理消息失败:', error)
        }

        ack()
      })
    })
  }

  private async reportConnectionStatus(status: 'connected' | 'disconnected', openId: string) {
    try {
      // 向服务端上报连接状态
      await fetch(import.meta.env.VITE_API_URL + '/users/devices/web', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          authorization: openId,
        },
        body: JSON.stringify({
          deviceId: this.cachedClientId,
          isActive: status === 'connected',
        }),
      })
    } catch (error) {
      console.error('上报连接状态失败:', error)
    }
  }
}

// 导出单例
export const localClient = new LocalClient()

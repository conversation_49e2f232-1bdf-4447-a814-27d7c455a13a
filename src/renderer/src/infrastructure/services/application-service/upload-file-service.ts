import type { AxiosResponse, AxiosResponseHeaders } from 'axios'
import axios from 'axios'
import { useUserApiService } from '@renderer/infrastructure/services/network-service/user-api-service'
import { electronService } from '@renderer/infrastructure/services'
import { useCallback, useMemo } from 'react'

type Bucket =
  | 'assets'
  | 'attachments'
  | 'site-spaces' // 站点空间存储桶
  | 'material-library' // 资源库存储桶
  | 'wechat-pubish' // 微信发布存储桶
  | 'cloud-publish' // 云发布存储桶
  | 'logs' // 日志存储桶

export function useUploadFileService(channel: 'aliyun' | 'tianyiyun' = 'aliyun') {
  const userApiService = useUserApiService()

  // 创建axios实例（原来在构造函数中的逻辑）
  const axiosInstance = useMemo(() => {
    const instance = axios.create({
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    })

    instance.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response) {
          const status = error.response.status
          if (status === 404) {
            return Promise.reject(new Error('文件未找到'))
          }
        }
        return Promise.reject(error)
      },
    )

    return instance
  }, [])

  /**
   * 获取资源直传地址
   * @param bucket 团队资源存储桶名(枚举值: assets, attachments)
   * @param fileKey
   * @param checksum
   */
  const getUploadUrl = useCallback(
    (bucket: Bucket, fileKey?: string, checksum?: string) => {
      return userApiService.get<{
        serviceUrl: string
        key: string
      }>(`/storages/${bucket}/upload-url`, {
        fileKey: fileKey,
        checksum: checksum,
        channel: channel,
      })
    },
    [channel, userApiService],
  )

  const getAccessUrl = useCallback(
    async (bucket: Bucket, fileKey?: string) => {
      return userApiService.get<string>(`/storages/${bucket}/access-url`, {
        fileKey: fileKey,
        channel: channel,
      })
    },
    [channel, userApiService],
  )

  const getHeadUrl = useCallback(
    async (bucket: Bucket, fileKey?: string) => {
      return userApiService.get<string>(`/storages/${bucket}/head-url`, {
        fileKey: fileKey,
        channel: channel,
      })
    },
    [channel, userApiService],
  )

  /**
   * 上传文件
   * @param url
   * @param filePath 文件路径
   * @param options 选项
   * @returns
   */
  const putFile = useCallback(
    async (
      url: string,
      filePath: string,
      options: { headers?: Record<string, string> } = {},
    ): Promise<AxiosResponse> => {
      const data = await electronService.readFileBuffer(filePath)
      return axiosInstance.put(url, data, { headers: options.headers })
    },
    [axiosInstance],
  )

  const putUint8Array = useCallback(
    async (url: string, content: Uint8Array | File, checksum?: string): Promise<AxiosResponse> => {
      return axiosInstance.put(url, content, {
        headers: { 'x-tos-meta-checksum': checksum },
      })
    },
    [axiosInstance],
  )

  const getUint8Array = useCallback(
    async (url: string) => {
      const response = await axiosInstance.get<Uint8Array>(url, {
        responseType: 'arraybuffer',
      })
      return {
        data: response.data as Uint8Array,
        headers: response.headers as AxiosResponseHeaders,
      }
    },
    [axiosInstance],
  )

  const header = useCallback(
    async (headerUrl: string): Promise<AxiosResponseHeaders> => {
      const response = await axiosInstance.head(headerUrl)
      return response.headers as AxiosResponseHeaders
    },
    [axiosInstance],
  )

  // 返回所有方法的对象
  return useMemo(
    () => ({
      getUploadUrl,
      getAccessUrl,
      getHeadUrl,
      putFile,
      putUint8Array,
      getUint8Array,
      header,
    }),
    [getUploadUrl, getAccessUrl, getHeadUrl, putFile, putUint8Array, getUint8Array, header],
  )
}

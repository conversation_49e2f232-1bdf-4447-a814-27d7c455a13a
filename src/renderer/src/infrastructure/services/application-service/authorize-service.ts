import type { Account, AccountState } from '@renderer/infrastructure/model/account'
import { WechatShiPinHao3rdPartySubAccount } from '@renderer/infrastructure/model/account'
import { SpiderAccount, Wechat3rdPartyAccount } from '@renderer/infrastructure/model/account'
import type { AuthorizingAccountInfoStructure } from '@common/model/authorizing-account-info'
import type { Platform } from '../../model/platforms'
import { getPlatformByName } from '../../model/platforms'
import type { SpaceFavorite } from '@renderer/infrastructure/model'
import { AuthorizingAccount, spaceColors } from '@renderer/infrastructure/model'
import type { Group } from '@renderer/infrastructure/model/group/group'
import type { BrowserContextIdentifier } from '@common/structure/browser/contextIdentifier'
import { useMemo } from 'react'
import type { BrowserService } from '@renderer/infrastructure/services/browser-service/browser'
import { useBrowserService } from '@renderer/infrastructure/services/browser-service/browser'
import {
  identifierService,
  platformService,
  type RandomService,
  usePersistentLogService,
  useRandomService,
} from '@renderer/infrastructure/services'

import type { <PERSON>ie } from 'electron'
import type { SpaceDumpService } from '@renderer/infrastructure/services/application-service/space/space-dump-service'
import { useSpaceDumpService } from '@renderer/infrastructure/services/application-service/space/space-dump-service'
import { useAccountApi } from '@renderer/infrastructure/services/entity-service/cloud/account-api'
import type { AccountSession, SessionState, AccountSessionWithRelease } from '@common/structure'
import type { CompressService } from '@renderer/infrastructure/services/application-service/infrastructure-service/compress-service'
import { useCompressService } from '@renderer/infrastructure/services/application-service/infrastructure-service/compress-service'
import type { SpaceService } from '@renderer/infrastructure/services/application-service/space/space-service'
import { useSpaceService } from '@renderer/infrastructure/services/application-service/space/space-service'
import { eventBus } from '@renderer/infrastructure/event-bus/buses'
import { authorizeEvents } from '@renderer/infrastructure/event-bus/business-events'
import type { InfiniteQueryResponse } from '@renderer/infrastructure/types'
import { useWechat3rdPartyService } from '@renderer/infrastructure/services/application-service/account/wechat-3rd-party-service'
import { useSpaceApi } from '../entity-service/cloud/space-api'
import type { MergedAccount } from '@renderer/infrastructure/model/merged-account'

export function useAuthorizeService() {
  const browserService = useBrowserService()
  const randomService = useRandomService()
  const spaceDumpService = useSpaceDumpService()
  const accountApi = useAccountApi()
  const spaceService = useSpaceService()
  const compressService = useCompressService()
  const persistentLogService = usePersistentLogService()
  const wechat3rdPartyService = useWechat3rdPartyService()
  const spaceApi = useSpaceApi()

  return useMemo(() => {
    return new AuthorizeService(
      browserService,
      randomService,
      spaceDumpService,
      accountApi,
      spaceService,
      compressService,
      persistentLogService,
      wechat3rdPartyService,
      spaceApi,
    )
  }, [
    browserService,
    randomService,
    spaceDumpService,
    accountApi,
    spaceService,
    compressService,
    persistentLogService,
    wechat3rdPartyService,
    spaceApi,
  ])
}

export class AuthorizeService {
  constructor(
    private browserService: BrowserService,
    private randomService: RandomService,
    /**
     * @deprecated 仅作为历史数据兼容，过一段时间之后可以清除 2025年6月3日
     */
    private spaceDumpService: SpaceDumpService,
    private accountApi: ReturnType<typeof useAccountApi>,
    private spaceService: SpaceService,
    private compressService: CompressService,
    private persistentLogService: ReturnType<typeof usePersistentLogService>,
    private wechat3rdPartyService: ReturnType<typeof useWechat3rdPartyService>,
    private spaceApi: ReturnType<typeof useSpaceApi>,
  ) {}

  async checkAccount(platformAccountId: string) {
    return this.accountApi.checkAccount(platformAccountId)
  }

  async addAccount(
    accountInfo: AuthorizingAccountInfoStructure,
    cookies: Record<string, string>[],
    localStorage: Record<string, string>,
    spaceColor: string,
  ) {
    const authorizingAccount = new AuthorizingAccount(
      getPlatformByName(accountInfo.platformName),
      accountInfo.accountId,
      accountInfo.nickName,
      accountInfo.avatar,
      accountInfo.identityVerified,
    )
    const account = await this.accountApi.putSpiderAccount(authorizingAccount, spaceColor)

    const accountSession = {
      cookies: cookies,
      localStorage: localStorage,
    } satisfies AccountSession as AccountSession

    this.persistentLogService.logAuthorizeSuccess(account, accountSession)

    await this.spaceApi.updateWebSession(account.accountId, accountSession)

    const updatedAccount = await this.accountApi.patchAccountState(
      account.accountId,
      '正常',
      'fixedChecksum',
    )

    eventBus.emit(authorizeEvents.accountAuthorizeSuccess, updatedAccount)
    return updatedAccount
  }

  async removeAccount(account: MergedAccount) {
    await this.accountApi.deleteAccount(account)
    if (account instanceof SpiderAccount) await this.spaceService.disposeContext(account.accountId)
  }

  async sessionDetectFinished(
    accountId: string,
    state: SessionState,
    detail: string | unknown,
    session: AccountSession,
    checksum: string,
  ) {
    const account = await this.getSpiderAccount(accountId)

    if (state !== '正常') {
      this.persistentLogService.logAuthorizeFail(account, detail, session)
    } else {
      this.persistentLogService.logAuthorizeSuccess(account, session)
    }

    const updatedAccount = await this.accountApi.patchAccountState(accountId, state, checksum)

    return updatedAccount
  }

  async setAccountRemark(accountId: string, remark: string) {
    void this.accountApi.patchAccountRemark(accountId, remark)
  }

  async unFreezeAccount(account: Account) {
    return this.accountApi.unFreezeAccount(account)
  }

  async unFreezeSpaceAccount(id: string) {
    return this.accountApi.unFreezeSpaceAccount(id)
  }

  async hasAnyAccounts() {
    return await this.accountApi.hasAnyAccounts()
  }

  async getAccounts(
    operableOnly: boolean,
    cursor: Date | null = null,
    platform: Platform | null = null,
    group: Group | null = null,
    name: string | null = null,
    accountState: AccountState | null = null,
    platforms: Platform[] | null = null,
  ): Promise<InfiniteQueryResponse<MergedAccount, Date | null>> {
    const response = await this.accountApi.getAccountsCursor(
      cursor,
      {
        platform: platform,
        group: group,
        name: name,
        platforms: platforms,
        accountState: accountState,
      },
      operableOnly,
    )
    return {
      data: response.data,
      cursor: response.cursor,
      end: response.end,
    }
  }

  setAccountGroups(accountId: string, groups: string[]) {
    return this.accountApi.patchAccountGroups(accountId, groups)
  }

  getAccount(accountId: string) {
    return this.accountApi.getAccount(accountId)
  }

  async getSpiderAccount(accountId: string) {
    const account = await this.getAccount(accountId)
    if (!(account instanceof SpiderAccount)) throw new Error('账号不是爬虫账号！')
    return account
  }

  async getWechat3rdPartyAccount(accountId: string) {
    const account = await this.getAccount(accountId)
    if (!(account instanceof Wechat3rdPartyAccount))
      throw new Error(`账号不是微信账号！${accountId}`)
    return account
  }

  async openNewSpace(platform: Platform) {
    const color = spaceColors[this.randomService.randomInt(0, spaceColors.length - 1)]
    const contextId = identifierService.generateUUID()
    await this.browserService.createAccountContext(contextId, color, platform)
    return this.browserService.openAccountTab(contextId, platform)
  }

  async openAccountSpace(account: SpiderAccount) {
    await this.tryRestoreContext(account)
    await this.browserService.openAccountTab(account.accountId, account.platform, account.accountId)
  }

  async tryRestoreContext(account: SpiderAccount) {
    const { session: accountSession, release } = await this.getAccountSession(account)
    try {
      if (accountSession) {
        await this.browserService.createAccountContext(
          account.accountId,
          account.color,
          account.platform,
          account,
          accountSession,
        )
      }
    } finally {
      if (account instanceof WechatShiPinHao3rdPartySubAccount) {
        void release?.()
      }
    }
  }

  async tryRestoreContextWithScenarioContext(account: SpiderAccount) {
    const { session: accountSession, release } =
      await this.getAccountSessionWithScenarioContext(account)
    try {
      if (accountSession) {
        await this.browserService.createAccountContext(
          account.accountId,
          account.color,
          account.platform,
          account,
          accountSession,
        )
      }
    } finally {
      if (account instanceof WechatShiPinHao3rdPartySubAccount) {
        void release?.()
      }
    }
  }

  async openAccountSpaceInNewWindow(windowIdentifier: string, account: SpiderAccount) {
    await this.tryRestoreContext(account)
    await this.browserService.openAccountTabInNewWindow(
      windowIdentifier,
      account.accountId,
      account.platform,
      account.accountId,
    )
  }

  async sessionDetect(account: SpiderAccount) {
    if (account instanceof WechatShiPinHao3rdPartySubAccount) {
      return true // 微信子账号不需要常规检测
    }
    const { session, release } = await this.getAccountSession(account)
    try {
      const sessionResult = await platformService.sessionDetect(account, session, 'fixedChecksum')
      return sessionResult.state
    } finally {
      void release?.()
    }
  }

  /**
   *
   * @param account
   * @param waitForAccountFree 是否等待账号释放（目前只有微信子账号会生效），需要及时响应的任务请设置为false
   */
  async getAccountSession(account: SpiderAccount): Promise<AccountSessionWithRelease> {
    if (account instanceof WechatShiPinHao3rdPartySubAccount) {
      const token = (await this.wechat3rdPartyService.lockParentAccount([account]))[0]
      try {
        const accountSession = await this.wechat3rdPartyService.getAccountSessionWithToken(
          account,
          token.wxkey,
        )
        return {
          session: accountSession,
          release: async () => {
            await this.wechat3rdPartyService.unlockWechatAccount(
              token.platformAccountId,
              token.wxkey,
            )
          },
        }
      } catch (e) {
        await this.wechat3rdPartyService.unlockWechatAccount(token.platformAccountId, token.wxkey)
        throw e
      }
    }

    try {
      const session = await this.spaceApi.getWebSession(account.accountId)
      return { session }
    } catch (e) {
      const dump = await this.spaceDumpService.getAccountSessionDump(account.spaceId)
      if (!dump) {
        throw e
      }
      const session = this.compressService.inflateObject<AccountSession>(dump)
      return { session }
    }
  }

  async getAccountSessionWithScenarioContext(
    account: SpiderAccount,
    waitForAccountFree: boolean = true,
  ): Promise<AccountSessionWithRelease> {
    if (account instanceof WechatShiPinHao3rdPartySubAccount) {
      const session = await this.wechat3rdPartyService.getAccountSessionWithScenarioContext(
        account,
        waitForAccountFree,
      ) // 微信子账号从api获取
      return { session }
    }
    try {
      const session = await this.spaceApi.getWebSession(account.accountId)
      return { session }
    } catch (e) {
      const dump = await this.spaceDumpService.getAccountSessionDump(account.spaceId)
      if (!dump) {
        throw e
      }
      const session = this.compressService.inflateObject<AccountSession>(dump)
      return { session }
    }
  }

  async closeTabs(contextIdentifier: BrowserContextIdentifier) {
    await this.browserService.closeAllTab(contextIdentifier.contextId)
  }

  isSpaceOpened(account: SpiderAccount): Promise<boolean> {
    return this.browserService.anyAccountSpaceTabOpened(account.accountId)
  }

  async pullSpace(account: SpiderAccount) {
    const accountId = account.accountId
    const openedUrls = await this.browserService.getOpenedUrls(accountId)
    await this.browserService.closeAllTab(accountId)
    await this.tryRestoreContext(account)
    await this.browserService.openAccountTab(
      accountId,
      account.platform,
      account.accountId,
      openedUrls,
    )
  }

  async openFavorite(account: SpiderAccount, favorite: SpaceFavorite) {
    await this.tryRestoreContext(account)
    await this.browserService.openAccountTab(
      account.accountId,
      account.platform,
      account.accountId,
      [favorite.url],
    )
  }

  async removeFavorite(account: SpiderAccount, url: string) {
    await this.spaceService.removeFavorite(account.accountId, url)
  }

  resetSpaceContext(accountId: string) {
    return this.spaceService.disposeContext(accountId)
  }

  getAccountFilters(platformName?: string) {
    return this.accountApi.getAccountFilters(platformName)
  }

  async getLocalContextAccountSession(account: SpiderAccount) {
    return this.browserService.getAccountSession(account.accountId, account.platform)
  }

  async hasContext(account: SpiderAccount) {
    return await this.browserService.isContextCreated(account.accountId)
  }

  async updateSpace(account: SpiderAccount) {
    await this.tryRestoreContext(account)

    const accountSession = await this.browserService.getAccountSession(
      account.accountId,
      account.platform,
    )

    this.persistentLogService.logAuthorizeUpdate(account, accountSession)

    await this.spaceApi.updateWebSession(account.accountId, accountSession)
  }

  async getAllSpiderAccounts(sessionState: SessionState) {
    return this.accountApi.getSpiderAccountAll(sessionState)
  }

  async setIdentityVerified(accountId: string, identityVerified: boolean) {
    return this.accountApi.setIdentityVerified(accountId, identityVerified)
  }

  setProxy(account: Account, areaCode: string | null) {
    return this.accountApi.setProxy(account, areaCode)
  }
}

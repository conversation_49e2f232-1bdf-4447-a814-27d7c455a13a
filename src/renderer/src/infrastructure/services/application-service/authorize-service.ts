import type { Account, AccountState } from '@renderer/infrastructure/model/account'
import { WechatShiPinHao3rdPartySubAccount } from '@renderer/infrastructure/model/account'
import { SpiderAccount, Wechat3rdPartyAccount } from '@renderer/infrastructure/model/account'
import type { AuthorizingAccountInfoStructure } from '@common/model/authorizing-account-info'
import type { Platform } from '../../model/platforms'
import { getPlatformByName } from '../../model/platforms'
import type { Collect, SpaceFavorite } from '@renderer/infrastructure/model'
import { AuthorizingAccount } from '@renderer/infrastructure/model'
import type { Group } from '@renderer/infrastructure/model/group/group'
import type { BrowserContextIdentifier } from '@common/structure/browser/contextIdentifier'
import { useMemo } from 'react'
import type { BrowserService } from '@renderer/infrastructure/services/browser-service/browser'
import { useBrowserService } from '@renderer/infrastructure/services/browser-service/browser'
import { usePersistentLogService } from '@renderer/infrastructure/services'
import { useAccountApi } from '@renderer/infrastructure/services/entity-service/cloud/account-api'
import type { AccountSession, SessionState } from '@common/structure'
import type { SpaceService } from '@renderer/infrastructure/services/application-service/space/space-service'
import { useSpaceService } from '@renderer/infrastructure/services/application-service/space/space-service'
import { eventBus } from '@renderer/infrastructure/event-bus/buses'
import { authorizeEvents } from '@renderer/infrastructure/event-bus/business-events'
import type { InfiniteQueryResponse } from '@renderer/infrastructure/types'
import { useSpaceApi } from '../entity-service/cloud/space-api'
import type { MergedAccount } from '@renderer/infrastructure/model/merged-account'
import { useWechat3rdPartyApi } from '../entity-service'

export function useAuthorizeService() {
  const browserService = useBrowserService()
  const accountApi = useAccountApi()
  const spaceService = useSpaceService()
  const persistentLogService = usePersistentLogService()
  const spaceApi = useSpaceApi()
  const wechat3rdPartyApi = useWechat3rdPartyApi()

  return useMemo(() => {
    return new AuthorizeService(
      browserService,
      accountApi,
      spaceService,
      persistentLogService,
      spaceApi,
      wechat3rdPartyApi,
    )
  }, [browserService, accountApi, spaceService, persistentLogService, spaceApi, wechat3rdPartyApi])
}

export class AuthorizeService {
  constructor(
    private browserService: BrowserService,
    private accountApi: ReturnType<typeof useAccountApi>,
    private spaceService: SpaceService,
    private persistentLogService: ReturnType<typeof usePersistentLogService>,
    private spaceApi: ReturnType<typeof useSpaceApi>,
    private wechat3rdPartyApi: ReturnType<typeof useWechat3rdPartyApi>,
  ) {}

  async addAccount(
    accountInfo: AuthorizingAccountInfoStructure,
    cookies: Record<string, string>[],
    localStorage: Record<string, string>,
    spaceColor: string,
  ) {
    const authorizingAccount = new AuthorizingAccount(
      getPlatformByName(accountInfo.platformName),
      accountInfo.accountId,
      accountInfo.nickName,
      accountInfo.avatar,
      accountInfo.identityVerified,
    )
    const accountSession = {
      cookies: cookies,
      localStorage: localStorage,
    } satisfies AccountSession as AccountSession

    const account = await this.accountApi.putSpiderAccount(
      authorizingAccount,
      spaceColor,
      accountSession,
    )

    this.persistentLogService.logAuthorizeSuccess(account, accountSession)

    await this.spaceApi.updateWebSession(account.accountId, accountSession)

    const updatedAccount = await this.accountApi.patchAccountState(
      account.accountId,
      '正常',
      'fixedChecksum',
    )

    eventBus.emit(authorizeEvents.accountAuthorizeSuccess, updatedAccount)
    return updatedAccount
  }

  async removeAccount(account: MergedAccount) {
    await this.accountApi.deleteAccount(account)
    if (account instanceof SpiderAccount) await this.spaceService.disposeContext(account.accountId)
  }

  async sessionDetectFinished(
    accountId: string,
    state: SessionState,
    detail: string | unknown,
    session: AccountSession,
    checksum: string,
  ) {
    const account = await this.getSpiderAccount(accountId)

    if (state !== '正常') {
      this.persistentLogService.logAuthorizeFail(account, detail, session)
    } else {
      this.persistentLogService.logAuthorizeSuccess(account, session)
    }

    const updatedAccount = await this.accountApi.patchAccountState(accountId, state, checksum)

    return updatedAccount
  }

  async setAccountRemark(accountId: string, remark: string) {
    void this.accountApi.patchAccountRemark(accountId, remark)
  }

  async unFreezeAccount(account: Account) {
    return this.accountApi.unFreezeAccount(account)
  }

  async unFreezeSpaceAccount(id: string) {
    return this.accountApi.unFreezeSpaceAccount(id)
  }

  async hasAnyAccounts() {
    return await this.accountApi.hasAnyAccounts()
  }

  async getAccounts(
    operableOnly: boolean,
    cursor: Date | null = null,
    platform: Platform | null = null,
    group: Group | null = null,
    name: string | null = null,
    accountState: AccountState | null = null,
    platforms: Platform[] | null = null,
  ): Promise<InfiniteQueryResponse<MergedAccount, Date | null>> {
    const response = await this.accountApi.getAccountsCursor(
      cursor,
      {
        platform: platform,
        group: group,
        name: name,
        platforms: platforms,
        accountState: accountState,
      },
      operableOnly,
    )
    return {
      data: response.data,
      cursor: response.cursor,
      end: response.end,
    }
  }

  setAccountGroups(accountId: string, groups: string[]) {
    return this.accountApi.patchAccountGroups(accountId, groups)
  }

  getAccount(accountId: string) {
    return this.accountApi.getAccount(accountId)
  }

  async getSpiderAccount(accountId: string) {
    const account = await this.getAccount(accountId)
    if (!(account instanceof SpiderAccount)) throw new Error('账号不是爬虫账号！')
    return account
  }

  async getWechat3rdPartyAccount(accountId: string) {
    const account = await this.getAccount(accountId)
    if (!(account instanceof Wechat3rdPartyAccount))
      throw new Error(`账号不是微信账号！${accountId}`)
    return account
  }

  async openNewSpace(platform: Platform, accountId?: string) {
    // const color = spaceColors[this.randomService.randomInt(0, spaceColors.length - 1)]
    // const contextId = accountId ?? identifierService.generateUUID()
    // await this.browserService.createAccountContext(contextId, color, platform)
    if (accountId) {
      const account = await this.getSpiderAccount(accountId)
      const accountSession = await this.getAccountSession(account)
      return this.browserService.createAuthTab(platform, { contextId: accountId, accountSession })
    }
    return this.browserService.createAuthTab(platform, { contextId: accountId })
  }

  async openAccountSpace(account: SpiderAccount, urls?: Collect[]) {
    const accountSession = await this.getAccountSession(account)
    await this.browserService.openAccountTab(
      account.accountId,
      account.platform,
      account.displayName,
      { ...accountSession, isUpdateAuth: !(account instanceof WechatShiPinHao3rdPartySubAccount) },
      urls,
    )
  }

  async tryRestoreContext(account: SpiderAccount) {
    const accountSession = await this.getAccountSession(account)

    if (accountSession) {
      await this.browserService.createAccountContext(
        account.accountId,
        account.color,
        account.platform,
        account,
        accountSession,
      )
    }
  }

  async openAccountSpaceInNewWindow(windowIdentifier: string, account: SpiderAccount) {
    await this.tryRestoreContext(account)
    await this.browserService.openAccountTabInNewWindow(
      windowIdentifier,
      account.accountId,
      account.platform,
      account.accountId,
    )
  }

  async sessionDetect(account: SpiderAccount) {
    if (account instanceof WechatShiPinHao3rdPartySubAccount) {
      void this.wechat3rdPartyApi.getAccountSession(account) //仅为了触发服务端刷新token，不做任何等待和错误处理
      return '正常'
    }

    const state = (await this.accountApi.checkAccount(account.accountId)).state

    this.accountApi.patchAccountState(account.accountId, state, 'fixedChecksum')

    return state
  }

  /**
   *
   * @param account
   * @param waitForAccountFree 是否等待账号释放（目前只有微信子账号会生效），需要及时响应的任务请设置为false
   */
  async getAccountSession(account: SpiderAccount): Promise<AccountSession> {
    if (account instanceof WechatShiPinHao3rdPartySubAccount) {
      return this.wechat3rdPartyApi.getAccountSession(account)
    }

    try {
      return await this.spaceApi.getWebSession(account.accountId)
    } catch (e) {
      await this.accountApi.patchAccountState(account.accountId, '已失效', 'fixedChecksum')
      throw new Error('账号已失效，请重新登录')
    }
  }

  async closeTabs(contextIdentifier: BrowserContextIdentifier) {
    await this.browserService.closeAllTab(contextIdentifier.contextId)
  }

  isSpaceOpened(account: SpiderAccount): Promise<boolean> {
    return this.browserService.anyAccountSpaceTabOpened(account.accountId)
  }

  async pullSpace(_account: SpiderAccount) {
    // const accountId = account.accountId
    // const openedUrls = await this.browserService.getOpenedUrls(accountId)
    // await this.browserService.closeAllTab(accountId)
    // await this.tryRestoreContext(account)
    // await this.browserService.openAccountTab(
    //   accountId,
    //   account.platform,
    //   account.displayName,
    //   null,
    //   openedUrls.map((url) => ({ url, accountId: accountId })),
    // )
  }

  async openFavorite(account: SpiderAccount, favorite: SpaceFavorite) {
    const accountSession = await this.getAccountSession(account)

    await this.browserService.openFavorite(
      favorite.url,
      account.accountId,
      favorite.name,
      accountSession,
      account.favorites.map((f) => ({
        url: f.url,
        accountId: account.accountId,
        id: f.id,
      })),
    )
  }

  async removeFavorite(account: SpiderAccount, id: string) {
    await this.spaceService.removeFavorite(account.accountId, id)
  }

  resetSpaceContext(accountId: string) {
    return this.spaceService.disposeContext(accountId)
  }

  getAccountFilters(platformName?: string) {
    return this.accountApi.getAccountFilters(platformName)
  }

  async getLocalContextAccountSession(account: SpiderAccount) {
    return this.browserService.getAccountSession(account.accountId, account.platform)
  }

  async hasContext(account: SpiderAccount) {
    return await this.browserService.isContextCreated(account.accountId)
  }

  async updateSpace(account: SpiderAccount) {
    await this.tryRestoreContext(account)

    const accountSession = await this.browserService.getAccountSession(
      account.accountId,
      account.platform,
    )

    this.persistentLogService.logAuthorizeUpdate(account, accountSession)

    await this.spaceApi.updateWebSession(account.accountId, accountSession)
  }

  async getAllSpiderAccounts(sessionState: SessionState) {
    return this.accountApi.getSpiderAccountAll(sessionState)
  }

  async setIdentityVerified(accountId: string, identityVerified: boolean) {
    return this.accountApi.setIdentityVerified(accountId, identityVerified)
  }

  setProxy(account: Account, areaCode: string | null) {
    return this.accountApi.setProxy(account, areaCode)
  }

  async setOverseasAuthorized(state: string, selectedAccountIds: string[]) {
    return await this.accountApi.setOverseasAuthorized(state, selectedAccountIds)
  }
}

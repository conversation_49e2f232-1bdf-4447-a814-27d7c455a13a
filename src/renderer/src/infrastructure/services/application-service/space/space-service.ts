import { useMemo } from 'react'
import {
  type BrowserService,
  useBrowserService,
} from '@renderer/infrastructure/services/browser-service/browser'

import type { SpaceApi } from '@renderer/infrastructure/services/entity-service/cloud/space-api'
import { useSpaceApi } from '@renderer/infrastructure/services/entity-service/cloud/space-api'

/**
 * 用于统一网站空间/账号空间的公共逻辑
 */
export function useSpaceService() {
  const browserService = useBrowserService()
  const spaceApi = useSpaceApi()
  return useMemo(() => new SpaceService(browserService, spaceApi), [browserService, spaceApi])
}

/**
 * 用于统一网站空间/账号空间的公共逻辑
 */
export class SpaceService {
  constructor(
    private browserService: BrowserService,
    private spaceApi: SpaceApi,
  ) {}

  async removeFavorite(spaceId: string, id: string) {
    await this.spaceApi.cancelCollect(spaceId, id)
  }

  async saveFavorite(spaceId: string, name: string, url: string) {
    await this.spaceApi.saveFavorite(spaceId, name, url)
  }

  async disposeContext(accountId: string) {
    await this.browserService.closeAllTab(accountId)
  }
}

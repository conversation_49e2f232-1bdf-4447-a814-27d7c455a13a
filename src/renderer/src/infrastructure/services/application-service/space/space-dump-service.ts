import { useMemo } from 'react'
import { useUploadFileService } from '@renderer/infrastructure/services'

/**
 * @deprecated 仅作为历史数据兼容，过一段时间之后可以清除 2025年6月3日
 */
export function useSpaceDumpService() {
  const uploadFileService = useUploadFileService()
  return useMemo(() => new SpaceDumpService(uploadFileService), [uploadFileService])
}

/**
 * @deprecated 仅作为历史数据兼容，过一段时间之后可以清除 2025年6月3日
 */
export class SpaceDumpService {
  constructor(private uploadFileService: ReturnType<typeof useUploadFileService>) {}

  async saveAccountSessionDump(spaceId: string, accountSessionDump: Uint8Array) {
    const { serviceUrl: putUrl } = await this.uploadFileService.getUploadUrl(
      'site-spaces',
      `${spaceId}_account_session`,
    )
    await this.uploadFileService.putUint8Array(putUrl, accountSessionDump)
  }

  async getAccountSessionDump(spaceId: string) {
    if (!spaceId) {
      throw new Error('空间ID不存在')
      return
    }

    const url = await this.uploadFileService.getAccessUrl(
      'site-spaces',
      `${spaceId}_account_session`,
    )
    try {
      const response = await this.uploadFileService.getUint8Array(url)
      return response.data
    } catch (e) {
      console.error(e)
      if (e instanceof Error && e.message === '文件未找到') {
        throw new Error('账号需要更新，请重新登录')
      }
      throw new Error('账号登录信息读取失败')
    }
  }
}

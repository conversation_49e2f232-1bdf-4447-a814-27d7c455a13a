import type { WebSpace } from '@renderer/infrastructure/model/space/web-space'
import { useBrowserService } from '@renderer/infrastructure/services/browser-service/browser'
import type { BrowserContextIdentifier } from '@common/structure/browser/contextIdentifier'
import { useSpace<PERSON>pi } from '@renderer/infrastructure/services/entity-service/cloud/space-api'
import { useSpaceService } from '@renderer/infrastructure/services/application-service/space/space-service'
import type { Collect, SpaceFavorite } from '@renderer/infrastructure/model'
import { useFavoriteCache } from '@renderer/store/space-store'
import { useMemo } from 'react'
import type { AccountSession } from '@common/structure'
import { identifierService } from '../infrastructure-service'

/**
 * 应用服务：Web空间管理服务
 */
export function useWebSpaceManageService() {
  const spaceApi = useSpaceApi()
  const browserService = useBrowserService()
  const spaceService = useSpaceService()
  const favoriteCache = useFavoriteCache()

  return useMemo(
    () => new WebSpaceManageService(spaceApi, browserService, spaceService, favoriteCache),
    [spaceApi, browserService, spaceService, favoriteCache],
  )
}

class WebSpaceManageService {
  constructor(
    private spaceApi: ReturnType<typeof useSpaceApi>,
    private browserService: ReturnType<typeof useBrowserService>,
    private spaceService: ReturnType<typeof useSpaceService>,
    private favoriteCache: ReturnType<typeof useFavoriteCache>,
  ) {}

  createSpace = async (
    url: string,
    spaceName: string,
    color: string,
    accountSession: AccountSession,
  ) => {
    const space = await this.spaceApi.createSpace(url, spaceName, color)

    await this.spaceApi.updateWebSession(space.id, accountSession)

    return space
  }

  updateSpaceSession = async (spaceId: string, accountSession: AccountSession) => {
    await this.spaceApi.updateWebSession(spaceId, accountSession)
  }

  getSpaces = (
    cursor: Date | null,
    options: { operableOnly: boolean; groupId?: string; spaceName?: string },
  ) => {
    return this.spaceApi.getSpaces(cursor, options)
  }

  private async tryRestoreContext(space: WebSpace, urls?: Collect[]): Promise<void> {
    try {
      const session = await this.spaceApi.getWebSession(space.id)
      await this.browserService.restoreContext(
        space.id,
        space.color,
        space.displayName,
        space.url,
        session,
        urls,
      )
    } catch (e) {
      await this.browserService.createNewContext(
        space.id,
        space.color,
        space.displayName,
        space.url,
      )
    }
  }

  openSpace = async (space: WebSpace, urls?: Collect[]) => {
    await this.tryRestoreContext(space, urls)
    await this.browserService.openTab(space.id, [space.url])
  }

  newOpenSpace = async (ident: string, space: WebSpace) => {
    await this.tryRestoreContext(space)
    await this.browserService.newOpenTab(space.id, [space.url], ident)
  }

  openFavorite = async (space: WebSpace, favorite: SpaceFavorite) => {
    await this.tryRestoreContext(space)
    await this.browserService.openTab(space.id, [favorite.url])
  }

  openFavoriteByCollect = async (space: WebSpace, collect: Collect[]) => {
    const session = await this.spaceApi.getWebSession(space.id)
    await this.browserService.openFavorite(space.url, space.accountId, space.name, session, collect)
  }

  uploadSpace = async (space: WebSpace) => {
    if (!(await this.browserService.isContextCreated(space.id)))
      throw new Error('没有找到待上传数据，请先打开空间')

    await this.browserService.restoreContext(
      space.id,
      space.color,
      space.displayName,
      space.url,
      null,
    )

    const accountSession = await this.browserService.getWebSession(space.id, space.url)

    await this.spaceApi.updateWebSession(space.id, accountSession)
  }

  removeSpace = async (spaceId: string) => {
    await this.spaceApi.removeSpace(spaceId)
    await this.spaceService.disposeContext(spaceId)
  }

  getSpace = (spaceId: string) => {
    return this.spaceApi.getSpace(spaceId)
  }

  cleanupSessionDirectory = async () => {
    return this.browserService.cleanupSessionDirectory([])
  }

  tryUpdateSpaceFavicon = async (contextIdentifier: BrowserContextIdentifier, favicon: string) => {
    const spaceId = contextIdentifier.contextId
    if (!(await this.browserService.hasIcon(spaceId))) {
      await this.browserService.updateIcon(spaceId, favicon)
    }
  }

  getSpaceIcon = (id: string): Promise<string | null> => {
    return this.browserService.getIcon(id)
  }

  isSpaceOpened = (spaceId: string): Promise<boolean> => {
    return this.browserService.anyWebSpaceTabOpened(spaceId)
  }

  pullSpace = async (spaceId: string) => {
    const openedUrls = await this.browserService.getOpenedUrls(spaceId)
    await this.browserService.closeAllTab(spaceId)
    const space = await this.spaceApi.getSpace(spaceId)
    await this.tryRestoreContext(space)
    await this.browserService.openTab(spaceId, openedUrls)
  }

  saveFavorite = async (spaceId: string, name: string, url: string) => {
    await this.spaceService.saveFavorite(spaceId, name, url)
  }

  removeFavorite = async (spaceId: string, id: string) => {
    await this.spaceService.removeFavorite(spaceId, id)
  }

  syncFavoritesToBrowser = async () => {
    const contextFavorites: Record<string, SpaceFavorite[]> = {}
    for (const [spaceId, favorites] of this.favoriteCache.spaceFavorites.entries()) {
      contextFavorites[spaceId] = favorites
    }
    await this.browserService.syncFavorites(contextFavorites)
  }

  async updateSpace(spaceId: string, spaceName: string, color: string) {
    return await this.spaceApi.updateSpace(spaceId, {
      spaceName: spaceName,
      color: color,
    })
  }

  async addWebSpace(url: string, spaceName: string, color: string) {
    const spaceId = identifierService.generateUUIDnew<string>({ prefix: 'new-space-' })
    await this.browserService.createAuthContext(spaceId, color, spaceName, url, true)
    await this.browserService.openTab(spaceId, [url])
  }

  async closeTabs(contextId: string) {
    await this.browserService.closeAllTab(contextId)
  }
}

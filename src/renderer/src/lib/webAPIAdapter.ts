import { type PlatformName } from '@common/model/platform-name'
import { localClient } from '../infrastructure/services/application-service/local-client'
import type { AccountSession } from '@common/structure'
import { type Collect } from '@renderer/infrastructure/model'

// 模拟Electron IPC通信的Web适配器
class WebAPIAdapter {
  env = import.meta.env

  // 事件监听器存储
  private listeners: Record<string, Array<(...args: unknown[]) => void>> = {}

  getPreloadPath = (relatedPath: string) => {
    return new URL(relatedPath, import.meta.url).pathname
  }

  createDownloader = () => {
    return null
  }

  // 模拟ipcRenderer.on
  on = (event: string, listener: () => unknown) => {
    if (!this.listeners[event]) {
      this.listeners[event] = []
    }
    this.listeners[event].push(listener)
    return () => this.off(event, listener)
  }

  // 模拟ipcRenderer.off
  off = (event: string, listener: () => unknown) => {
    if (this.listeners[event]) {
      this.listeners[event] = this.listeners[event].filter((l) => l !== listener)
    }
  }

  // 模拟ipcRenderer.offAll
  offAll = (event: string) => {
    this.listeners[event] = []
  }

  // 模拟ipcRenderer.send
  send = (channel: string, ...args: unknown[]) => {
    console.log(`Web环境: 发送消息到 ${channel}`, args)
    // 在Web环境中，可以使用localStorage、IndexedDB或API调用替代
  }

  // 其他需要模拟的Electron API
  invoke = async (channel: string, ...args: unknown[]) => {
    console.log(`Web环境: 调用 ${channel}`, args)

    switch (channel) {
      case 'getAccountUrl':
        {
          const { accountUrls } = await localClient.accountUrls(
            args[0] as { platformName: string; accountId: string }[],
          )
          return accountUrls
        }

        break
      case 'createAuthTab':
        {
          const [data, platform] = args as [
            { contextId: string; accountSession: AccountSession },
            string,
          ]
          await localClient.createAuth(platform as PlatformName | '未知', data)
        }
        break
      case 'createAuthContext':
        {
          const [accountId, color, url, spaceName, unsaved] = args as [
            string,
            string,
            string,
            string,
            boolean,
          ]
          await localClient.createContextAuth(url, { accountId, color, spaceName, unsaved })
        }
        break
      case 'createNewContext':
        {
          const [accountId, color, url, spaceName] = args as [string, string, string, string]

          await localClient.openView({
            url,
            color,
            accountId,
            spaceName,
          })
        }
        break
      case 'restoreContext':
        {
          const [accountId, color, title, url, sessionObject, collects] = args as [
            string,
            string,
            string,
            string,
            AccountSession,
            Collect[],
          ]

          await localClient.openAuthViewReset({
            url,
            accountId: accountId as string,
            cookies: JSON.stringify(sessionObject.cookies),
            localStorage: JSON.stringify(sessionObject.localStorage),
            title,
            color,
            collects,
          })
        }
        break
      case 'openFavorite':
        {
          const [url, accountId, title, sessionObject, collects] = args as [
            string,
            string,
            string,
            AccountSession,
            Collect[],
          ]

          await localClient.openAuthViewReset({
            url,
            title,
            accountId: accountId as string,
            cookies: JSON.stringify(sessionObject.cookies),
            localStorage: JSON.stringify(sessionObject.localStorage),
            collects,
          })
        }
        break
      case 'openAccountTab':
        {
          const [accountId, platform, name, sessionObject, collects] = args as [
            string,
            string,
            string,
            AccountSession,
            Collect[],
          ]

          await localClient.openAuthView({
            platform: platform as PlatformName | '未知',
            accountId: accountId,
            title: name,
            cookies: JSON.stringify(sessionObject.cookies),
            localStorage: JSON.stringify(sessionObject.localStorage),
            isUpdateAuth: sessionObject.isUpdateAuth,
            collects,
          })
        }
        break
      case 'parsingURLContent':
        break
      default:
        break
    }
    // 在Web环境中，可以使用fetch、IndexedDB或API调用替代
    return Promise.resolve(null)
  }

  sendSync = (channel: string, ...args: unknown[]) => {
    console.log(`Web环境: 同步发送消息到 ${channel}`, args)
    // 在Web环境中，可以使用localStorage、IndexedDB或API调用替代
    return null
  }
}

// 导出Web适配器实例
export const webAPI = new WebAPIAdapter()

// 检测环境并导出适当的API

if (typeof window !== 'undefined') {
  // @ts-expect-error 这里兼容了web
  window.api = webAPI
}

import { create } from 'zustand'

type LocalState = {
  openCheck: boolean
  setOpenCheck: (open: boolean) => void
  connecting: boolean
  setConnecting: (connecting: boolean) => void
}

export const useLocalStore = create<LocalState>()((set) => ({
  openCheck: false,
  setOpenCheck: (open) => {
    return set({ openCheck: open })
  },
  connecting: false,
  setConnecting: (connecting) => set({ connecting }),
}))

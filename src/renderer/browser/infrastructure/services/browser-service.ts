import { browserChannel } from '@common/events/browser-events'
import { useMemo } from 'react'
import type { BrowserContextIdentifier } from '@common/structure/browser/contextIdentifier'

export class BrowserService {
  activeTab(tabId: string) {
    return window.api.invoke<void>(browserChannel.activeTab, tabId, window.__ident__)
  }

  closeTab(tabId: string) {
    return window.api.invoke<void>(browserChannel.closeTab, tabId, window.__ident__)
  }

  goBack() {
    window.api.send(browserChannel.goBack)
  }

  goForward() {
    window.api.send(browserChannel.goForward)
  }

  refresh() {
    window.api.send(browserChannel.refresh)
  }

  authorized(tabId: string) {
    return window.api.invoke<string>(browserChannel.authorize, tabId)
  }

  setHeaderViewTop() {
    window.api.send(browserChannel.setHeaderViewTop)
  }

  setTabViewTop() {
    window.api.send(browserChannel.setTabViewTop)
  }

  // 语义化方法：显示 Header UI 元素时调用
  showHeaderUI() {
    this.setHeaderViewTop()
  }

  // 语义化方法：隐藏 Header UI 元素，恢复正常浏览时调用
  hideHeaderUI() {
    this.setTabViewTop()
  }

  closeOtherTab(tabId: string) {
    window.api.send(browserChannel.closeOtherTab, tabId)
  }

  closeRightTab(tabId: string) {
    window.api.send(browserChannel.closeRightTab, tabId)
  }

  async saveFavorite(contextIdentifier: BrowserContextIdentifier, name: string, url: string) {
    return window.api.invoke<void>(browserChannel.saveFavorite, contextIdentifier, name, url)
  }

  async removeFavorite(contextIdentifier: BrowserContextIdentifier, url: string) {
    return window.api.invoke<void>(browserChannel.removeFavorite, contextIdentifier, url)
  }
}

export function useBrowserService() {
  return useMemo(() => new BrowserService(), [])
}

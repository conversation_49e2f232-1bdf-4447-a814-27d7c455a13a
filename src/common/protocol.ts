export const isWin = (() => {
  if (typeof window === 'undefined' && typeof process !== 'undefined') {
    // 在 Node.js 中运行
    return process.platform === 'win32'
  }
  return /windows/i.test(navigator.userAgent.toLowerCase())
})()

export const isProduction = import.meta.env.MODE === 'production'

export const FileProtocolName = 'yi-file'

export const FileProtocol = `${FileProtocolName}://`

export const localPath2Url = (path: string | File) => {
  return typeof path !== 'string'
    ? URL.createObjectURL(path)
    : path.startsWith('https://') || path.startsWith('http://')
      ? path
      : path
}

export const fileUrl2Path = (url: string) => {
  if (url.startsWith(FileProtocol)) {
    return decodeURIComponent(url.replace(FileProtocol, ''))
  }
  return url
}

export const isFileUrl = (url: string) => {
  try {
    const formatUrl = new URL(url)
    return formatUrl.protocol === FileProtocolName + ':'
  } catch (e) {
    return false
  }
}

import type { API } from './API'

declare global {
  interface Window {
    api: API

    exportDatabase: () => void
    importDatabase: () => void
    setTableData: (table: string, key: string, value: unknown) => void
    updateTableData: (table: string, key: string, change:(value)=>void) => void
    detectSession: (accountId) => void
    emitEvent: (eventName: string, params?: unknown) => void
    listEvents: () => void
  }

  interface ImportMeta {
    hot?: {
      dispose: (callback: () => void) => void
    }
    readonly env: ImportMetaEnv
  }
}

interface ImportMetaEnv {
  /**
   * 应用名称
   * @readonly
   */
  readonly VITE_APP_NAME: string
  /**
   * 应用API
   */
  readonly VITE_API_URL: string
  /**
   * 应用WSS
   */
  readonly VITE_WSS_URL: string
  /**
   * 应用WSS路径
   */
  readonly VITE_WSS_PATH: string
}

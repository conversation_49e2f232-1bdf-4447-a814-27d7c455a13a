import { ipc<PERSON><PERSON><PERSON> } from 'electron'
import { authorizeEvents } from '@common/events/authorize-events'

export interface API {
  log(message?: unknown, ...optionalParams: unknown[]): void
  error(message?: unknown, ...optionalParams: unknown[]): void
  authorized(): void
}

export const electronAPI: API = {
  log: (message?: unknown, ...optionalParams: unknown[]) => {
    ipcRenderer.send(authorizeEvents.log, message, ...optionalParams)
  },
  error: (message?: unknown, ...optionalParams: unknown[]) => {
    ipcRenderer.send(authorizeEvents.error, message, ...optionalParams)
  },
  authorized: async () => {
    ipcRenderer.send(authorizeEvents.authorizing)
  },
}

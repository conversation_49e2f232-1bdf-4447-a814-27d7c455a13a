import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'
import { electronAPI } from './API'
import { authorizeEvents } from '@common/events/authorize-events'

try {
  contextBridge.exposeInMainWorld('api', electronAPI)

  ipcRenderer.on(authorizeEvents.initializes, (_event, params) => {
    window.authParam = params
    ipcRenderer.sendToHost(authorizeEvents.initialized)
  })
} catch (error) {
  console.error(error)
}

import fs from 'fs-extra'
import { DownloaderHelper } from 'node-downloader-helper'
import path from 'path'

export interface DownloaderCallbacks {
  url: string
  dirPath: string
  fileName?: string
  onProgress?: (progress: number) => void
  onEnd?: (path?: string) => void
  onError?: (error: unknown) => void
}

/**
 * 创建下载器
 * @param param0
 * @returns
 */
export async function createDownloader({
  url,
  fileName,
  dirPath,
  onProgress = () => {},
  onEnd = () => {},
  onError = () => {},
}: DownloaderCallbacks) {
  const currentFileName = fileName ?? path.basename(url)
  const currentFilePath = path.join(dirPath, currentFileName)

  if (!fs.existsSync(dirPath)) {
    fs.ensureDirSync(dirPath)
  }

  const dl = new DownloaderHelper(url, dirPath, {
    fileName: currentFileName,
    override: true,
  })

  if (onProgress) {
    dl.on('progress', ({ progress }) => {
      onProgress(progress)
    })
  }

  dl.on('error', onError)
  dl.on('end', () => {
    onEnd && onEnd(currentFilePath)
  })

  return {
    onStart() {
      onProgress && onProgress(1)
      dl.start().catch((error) => {
        onError && onError(error)
        onEnd && onEnd()
      })
    },
    onPause() {
      void dl.pause()
    },
    onStop() {
      void dl.stop()
    },
    onResume() {
      void dl.resume()
    },
  }
}

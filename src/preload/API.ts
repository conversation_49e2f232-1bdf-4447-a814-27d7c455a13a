import { ipc<PERSON><PERSON><PERSON> } from 'electron'
import path from 'node:path'
import type { Ipc<PERSON><PERSON>erListener } from '@electron-toolkit/preload'
import { createDownloader } from './downloaderHelper'

export class API {
  env = import.meta.env

  on = (event: string, listener: Ipc<PERSON><PERSON>erListener, removeOnHotReload: boolean = true) => {
    ipcRenderer.addListener(event, listener)

    if (removeOnHotReload && import.meta.hot) {
      import.meta.hot?.dispose(() => {
        // 移除监听
        ipcRenderer.removeListener(event, listener)
      })
    }

    return () => {
      ipcRenderer.removeListener(event, listener)
    }
  }

  off = (event: string, listener: Ipc<PERSON>endererListener) => {
    ipcRenderer.removeListener(event, listener)
  }

  offAll = (event: string) => {
    ipcRenderer.removeAllListeners(event)
  }

  invoke = <T>(event: string, ...args: unknown[]): Promise<T> => {
    return ipcRenderer.invoke(event, ...args) as Promise<T>
  }

  send = (event: string, ...args: unknown[]) => {
    return ipcRenderer.send(event, ...args)
  }

  sendSync = (event: string, ...args: unknown[]) => {
    return ipcRenderer.sendSync(event, ...args)
  }

  getPreloadPath = (relatedPath: string) => path.join(__dirname, relatedPath)

  createDownloader = createDownloader
}

export const api = new API()

import {
  app,
  BrowserWindow,
  globalShortcut,
  nativeTheme,
  powerMonitor,
  protocol,
  session,
  shell,
} from 'electron'
import path, { join } from 'path'
import { is } from '@electron-toolkit/utils'
import handlers from './handlers'
import icon from '../../resources/icon.png?asset'
import log from 'electron-log/main'
import { setIsQuitting } from './auto-update'
import { showTray } from './tray'
import { mainEvents } from '@common/events/main-events'
import { getButtonPosition, store } from './store'
import WindowStateManager from './electron-window-store'
import { FileProtocolName, fileUrl2Path, isWin } from '@common/protocol'
import { pathService } from './services/path-service'
import fs from 'fs-extra'

export let mainWindow: BrowserWindow

nativeTheme.themeSource = 'light'

async function createWindow(): Promise<BrowserWindow> {
  const windowStateManager = new WindowStateManager(store)

  // 获取初始窗口配置
  const initialConfig = windowStateManager.getInitialWindowConfig()
  // Create the browser window.
  const titleBarStyle =
    process.platform === 'win32'
      ? {
          titleBarStyle: 'hidden' as const,
        }
      : {
          titleBarStyle: 'hiddenInset' as const,
          trafficLightPosition: getButtonPosition(store.get('sidebarCollapsed')),
        }

  mainWindow = new BrowserWindow({
    ...titleBarStyle,
    width: initialConfig.width,
    height: initialConfig.height,
    x: initialConfig.x,
    y: initialConfig.y,
    show: false,
    icon: icon,
    minWidth: 1060,
    minHeight: 700,
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false,
      webSecurity: false,
      webviewTag: true,
    },
  })

  if (isWin) {
    const WM_INITMENU = 0x0116

    mainWindow.hookWindowMessage(WM_INITMENU, () => {
      mainWindow.setEnabled(false)
      mainWindow.setEnabled(true)
    })
  }

  // 设置窗口状态追踪
  windowStateManager.setupWindowStateTracking(mainWindow)

  mainWindow.setMenu(null)

  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
    void showTray()
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    void shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // 监听进入和退出全屏的事件
  mainWindow.on('enter-full-screen', () => {
    mainWindow.webContents.send(mainEvents.fullscreenChanged, true)
  })

  // 系统恢复时检查
  powerMonitor.on('resume', () => {
    mainWindow.webContents.send(mainEvents.systemResume)
  })

  mainWindow.on('show', () => {
    mainWindow.webContents.send(mainEvents.windowShow)
  })

  mainWindow.on('leave-full-screen', () => {
    mainWindow.webContents.send(mainEvents.fullscreenChanged, false)
  })

  mainWindow.webContents.session.webRequest.onBeforeSendHeaders((details, callback) => {
    // hm.js替换Referer，否则无法生效
    if (details.url.startsWith('https://hm.baidu.com/hm.js')) {
      details.requestHeaders['Referer'] = 'https://lite-desktop.yixiaoer.com/'
      details.requestHeaders['User-Agent'] =
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }

    // 修复B站头像无法正常显示
    if (details.url.includes('hdslb.com')) {
      details.requestHeaders['Referer'] = 'https://member.bilibili.com/'
    }

    if (details.url.includes('sinaimg.cn')) {
      details.requestHeaders['Referer'] = 'https://weibo.com/'
    }

    callback({ cancel: false, requestHeaders: details.requestHeaders })
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    void mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    void mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }

  handlers.registerHandlers(mainWindow)

  // region 注册开发者工具快捷键
  try {
    // windows
    if (process.platform === 'win32') {
      globalShortcut.register('control+shift+alt+F1', () => {
        if (mainWindow.webContents.isDevToolsOpened()) {
          console.log('Close dev tool...')
          mainWindow.webContents.closeDevTools()
        } else {
          console.log('Open dev tool...')
          mainWindow.webContents.openDevTools({ mode: 'undocked' })
        }
      })
    }

    // macos
    if (process.platform === 'darwin') {
      globalShortcut.register('command+shift+F1', () => {
        mainWindow.webContents.openDevTools({ mode: 'detach' })
      })
    }
  } catch (e) {
    console.error(e)
  }
  // endregion

  app.on('activate', () => {
    console.log('应用激活')
    mainWindow.show()
  })

  return Promise.resolve(mainWindow)
}

log.initialize()

// 确保日志目录存在
try {
  const logDir = pathService.getLogPath()
  fs.ensureDirSync(logDir)
  console.log('日志目录已创建:', logDir)
} catch (error) {
  console.error('创建日志目录失败:', error)
}

// electron-log 接管主线程日志输出
console.log = log.log
console.info = log.info
console.warn = log.warn
console.debug = log.debug
console.error = log.error

log.transports.file.resolvePathFn = () => {
  const logDir = pathService.getLogPath()
  // 确保目录存在
  try {
    fs.ensureDirSync(logDir)
  } catch (error) {
    console.error('确保日志目录存在失败:', error)
  }
  return path.join(logDir, 'main.log')
}

log.transports.file.maxSize = 5 * 1024 * 1024

log.transports.file.archiveLogFn = (oldLogFile) => {
  try {
    const archivePath = path.dirname(oldLogFile.path)
    const timestamp = Date.now()
    const newLogFile = path.join(
      archivePath,
      `${path.basename(oldLogFile.path, '.log')}-${timestamp}.log`,
    )
    fs.renameSync(oldLogFile.path, newLogFile)
  } catch (error) {
    console.error('归档日志文件失败:', error)
  }
}

// 添加全局错误处理，防止日志写入失败导致应用崩溃
process.on('uncaughtException', (error) => {
  // 如果是日志相关的错误，尝试使用原生console输出
  if (error.message.includes('write EIO') || error.message.includes('ENOENT')) {
    console.error('日志写入错误，使用原生console输出:', error.message)
    // 临时禁用文件日志，只使用控制台日志
    log.transports.file.level = false
  } else {
    console.error('未捕获的异常:', error)
  }
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason, 'at:', promise)
})

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.

// 检查是否是唯一实例
const gotTheLock = app.requestSingleInstanceLock()

if (!gotTheLock) {
  app.quit()
} else {
  void app.whenReady().then(async () => {
    registerProtocol()

    await createWindow()

    // 自动更新

    // app.on('activate', async () => {
    //   // 无窗口时创建新窗口
    //   if (BrowserWindow.getAllWindows().length === 0) {
    //     await createWindow();
    //   }
    // });
  })

  // Quit when all windows are closed, except on macOS. There, it's common
  // for applications and their menu bar to stay active until the user quits
  // explicitly with Cmd + Q.
  app.on('window-all-closed', () => {
    console.log('window-all-closed')
    if (process.platform !== 'darwin') {
      app.quit()
    }
  })

  app.on('before-quit', () => {
    console.log('before-quit')
    setIsQuitting(true)
  })

  app.on('second-instance', () => {
    // 当运行第二个实例时, 将会聚焦到 mainWindow 这个窗口
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore()
      mainWindow.focus()
      mainWindow.show()
    }
  })

  // SIGINT 退出应用
  process.on('SIGINT', () => {
    console.debug('SIGINT退出应用')
    app.exit()
  })
}

protocol.registerSchemesAsPrivileged([
  {
    scheme: FileProtocolName,
    privileges: {
      standard: false,
      supportFetchAPI: true,
      corsEnabled: true,
      secure: true,
      bypassCSP: true,
      stream: true, // 视频文件需要流支持
    },
  },
])

function registerProtocol() {
  protocol.registerFileProtocol('yi-file', (request, callback) => {
    const localPath = fileUrl2Path(request.url) // 去掉yi-file://前缀
    callback(path.normalize(localPath))
  })
}

if (is.dev) {
  const cwd = process.cwd()
  app
    .whenReady()
    .then(() => {
      return Promise.all([
        session.defaultSession.loadExtension(path.join(cwd, 'devtools/react-devtool'), {
          allowFileAccess: true,
        }),
        session.defaultSession.loadExtension(path.join(cwd, 'devtools/redux-devtool'), {
          allowFileAccess: true,
        }),
      ])
    })
    .catch((e) => log.error('安装失败的扩展:', e))
}

void app.whenReady().then(() => {
  // 百度统计拦截
  session.defaultSession.webRequest.onBeforeRequest((details, callback) => {
    const productionBaseUrlReg = /(http|file)%3A%2F%2F.+?%2Frenderer%2F/g
    const developmentBaseUrlReg = /http%3A%2F%2Flocalhost%3A\d+%2F/g
    // hn.gif是上报数据的请求，生产环境需要替换app://./和http://./为某个合法的域名，这里是伪装成https://lite-desktop.yixiaoer.com/
    if (
      (details.url.startsWith('https://hm.baidu.com/hm.gif') &&
        //包含app://./或http://./的请求
        productionBaseUrlReg.test(details.url)) ||
      developmentBaseUrlReg.test(details.url)
    ) {
      callback({
        cancel: false,
        redirectURL: details.url
          .replaceAll(productionBaseUrlReg, 'https%3A%2F%2Flite-desktop.yixiaoer.com%2F') //http://..../renderer/ -> https://lite-desktop.yixiaoer.com/
          .replaceAll(developmentBaseUrlReg, 'https%3A%2F%2Flite-desktop-dev.yixiaoer.com%2F'), //http://localhost:6547/ -> https://lite-desktop-dev.yixiaoer.com/,
      })
      // 取消当前请求
      return
    }

    callback({ cancel: false })
  })
})

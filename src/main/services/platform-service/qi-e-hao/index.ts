import { AuthorizingAccountInfo } from '@main/model/authorizing-account-info'
import { PlatformService } from '../index'
import { platformNames } from '@common/model/platform-name'
import { getPlatformServicePromise } from '../import-promise'
import { Platforms } from '@yixiaoer/platform-service'

class QiEHaoPlatformService extends PlatformService {
  constructor() {
    super(platformNames.QiEHao)
  }

  async getAccountInfo(cookies: Electron.Cookie[]): Promise<AuthorizingAccountInfo> {
    const cookie = this.convertCookie(cookies)

    const info = await this.getData(
      async () => (await getPlatformServicePromise()).Qiehao.getQiehaoUserInfo(cookie),
      (x) => x.data!,
    )
    return new AuthorizingAccountInfo(
      platformNames.QiEHao,
      info.cpInfo.mediaId,
      info.cpInfo.mediaName,
      info.cpInfo.header,
    )
  }

  async queryAccountOverview(cookies: Electron.Cookie[]) {
    const cookie = this.convertCookie(cookies)

    const platformService = await getPlatformServicePromise()

    // 企鹅号有子类型配置：视频分析(video) 和 图文分析(dynamic)
    const [video, article] = await Promise.all([
      platformService.DataService.getAccountReport(Platforms.QiEHao, cookie, 'video'),
      platformService.DataService.getAccountReport(Platforms.QiEHao, cookie, 'article'),
    ])

    return {
      video: video.data || [],
      article: article.data || [],
    }
  }

  async queryPublishOverview(cookies: Electron.Cookie[]) {
    const cookie = this.convertCookie(cookies)

    const platformService = await getPlatformServicePromise()

    // 企鹅号分子类型：文章对应article，视频对应video
    const videoOverviews = await this.getData(
      async () =>
        await platformService.DataService.getAccountContentList(
          Platforms.QiEHao,
          false,
          cookie,
          'video',
        ),
      (x) => x.data || [],
    )
    const articleOverviews = await this.getData(
      async () =>
        await platformService.DataService.getAccountContentList(
          Platforms.QiEHao,
          false,
          cookie,
          'article',
        ),
      (x) => x.data || [],
    )
    return [...videoOverviews, ...articleOverviews]
  }
}

export const qiEHaoPlatformService = new QiEHaoPlatformService()

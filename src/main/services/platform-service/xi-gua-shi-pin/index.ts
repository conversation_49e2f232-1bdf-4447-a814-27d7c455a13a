import { AuthorizingAccountInfo } from '@main/model/authorizing-account-info'
import { PlatformService } from '../index'
import { platformNames } from '@common/model/platform-name'
import { getPlatformServicePromise } from '../import-promise'

class XiGuaShiPinPlatformService extends PlatformService {
  constructor() {
    super(platformNames.XiGuaShiPin)
  }

  async getAccountInfo(cookies: Electron.Cookie[]): Promise<AuthorizingAccountInfo> {
    const cookie = this.convertCookie(cookies)

    const info = await this.getData(
      async () => (await getPlatformServicePromise()).Xiguashipin.getXiguashipinUserInfo(cookie),
      (x) => x.data!,
    )
    return new AuthorizingAccountInfo(
      platformNames.XiGuaShiPin,
      info.userId,
      info.userName,
      info.userImage,
    )
  }
}

export const xiGuaShiPinPlatformService = new XiGuaShiPinPlatformService()

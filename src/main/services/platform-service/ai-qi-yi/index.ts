import { AuthorizingAccountInfo } from '@main/model/authorizing-account-info'
import { PlatformService } from '../index'
import { platformNames } from '@common/model/platform-name'
import { getPlatformServicePromise } from '../import-promise'
import { Platforms } from '@yixiaoer/platform-service'

class AiQiYiPlatformService extends PlatformService {
  constructor() {
    super(platformNames.AiQiYi)
  }

  async getAccountInfo(cookies: Electron.Cookie[]): Promise<AuthorizingAccountInfo> {
    const cookie = this.convertCookie(cookies)

    return this.convert2PlatformAccountInfo(
      async () => (await getPlatformServicePromise()).Aiqiyi.getAiqiyiUserInfo(cookie),
      (x) =>
        new AuthorizingAccountInfo(
          platformNames.AiQiYi,
          x.data!.uid.toString(),
          x.data!.nickname,
          x.data!.icon,
          x.verify,
        ),
    )
  }

  async queryAccountOverview(cookies: Electron.Cookie[]) {
    const cookie = this.convertCookie(cookies)

    const platformService = await getPlatformServicePromise()

    // 爱奇艺没有子类型配置，只获取一种数据类型放到 video 字段中
    const data = await platformService.DataService.getAccountReport(Platforms.AiQiYi, cookie)

    return {
      video: data.data || [],
    }
  }

  async queryPublishOverview(cookies: Electron.Cookie[]) {
    const cookie = this.convertCookie(cookies)

    const platformService = await getPlatformServicePromise()

    return await this.getData(
      async () =>
        await platformService.DataService.getAccountContentList(Platforms.AiQiYi, false, cookie),
      (x) => x.data || [],
    )
  }
}

export const aiQiYiPlatformService = new AiQiYiPlatformService()

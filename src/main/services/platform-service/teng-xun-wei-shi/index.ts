import { AuthorizingAccountInfo } from '@main/model/authorizing-account-info'
import { PlatformService } from '../index'
import { platformNames } from '@common/model/platform-name'
import { getPlatformServicePromise } from '../import-promise'
import type { PlatformDataItem } from '@common/structure'
import { Platforms } from '@yixiaoer/platform-service'

class TengXunWeiShiPlatformService extends PlatformService {
  constructor() {
    super(platformNames.TengXunWeiShi)
  }

  async getAccountInfo(cookies: Electron.Cookie[]): Promise<AuthorizingAccountInfo> {
    const cookie = this.convertCookie(cookies)

    const authorInfo = await this.getData(
      async () =>
        (await getPlatformServicePromise()).Tengxunweishi.getTengxunweishiAuthInfo(cookie),
      (x) => x.data!,
    )

    const info = await this.getData(
      async () =>
        (await getPlatformServicePromise()).Tengxunweishi.getTengxunweishiUserInfo(
          cookie,
          authorInfo.personid,
        ),
      (x) => x.data!,
    )

    return new AuthorizingAccountInfo(
      platformNames.TengXunWeiShi,
      info.personId,
      info.nickName,
      info.avatar,
    )
  }

  async getTopics(cookies: Electron.Cookie[], keyword: string) {
    const cookie = this.convertCookie(cookies)

    const result = await this.getData(
      async () =>
        (await getPlatformServicePromise()).Tengxunweishi.getWeiShiTopics(cookie, keyword),
      (x) => x.topicList ?? [],
    )
    return result.map(
      (x) =>
        ({
          id: x.id,
          text: x.name,
          raw: x,
        }) satisfies PlatformDataItem as PlatformDataItem,
    )
  }

  async queryAccountOverview(cookies: Electron.Cookie[]) {
    const cookie = this.convertCookie(cookies)

    const platformService = await getPlatformServicePromise()

    // 腾讯微视没有子类型配置，只获取一种数据类型放到 video 字段中
    const data = await platformService.DataService.getAccountReport(Platforms.TengXunWeiShi, cookie)

    return {
      video: data.data || [],
    }
  }

  async queryPublishOverview(cookies: Electron.Cookie[]) {
    const cookie = this.convertCookie(cookies)

    const platformService = await getPlatformServicePromise()

    return await this.getData(
      async () =>
        await platformService.DataService.getAccountContentList(
          Platforms.TengXunWeiShi,
          false,
          cookie,
        ),
      (x) => x.data || [],
    )
  }
}

export const tengXunWeiShiPlatformService = new TengXunWeiShiPlatformService()

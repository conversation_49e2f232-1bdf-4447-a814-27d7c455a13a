import fs from 'node:fs'
import path from 'path'
import fsExtra from 'fs-extra'

function mkdirSyncRecursive(filename: string) {
  const directories = path.dirname(filename).split(path.sep)
  let currentDirectory = ''
  directories.forEach((directory) => {
    currentDirectory += directory + path.sep
    if (!fs.existsSync(currentDirectory)) {
      fs.mkdirSync(currentDirectory)
    }
  })
}

class FileSystemService {
  writeToFile(path: string, data: Buffer) {
    return new Promise<void>((resolve, reject) => {
      try {
        mkdirSyncRecursive(path)
        fs.writeFile(path, data, (err) => {
          if (err) {
            reject(err)
          } else {
            resolve()
          }
        })
      } catch (e) {
        reject(e)
      }
    })
  }

  // 同步方法writeToFile
  writeToFileSync(path: string, data: Buffer) {
    mkdirSyncRecursive(path)
    fs.writeFileSync(path, data)
  }

  copyDirectory(
    source: string,
    destination: string,
    options: {
      blacklist?: (string | RegExp)[]
      whitelist?: (string | RegExp)[]
      recursive?: boolean
    } = {},
  ) {
    const { blacklist = [], whitelist = [], recursive = true } = options

    const shouldCopy = (filePath: string) => {
      if (whitelist.length > 0 && !whitelist.some((pattern) => filePath.match(pattern))) {
        return false
      }
      if (blacklist.some((pattern) => filePath.match(pattern))) {
        return false
      }
      return true
    }

    const copyRecursive = (src: string, dest: string) => {
      if (!shouldCopy(src)) return

      const stats = fs.lstatSync(src)
      if (stats.isDirectory()) {
        fsExtra.ensureDirSync(dest)
        fs.readdirSync(src).forEach((child) => {
          copyRecursive(path.join(src, child), path.join(dest, child))
        })
      } else {
        fsExtra.copySync(src, dest)
      }
    }

    if (recursive) {
      copyRecursive(source, destination)
    } else {
      fs.readdirSync(source).forEach((child) => {
        const srcPath = path.join(source, child)
        const destPath = path.join(destination, child)
        if (shouldCopy(srcPath)) {
          fsExtra.copySync(srcPath, destPath)
        }
      })
    }
  }

  remove(dirPath: string): void {
    fsExtra.removeSync(dirPath)
  }

  isFileExist(path: string) {
    return fs.existsSync(path)
  }

  copyLargeFile(sourcePath: string, destPath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      mkdirSyncRecursive(destPath)
      // 创建读写流
      const readStream = fs.createReadStream(sourcePath)
      const writeStream = fs.createWriteStream(destPath)

      // 处理拷贝文件完成及错误
      readStream.on('error', reject)
      writeStream.on('error', reject)
      writeStream.on('finish', resolve)

      // 管道传输数据
      readStream.pipe(writeStream)
    })
  }

  async safeCopy(sourcePath: string, destPath: string) {
    await fsExtra.copy(sourcePath, destPath, {
      overwrite: false,
      filter: (src) => {
        // 仅复制目标不存在的文件
        const targetPath = src.replace(sourcePath, destPath)
        return !fs.existsSync(targetPath)
      },
    })
  }

  /**
   * 从文件路径中提取文件名
   * 支持Windows和Mac/Linux路径格式
   * @param {string} filePath - 完整的文件路径
   * @param {boolean} [keepExtension=true] - 是否保留文件扩展名
   * @returns {string} - 提取出的文件名
   */
  public getFileName(filePath: string, keepExtension: boolean = true): string {
    // 使用Node.js的path模块来处理不同操作系统的路径差异
    const path = require('path')

    // 获取文件名(包含扩展名)
    const fullFileName = path.basename(filePath)

    if (keepExtension) {
      return fullFileName
    } else {
      // 如果不需要扩展名，则将其移除
      return path.parse(fullFileName).name
    }
  }
}

export const fileSystemService = new FileSystemService()

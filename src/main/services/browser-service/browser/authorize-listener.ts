import { AccountSpaceBrowserContext } from './browser-context'
import { globalBrowserContextManager } from './contextManager'
import { browserEvents } from '@main/services/eventBus/event/events'
import { browserEventBus } from '@main/services/eventBus/eventBus'
import { tabManager } from './tab-manager'
import { getPlatformService } from '@main/services/platform-service/factory'
import { getAuthorizeServicePromise } from '@main/services/platform-service/import-promise'

class AuthorizeListenerManager {
  init() {
    browserEventBus.on(browserEvents.tabsInContextIncreased, async (contextId) => {
      const context = globalBrowserContextManager.get(contextId)
      if (
        !(context instanceof AccountSpaceBrowserContext) ||
        context.account?.type === 'wechatSubAccount'
      )
        return

      console.log('检测登录状态!!!', context.identifier)
      try {
        const cookies = await context.session.cookies.get({})
        const accountSession = {
          cookies,
          localStorage: {},
        }
        const service = getPlatformService(context.platformName)
        const currentState = await service.sessionDetect(accountSession, true)

        const prevSessionState = context.sessionState

        console.log('检测完成!!!', currentState, prevSessionState, accountSession)

        if (prevSessionState === '已失效' && currentState === '正常') {
          console.log('登录成功!!!', context.identifier)

          const tabs = tabManager.getTabsByContext(context.identifier)

          // 等待平台特定的完成信号
          const platformConfig = (await getAuthorizeServicePromise()).platformConfig[
            context.platformName
          ]

          if (platformConfig && platformConfig.waitForLoginFinish)
            await platformConfig.waitForLoginFinish(
              context.identifier,
              tabs.map((x) => x.view),
              new AbortController().signal,
            )

          for (const tab of tabs) {
            accountSession.localStorage = {
              ...accountSession.localStorage,
              ...(await tab.view.webContents.executeJavaScript(`
                   (function() {
                     const localStorageData = {};
                     for (let i = 0; i < localStorage.length; i++) {
                       const key = localStorage.key(i);
                       if (key) {
                         const item = localStorage.getItem(key);
                         if (item) localStorageData[key] = item;
                       }
                     }
                     return localStorageData;
                   })()
                 `)),
            }
          }

          browserEventBus.emit(
            browserEvents.accountSessionStateChanged,
            context.identifier,
            accountSession,
            currentState, // 传递检测出的状态
          )
        } else if (prevSessionState === '正常' && currentState === '已失效') {
          console.log('登录失效!!!', context.identifier)
          browserEventBus.emit(
            browserEvents.accountSessionStateChanged,
            context.identifier,
            accountSession,
            currentState, // 传递检测出的状态
          )
        }

        if (currentState === '正常') {
          // 补丁：有账号信息的情况下，登录成功后需要更新认证状态
          void service.getAccountInfo(accountSession.cookies).then((accountInfo) => {
            if (
              context.account &&
              accountInfo.identityVerified !== context.account.identityVerified
            ) {
              browserEventBus.emit(
                browserEvents.identifyVerified,
                context.identifier,
                accountInfo.identityVerified,
              )
            }
          })
        }
      } catch (e) {
        console.error(e)
      }
    })
  }
}

export const authorizeListenerManager = new AuthorizeListenerManager()

import type { BusinessContext } from '@common/model/business-context'

class SystemService {
  public authorizationToken: string = ''
  public businessContext: BusinessContext = 'User'

  changeBusinessContext(context: BusinessContext) {
    this.businessContext = context
  }

  getBusinessContext() {
    return this.businessContext
  }

  setAuthorizationToken(token: string) {
    this.authorizationToken = token
  }

  getAuthorizationToken() {
    return this.authorizationToken
  }
}

export const systemService = new SystemService()

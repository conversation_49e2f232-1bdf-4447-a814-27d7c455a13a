import type { AccountSession } from '@common/structure'
import type { <PERSON><PERSON> } from 'electron'
import type { Logger } from 'electron-log'
import log from 'electron-log'
import path from 'path'
import fs from 'fs-extra'
import { pathService } from './path-service'

export class AuthorizeLogService {
  constructor(private logger: Logger) {}

  logAuthorizeSuccess(
    platform: string,
    authorId: string,
    authorName: string,
    accountSession: AccountSession,
  ) {
    this.logger.info(
      `Authorize success: ${platform} ${authorId} ${authorName} ${JSON.stringify(accountSession)}`,
    )
  }

  logAuthorizeFail(
    platform: string,
    authorId: string,
    authorName: string,
    detail: string | unknown,
    cookies: <PERSON>ie[],
  ) {
    this.logger.error(
      `Authorize fail: ${platform} ${authorId} ${authorName} ${JSON.stringify(detail)} ${JSON.stringify(cookies)}`,
    )
  }

  logAuthorizeError(
    platform: string,
    authorId: string,
    authorName: string,
    accountSession: AccountSession,
    error: Error,
  ) {
    this.logger.error(
      `Authorize error: ${platform} ${authorId} ${authorName} ${JSON.stringify(accountSession)} ${error.message}`,
    )
  }

  logAuthorizeUpdate(
    platform: string,
    authorId: string,
    authorName: string,
    accountSession: AccountSession,
  ) {
    this.logger.info(
      `Authorize update: ${platform} ${authorId} ${authorName} ${JSON.stringify(accountSession)}`,
    )
  }
}

const logger = log.create({
  logId: 'Authorize',
})

logger.transports.console.level = false

// Set the maximum log size to 5 MB
logger.transports.file.maxSize = 5 * 1024 * 1024

// Define the custom archive function
logger.transports.file.archiveLogFn = (oldLogFile) => {
  const archivePath = path.dirname(oldLogFile.path)
  const timestamp = Date.now()
  const newLogFile = path.join(
    archivePath,
    `${path.basename(oldLogFile.path, '.log')}-${timestamp}.log`,
  )
  fs.renameSync(oldLogFile.path, newLogFile)
}

logger.transports.file.resolvePathFn = () => {
  const logDir = pathService.getLogPath()
  return path.join(logDir, 'authorize.log')
}

export const authorizeLogService = new AuthorizeLogService(logger)

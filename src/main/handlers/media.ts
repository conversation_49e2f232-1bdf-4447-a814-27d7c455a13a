import { ipcMain } from 'electron'
import type { GeneralTrack, ImageTrack, MediaInfo, Track } from 'mediainfo.js'
import fs from 'node:fs'
import { VideoFileInfo } from '@main/model/video-file-info'
import { uiEvents } from '@common/events/ui-events'
import type { VideoFileInfoStructure } from '@common/model/video-file-info'
import { ImageFileInfo } from '@main/model/image-file-info'
import type { ImageFileInfoStructure } from '@common/model/image-file-info'
import sizeOf from 'image-size'
import { fileSystemService } from '@main/services/fileSystemService'
// 不要试图改成 import { default as MediaInfoFactory } from 'mediainfo.js' ，否则会报错 MediaInfoFactory is not a function
// https://github.com/buzz/mediainfo.js/issues/143#issuecomment-1714634596
// eslint-disable-next-line @typescript-eslint/no-var-requires
const { default: MediaInfoFactory } = require('mediainfo.js')

export default {
  registerHandlers(_browserWindow: Electron.BrowserWindow) {
    function functionFactory(path: string) {
      const getSize = () => fs.promises.stat(path).then((stat) => stat.size)
      const readChunk = (size: number, offset: number): Uint8Array => {
        const buffer = Buffer.alloc(size) as unknown as Uint8Array // as unknown 是因为项目配置问题导致vs无法识别Buffer为类型Uint8Array的子类，应该修复项目配置后即可取消这个强制转换
        const fd = fs.openSync(path, 'r')

        fs.readSync(fd, buffer, 0, size, offset)
        fs.closeSync(fd)

        return buffer
      }
      return { getSize, readChunk }
    }

    MediaInfoFactory().then((mediainfo: MediaInfo) => {
      ipcMain.handle(
        uiEvents.getVideoInfo,
        async (_event, path: string): Promise<VideoFileInfoStructure> => {
          const { getSize, readChunk } = functionFactory(path)
          // eslint-disable-next-line no-async-promise-executor
          return new Promise<VideoFileInfoStructure>(async (resolve, reject) => {
            try {
              const analyzeResult = await mediainfo.analyzeData(getSize, readChunk)

              const generalInfo = analyzeResult.media?.track.find(
                (x: Track) => x['@type'] === 'General',
              )
              const videoInfo = analyzeResult.media?.track.find(
                (x: Track) => x['@type'] === 'Video',
              )
              const audioInfo = analyzeResult.media?.track.find(
                (x: Track) => x['@type'] === 'Audio',
              )

              if (
                !generalInfo?.Format ||
                !generalInfo?.Duration ||
                !generalInfo?.FileSize ||
                !videoInfo?.Width ||
                !videoInfo?.Height ||
                !videoInfo?.Format ||
                !videoInfo?.BitDepth
              ) {
                reject('文件信息缺失:' + JSON.stringify(analyzeResult.media?.track))
                return
              }

              // path解析出filename
              const name = fileSystemService.getFileName(path)

              resolve(
                VideoFileInfo.of(
                  path,
                  Number.parseInt(generalInfo.FileSize),
                  generalInfo.Format,
                  generalInfo.Duration,
                  videoInfo.Width,
                  videoInfo.Height,
                  videoInfo.Format,
                  videoInfo.BitDepth,
                  generalInfo.FileName ?? name ?? '',
                  !!audioInfo,
                ),
              )
            } catch (e) {
              console.error(e)
              reject(e)
            }
          })
        },
      )
      ipcMain.handle(
        uiEvents.getImageInfo,
        async (_event, path: string): Promise<ImageFileInfoStructure> => {
          const { getSize, readChunk } = functionFactory(path)
          // eslint-disable-next-line no-async-promise-executor
          return new Promise<ImageFileInfo>(async (resolve, reject) => {
            try {
              const analyzeResult = await mediainfo.analyzeData(getSize, readChunk)

              const imageInfo = analyzeResult.media?.track.find(
                (x: Track) => x['@type'] === 'Image',
              ) as ImageTrack
              const generalInfo = analyzeResult.media?.track.find(
                (x: Track) => x['@type'] === 'General',
              ) as GeneralTrack
              let width = imageInfo.Width
              let height = imageInfo.Height
              if (!width || !height) {
                const size = sizeOf(path)
                width = size.width
                height = size.height
              }
              if (!imageInfo || !width || !height || !generalInfo.FileSize || !imageInfo.Format) {
                reject('文件信息缺失:' + JSON.stringify(analyzeResult.media?.track))
                return
              }

              resolve(
                ImageFileInfo.of(
                  width,
                  height,
                  Number.parseInt(generalInfo.FileSize),
                  imageInfo.Format,
                  path,
                ),
              )
            } catch (e) {
              console.error(e)
              reject(e)
            }
          })
        },
      )
    })
  },
}

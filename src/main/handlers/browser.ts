import { ipc<PERSON>ain } from 'electron'
import { BrowserService, browserService } from '@main/services/browser-service/browser-service'
import { browserAuthorizeChannel, browserChannel } from '@common/events/browser-events'
import type { BrowserContextIdentifier } from '@common/structure/browser/contextIdentifier'
import { browserEventBus } from '@main/services/eventBus/eventBus'
import type { SpaceIdentifier } from '@common/structure/space/space-identifier'
import { spaceService } from '@main/services/space-service/space-service'
import { browserEvents } from '@main/services/eventBus/event/events'
import { getAuthorizeServicePromise } from '@main/services/platform-service/import-promise'
import type { TeamIdentifier } from '@common/structure/team-identifier'
import type { ContextFavorite } from '@common/structure/space/context-favorite'
import { globalBrowserContextManager } from '@main/services/browser-service/browser/contextManager'
import type { AccountSession, SessionState } from '@common/structure'
import { getPlatformService } from '@main/services/platform-service/factory'
import { identifierService } from '@common/infrastructure/services/identifier-service'
import { tabManager } from '@main/services/browser-service/browser/tab-manager'
import type { AccountInfoStructure } from '@common/model/account-info'
import { authorizeListenerManager } from '@main/services/browser-service/browser/authorize-listener'

export const BrowserServiceMap: Record<string, BrowserService> = {}

browserEventBus.on('group-closed', (windowIdentifier: string) => {
  delete BrowserServiceMap[windowIdentifier]
})

export function useBrowserService(windowIdentifier: string) {
  if (!BrowserServiceMap[windowIdentifier]) {
    BrowserServiceMap[windowIdentifier] = new BrowserService(windowIdentifier)
  }

  return BrowserServiceMap[windowIdentifier]
}

export default {
  async registerHandlers(_browserWindow: Electron.BrowserWindow) {
    ipcMain.handle(
      browserChannel.closeBrowser,
      (_event, browserContextIdentifier: BrowserContextIdentifier) => {
        console.log('closeBrowser', browserContextIdentifier)
        return browserService.closeBrowser()
      },
    )
    ipcMain.handle(
      browserChannel.openTab,
      async (_event, browserContextIdentifier: BrowserContextIdentifier, urls: string[]) => {
        console.log('openTab', browserContextIdentifier, urls)
        await Promise.all(
          urls.map((url) => browserService.openWebTab(browserContextIdentifier, url)),
        )
      },
    )
    ipcMain.handle(
      browserChannel.newOpenTab,
      async (
        _event,
        browserContextIdentifier: BrowserContextIdentifier,
        urls: string[],
        windowIdentifier: string,
      ) => {
        console.log('newOpenTab', browserContextIdentifier, urls)

        const service = useBrowserService(windowIdentifier)
        await Promise.all(urls.map((url) => service.openWebTab(browserContextIdentifier, url)))
      },
    )
    ipcMain.handle(browserChannel.activeTab, (_event, tabId: string, windowIdentifier: string) => {
      console.log('activeTab', tabId)
      if (windowIdentifier && BrowserServiceMap[windowIdentifier]) {
        return BrowserServiceMap[windowIdentifier].activeTab(tabId)
      }
      return browserService.activeTab(tabId)
    })
    ipcMain.handle(browserChannel.closeTab, (_event, tabId: string, windowIdentifier: string) => {
      console.log('closeTab', tabId)
      if (windowIdentifier && BrowserServiceMap[windowIdentifier]) {
        return BrowserServiceMap[windowIdentifier].closeTab(tabId)
      }
      return browserService.closeTab(tabId)
    })
    ipcMain.handle(browserChannel.authorize, async (_event, tabId: string) => {
      console.log('authorize', tabId)
      try {
        const tab = tabManager.getTab(tabId)
        await tab.authorize()
        return '授权成功'
      } catch (error) {
        console.error('授权异常', error)
        return '未检测到登录成功，请重试'
      }
    })
    ipcMain.handle(
      browserChannel.saveFavorite,
      (_event, contextIdentifier: BrowserContextIdentifier, title: string, url: string) => {
        console.log('saveFavorite', contextIdentifier, title, url)
        return _browserWindow.webContents.send(
          browserChannel.saveFavorite,
          contextIdentifier,
          title,
          url,
        )
      },
    )
    ipcMain.handle(
      browserChannel.removeFavorite,
      (_event, contextIdentifier: BrowserContextIdentifier, url: string) => {
        console.log('removeFavorite', contextIdentifier, url)
        return _browserWindow.webContents.send(
          browserChannel.removeFavorite,
          contextIdentifier,
          url,
        )
      },
    )
    ipcMain.handle(
      browserChannel.openAccountTab,
      async (
        _event,
        contextIdentifier: BrowserContextIdentifier,
        platformName: string,
        accountId: string | null,
        urls?: string[],
      ) => {
        console.log('openAccountTab', contextIdentifier)
        let url: string
        //指定了urls则使用第一个，否则使用platformConfig中的entryUrl
        if (urls && urls.length > 0) {
          url = urls[0]
        } else if (accountId) {
          const entryUrl = (await getAuthorizeServicePromise()).platformConfig[platformName]
            ?.entryUrl
          if (!entryUrl) throw new Error('平台entryUrl未配置')
          url = entryUrl
        } else {
          //没有accountId则使用authorizeUrl
          const authorizeUrl = (await getAuthorizeServicePromise()).platformConfig[platformName]
            ?.authorizeUrl
          if (!authorizeUrl) throw new Error('平台authorizeUrl未配置')
          url = authorizeUrl
        }
        await browserService.openAccountTab(contextIdentifier, platformName, url)
      },
    )

    ipcMain.handle(
      browserChannel.openAccountTabInNewWindow,
      async (
        _event,
        ident: string,
        contextIdentifier: BrowserContextIdentifier,
        platformName: string,
        accountId: string | null,
        urls?: string[],
      ) => {
        console.log('openAccountTabInNewWindow', contextIdentifier)
        let url: string
        //指定了urls则使用第一个，否则使用platformConfig中的entryUrl
        if (urls && urls.length > 0) {
          url = urls[0]
        } else if (accountId) {
          const entryUrl = (await getAuthorizeServicePromise()).platformConfig[platformName]
            ?.entryUrl
          if (!entryUrl) throw new Error('平台entryUrl未配置')
          url = entryUrl
        } else {
          //没有accountId则使用authorizeUrl
          const authorizeUrl = (await getAuthorizeServicePromise()).platformConfig[platformName]
            ?.authorizeUrl
          if (!authorizeUrl) throw new Error('平台authorizeUrl未配置')
          url = authorizeUrl
        }

        const service = useBrowserService(ident)
        await service.openAccountTab(contextIdentifier, platformName, url)
      },
    )

    ipcMain.handle(
      browserChannel.restoreContext,
      async (
        _event,
        identifier: BrowserContextIdentifier,
        color: string,
        contextName: string,
        startUrl: string,
        session: AccountSession | null,
      ) => {
        console.log('restoreContext', identifier)
        await globalBrowserContextManager.openWebSpaceContext(
          identifier,
          color,
          contextName,
          false,
          startUrl,
          session,
        )
      },
    )
    ipcMain.handle(
      browserChannel.closeAllTab,
      async (_event, identifier: BrowserContextIdentifier) => {
        console.log('closeAllTab', identifier)
        await browserService.closeAll(identifier)
      },
    )
    ipcMain.handle(
      browserChannel.createNewContext,
      async (
        _event,
        identifier: BrowserContextIdentifier,
        color: string,
        contextName: string,
        unsaved: boolean,
      ) => {
        console.log('createNewContext', identifier)
        await globalBrowserContextManager.openWebSpaceContext(
          identifier,
          color,
          contextName,
          unsaved,
        )
      },
    )
    ipcMain.handle(
      browserChannel.createAccountContext,
      async (
        _event,
        identifier: BrowserContextIdentifier,
        color: string,
        platformName: string,
        account: AccountInfoStructure | null,
        accountSession: AccountSession | null,
      ) => {
        console.log('createAccountContext', identifier)
        await globalBrowserContextManager.openAccountSpaceContext(
          identifier,
          color,
          platformName,
          account,
          accountSession,
        )
      },
    )
    ipcMain.handle(
      browserChannel.updateAccountContext,
      async (
        _event,
        identifier: BrowserContextIdentifier,
        account: AccountInfoStructure | null,
      ) => {
        console.log('updateAccountContext', identifier)
        globalBrowserContextManager.updateAccountContext(identifier, account)
      },
    )
    ipcMain.handle(
      browserChannel.isContextCreated,
      (_event, identifier: BrowserContextIdentifier) => {
        console.log('isContextCreated', identifier)
        return globalBrowserContextManager.isCreated(identifier)
      },
    )
    ipcMain.handle(
      browserChannel.anyWebSpaceTabOpened,
      (_event, identifier: BrowserContextIdentifier) => {
        console.log('anyWebSpaceTabOpened', identifier)
        return tabManager.anyWebSpaceTabOpened(identifier)
      },
    )
    ipcMain.handle(
      browserChannel.anyAccountSpaceTabOpened,
      (_event, identifier: BrowserContextIdentifier) => {
        console.log('anyAccountSpaceTabOpened', identifier)
        return tabManager.anyAccountSpaceTabOpened(identifier)
      },
    )
    ipcMain.handle(browserChannel.getOpenedUrls, (_event, identifier: BrowserContextIdentifier) => {
      console.log('getOpenedUrls', identifier)
      return browserService.getOpenedUrls(identifier)
    })
    ipcMain.handle(
      browserChannel.syncFavorites,
      (
        _event,
        teamIdentifier: TeamIdentifier,
        contextFavorites: Record<string, ContextFavorite[]>,
      ) => {
        console.log('syncFavorites', teamIdentifier)
        return browserService.saveFavorites(teamIdentifier, contextFavorites)
      },
    )
    ipcMain.handle(
      browserChannel.getAccountSession,
      async (_event, contextIdentifier: BrowserContextIdentifier, platformName: string) => {
        console.log('getAccountSession', contextIdentifier)

        const entryUrl = (await getAuthorizeServicePromise()).platformConfig[platformName]?.entryUrl
        if (!entryUrl) throw new Error('平台entryUrl未配置')

        const tabId = identifierService.generateUUID()
        const tab = await tabManager.openAccountSpaceTab(tabId, contextIdentifier, entryUrl)
        const accountSession = await browserService.getAccountSession(tab)
        tabManager.closeTab(tabId)
        return accountSession
      },
    )

    ipcMain.handle(
      browserChannel.getWebSession,
      async (_event, contextIdentifier: BrowserContextIdentifier, url: string) => {
        console.log('getWebSession', contextIdentifier)

        const tabId = identifierService.generateUUID()
        const tab = await tabManager.openWebSpaceTab(tabId, contextIdentifier, url)
        const accountSession = await browserService.getAccountSession(tab)
        tabManager.closeTab(tabId)
        return accountSession
      },
    )
    ipcMain.handle(
      browserChannel.cleanupSessionDirectory,
      (_event, userId: string, teamId: string, existedContextIds: string[]) => {
        console.log('cleanupSessionDirectory', existedContextIds)
        return globalBrowserContextManager.cleanupSessionDirectory(
          userId,
          teamId,
          existedContextIds,
        )
      },
    )
    ipcMain.handle(browserChannel.hasSpaceIcon, (_event, spaceIdentifier: SpaceIdentifier) => {
      console.log('hasSpaceIcon', spaceIdentifier)
      return spaceService.hasSpaceIcon(spaceIdentifier)
    })
    ipcMain.handle(
      browserChannel.updateSpaceIcon,
      (_event, spaceIdentifier: SpaceIdentifier, favicon: string) => {
        console.log('updateSpaceIcon', spaceIdentifier)
        return spaceService.updateIcon(spaceIdentifier, favicon)
      },
    )
    ipcMain.handle(browserChannel.getSpaceIcon, (_event, spaceIdentifier: SpaceIdentifier) => {
      console.log('getSpaceIcon', spaceIdentifier)
      return spaceService.getIconUrl(spaceIdentifier)
    })

    browserEventBus.on(
      'tab-favicon-updated',
      (_tabId: string, contextIdentifier: BrowserContextIdentifier, url: string) => {
        _browserWindow.webContents.send(
          browserChannel.contextFaviconUpdated,
          contextIdentifier,
          url,
        )
      },
    )

    browserEventBus.on('space-icon-updated', (spaceIdentifier: SpaceIdentifier) => {
      _browserWindow.webContents.send(browserChannel.spaceIconUpdated, spaceIdentifier)
    })

    browserEventBus.on(
      browserEvents.WebSpaceAuthorized,
      async (
        contextIdentifier: BrowserContextIdentifier,
        url: string,
        accountSession: AccountSession,
      ) => {
        const context = globalBrowserContextManager.getWebSpaceContext(contextIdentifier)
        _browserWindow.webContents.send(
          browserAuthorizeChannel.webSpaceAuthorizeSuccess,
          contextIdentifier,
          url,
          context.contextName,
          context.color,
          context.unsaved,
          accountSession,
        )
      },
    )

    browserEventBus.on(
      browserEvents.accountSessionStateChanged,
      async (
        contextIdentifier: BrowserContextIdentifier,
        accountSession: AccountSession,
        detectedState: SessionState,
      ) => {
        const context = globalBrowserContextManager.getAccountSpaceContext(contextIdentifier)
        const currentState = detectedState // 使用检测出的状态而不是Context的状态

        console.log('accountSessionStateChanged', contextIdentifier, detectedState)

        if (currentState === '正常') {
          _browserWindow.webContents.send(
            browserAuthorizeChannel.accountAuthorizing,
            contextIdentifier,
            context.account,
          )
          try {
            const accountInfo = await getPlatformService(context.platformName).getAccountInfo(
              accountSession.cookies,
            )
            _browserWindow.webContents.send(
              browserAuthorizeChannel.accountAuthorizeSuccess,
              contextIdentifier,
              context.account,
              context.color,
              accountSession.cookies,
              accountSession.localStorage,
              accountInfo,
            )
          } catch (e) {
            console.error(e)
            _browserWindow.webContents.send(
              browserAuthorizeChannel.accountAuthorizeError,
              contextIdentifier,
            )
          }
        } else if (currentState === '已失效') {
          _browserWindow.webContents.send(
            browserAuthorizeChannel.accountSessionFailed,
            contextIdentifier,
            context.account,
            accountSession,
          )
        }
      },
    )

    browserEventBus.on(
      browserEvents.identifyVerified,
      async (contextIdentifier: BrowserContextIdentifier, identityVerified: boolean) => {
        console.log('identifyVerified', contextIdentifier)
        const context = globalBrowserContextManager.getAccountSpaceContext(contextIdentifier)
        _browserWindow.webContents.send(
          browserAuthorizeChannel.accountIdentityVerified,
          contextIdentifier,
          context.account,
          identityVerified,
        )
      },
    )

    browserEventBus.on(browserEvents.headerInitialed, () => {
      _browserWindow.webContents.send(browserChannel.headerInitialed)
    })

    authorizeListenerManager.init()
  },
}

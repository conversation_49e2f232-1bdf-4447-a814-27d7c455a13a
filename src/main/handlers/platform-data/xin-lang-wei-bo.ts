import { uiEvents } from '@common/events/ui-events'
import { ipcMain } from 'electron'
import { xinLangWeiBoPlatformService } from '../../services/platform-service/xin-lang-wei-bo'

export default {
  registerHandlers(_browserWindow: Electron.BrowserWindow) {
    const platform = uiEvents.platform.xinLangWeiBo

    ipcMain.handle(platform.getTopics, (_event, cookies: Electron.Cookie[], keyword: string) => {
      return xinLangWeiBoPlatformService.getTopics(cookies, keyword)
    })

    ipcMain.handle(platform.getLocations, (_event, cookies: Electron.Cookie[], keyword: string) => {
      return xinLangWeiBoPlatformService.getLocations(cookies, keyword)
    })
  },
}

import { uiEvents } from '@common/events/ui-events'
import { ipcMain } from 'electron'
import { tengXunWeiShiPlatformService } from '@main/services/platform-service/teng-xun-wei-shi'

export default {
  registerHandlers(_browserWindow: Electron.BrowserWindow) {
    const platform = uiEvents.platform.tengXunWeiShi
    ipcMain.handle(platform.getTopics, (_event, cookies: Electron.Cookie[], keyword: string) => {
      return tengXunWeiShiPlatformService.getTopics(cookies, keyword)
    })
  },
}

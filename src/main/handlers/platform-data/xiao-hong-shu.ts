import { uiEvents } from '@common/events/ui-events'
import { ipcMain } from 'electron'
import { xiaoHongShuPlatformService } from '../../services/platform-service/xiao-hong-shu'

export default {
  registerHandlers(_browserWindow: Electron.BrowserWindow) {
    const platform = uiEvents.platform.xiaoHongShu
    ipcMain.handle(platform.getTopics, (_event, cookies: Electron.Cookie[], keyWord: string) => {
      return xiaoHongShuPlatformService.getTopics(cookies, keyWord)
    })
    ipcMain.handle(platform.getFriends, (_event, cookies: Electron.Cookie[], keyWord: string) => {
      return xiaoHongShuPlatformService.getFriends(cookies, keyWord)
    })
    ipcMain.handle(platform.getLocations, (_event, cookies: Electron.Cookie[], keyWord: string) => {
      return xiaoHongShuPlatformService.getLocations(cookies, keyWord)
    })
  },
}

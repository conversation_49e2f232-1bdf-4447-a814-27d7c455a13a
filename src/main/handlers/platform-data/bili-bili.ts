import { uiEvents } from '@common/events/ui-events'
import { ipcMain } from 'electron'
import { bilibiliPlatformService } from '../../services/platform-service/bili-bili'

export default {
  registerHandlers(_browserWindow: Electron.BrowserWindow) {
    const platform = uiEvents.platform.biliBili
    ipcMain.handle(
      platform.getTopics,
      (_event, cookies: Electron.Cookie[], categoryId: number, keyWord?: string) => {
        return bilibiliPlatformService.getTopics(cookies, categoryId, keyWord)
      },
    )
    ipcMain.handle(platform.getFriends, (_event, cookies: Electron.Cookie[], keyWord?: string) => {
      return bilibiliPlatformService.getFriends(cookies, keyWord)
    })
    ipcMain.handle(platform.getCategories, (_event, cookies: Electron.Cookie[]) => {
      return bilibiliPlatformService.getVideoCategory(cookies)
    })
  },
}

import { ipcMain } from 'electron'
import { uiEvents, windowEvents } from '@common/events/ui-events'
import { store } from '@main/store.js'

export default {
  registerHandlers(browserWindow: Electron.BrowserWindow) {
    ipcMain.handle(uiEvents.setWindowSize, (_event, width: number, height: number) => {
      browserWindow.hide()
      browserWindow.setSize(width, height, false)
      browserWindow.show()
    })

    ipcMain.handle(uiEvents.setWindowMaxSize, (_event, width: number, height: number) => {
      browserWindow.setMaximumSize(width, height)
    })

    ipcMain.handle(uiEvents.setWindowMinSize, (_event, width: number, height: number) => {
      browserWindow.setMinimumSize(width, height)
    })

    ipcMain.handle(uiEvents.setWindowResizable, (_event, resizable: boolean) => {
      browserWindow.setResizable(resizable)
    })

    ipcMain.handle(uiEvents.setWindowCenter, () => {
      browserWindow.center()
    })

    ipcMain.handle(windowEvents.focusWindow, (_event) => {
      if (browserWindow.isMinimized()) {
        browserWindow.restore()
      }
      browserWindow.focus()
    })

    ipcMain.handle(windowEvents.minimum, (_event) => {
      browserWindow.minimize()
    })

    ipcMain.handle(windowEvents.maximum, (_event) => {
      if (browserWindow.isMaximized()) {
        browserWindow.unmaximize()
      } else {
        browserWindow.maximize()
      }
    })

    ipcMain.handle(windowEvents.close, (_event) => {
      browserWindow.close()
    })

    ipcMain.handle(windowEvents.move, (_event, dx: number, dy: number) => {
      const [x, y] = browserWindow.getPosition()
      browserWindow.setPosition(x + dx, y + dy)
    })
    ipcMain.on(windowEvents.sidebarCollapse, (_event, collapsed: boolean) => {
      store.set('sidebarCollapsed', collapsed)
    })
  },
}

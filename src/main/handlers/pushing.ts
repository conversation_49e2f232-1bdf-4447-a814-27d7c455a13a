import { ipcMain } from 'electron'
import { uiEvents } from '@common/events/ui-events'
import { getPlatformService } from '@main/services/platform-service/factory'
import type { EditContentType } from '@common/model/content-type'
import { getPlatformServicePromise } from '@main/services/platform-service/import-promise'
import { systemService } from '@main/services/system-service'
import type { AccountInfoStructure } from '@common/model/account-info'
import type { CloudPublishTaskVideoFromLite } from '@yixiaoer/platform-service'

export default {
  registerHandlers(_browserWindow: Electron.BrowserWindow) {
    ipcMain.handle(
      uiEvents.auditStateQuery,
      (
        _event,
        taskId: string,
        platformName: string,
        contentType: EditContentType,
        account: AccountInfoStructure,
        publishId: string,
      ) => {
        return getPlatformService(platformName).queryState(taskId, contentType, account, publishId)
      },
    )

    ipcMain.handle(uiEvents.pushAppTask, async (_event, body: CloudPublishTaskVideoFromLite) => {
      console.debug('pushAppTask', systemService.getAuthorizationToken(), body)
      const service = await getPlatformServicePromise()
      const result = await service.Cloud.pushPublishLiteClientTask(body)
      console.debug('任务接收结果', result)
    })

    ipcMain.handle(uiEvents.getUnfinishedTaskIds, async () => {
      const service = await getPlatformServicePromise()
      return service.Cloud.getPublishingTasks()
    })

    ipcMain.handle(uiEvents.cancelPublishTask, async (_event, taskId: string) => {
      console.debug('cancelPublishTask', taskId)
      try {
        const service = await getPlatformServicePromise()
        return service.Cloud.cancelPublishTask(taskId)
      } catch (error) {
        console.error('cancelPublishTask failed:', error)
        throw error
      }
    })
  },
}

import { rpaChannel } from '@common/events/rpa-events'
import { platformNames } from '@common/model/platform-name'
import { type BrowserService } from '@main/services/browser-service/browser-service'
import { pathService } from '@main/services/path-service'

import { exec } from 'child_process'
import { ipcMain, shell } from 'electron'
import fs from 'fs-extra'
import vm from 'vm'
import type { BrowserContext } from '@main/services/browser-service/browser/browser-context'
import { globalBrowserContextManager } from '@main/services/browser-service/browser/contextManager'
import { useBrowserService } from './browser'
import { browserEventBus } from '@main/services/eventBus/eventBus'

export default {
  registerHandlers(_browserWindow: Electron.BrowserWindow) {
    const rpaBrowserServiceMap: Record<string, BrowserService> = {}

    browserEventBus.on('group-closed', (windowIdentifier: string) => {
      delete rpaBrowserServiceMap[windowIdentifier]
    })

    ipcMain.on(rpaChannel.publishBulk, async (event, data, index) => {
      const browserService = rpaBrowserServiceMap[data]
      let returnValue = false
      if (browserService) {
        const tab = browserService.getGroup().tabs[index]
        if (tab) {
          try {
            returnValue = await tab.view.webContents.executeJavaScript('startPush()')
            console.log('returnValue', returnValue)
          } catch (error) {
            //
            console.log(error)
          }
        }
      }
      event.returnValue = returnValue
    })

    ipcMain.handle(
      rpaChannel.start,
      async (
        _event,
        configs: (Record<string, unknown> & { accountId: string; contextId: string })[],
        userInfo: {
          userid: string
          teamId: string
          type: 'video' | 'image-text'
        },
      ) => {
        const rpaFilePath = pathService.getRpaFilePath()

        if (!fs.existsSync(rpaFilePath)) {
          // TODO
        }

        const { userid, teamId, type } = userInfo
        const contexts: Record<string, BrowserContext> = {}

        for (const config of configs) {
          contexts[config.accountId] = globalBrowserContextManager.get({
            userid,
            teamId,
            contextId: config.contextId,
          })
        }

        const code = fs.readFileSync(rpaFilePath, 'utf-8').toString()
        const id = JSON.stringify({
          time: Date.now(),
          num: configs.length,
        })
        const service = useBrowserService(id)
        await service.getGroup().init()
        rpaBrowserServiceMap[id] = service

        vm.runInThisContext(`((require, browserService, startConfigs, browserContexts, platformNames, exports, type, serviceId)=>{
        ${code}
        })`)(require, service, configs, contexts, platformNames, exports, type, id)
      },
    )

    if (import.meta.env.DEV) {
      ipcMain.handle('openDir', () => {
        const pathUrl = pathService.getOnlineScripts()
        // Mac os 使用 showItemInFolder 非常慢
        if (process.platform === 'darwin') {
          exec(`open -R "${pathUrl}"`)
        } else {
          shell.showItemInFolder(pathUrl)
        }
      })
    }

    ipcMain.handle(
      rpaChannel.sendEvent,
      async (_event, value: string, tabId: string, serviceId: string) => {
        const service = rpaBrowserServiceMap[serviceId]
        const tab = service.getTab(tabId)

        if (tab) {
          const chars = [...value]
          tab.view.webContents.focus()

          for (const char of chars) {
            tab.view.webContents.sendInputEvent({
              type: 'keyDown',
              keyCode: char,
              modifiers: [],
            })

            if (char !== ' ') {
              tab.view.webContents.sendInputEvent({
                type: 'char',
                keyCode: char,
              })
            } else {
              tab.view.webContents.sendInputEvent({
                type: 'char',
                keyCode: 'Space',
              })
            }

            tab.view.webContents.sendInputEvent({
              type: 'keyUp',
              keyCode: char,
            })
          }
        }
      },
    )

    ipcMain.handle(rpaChannel.sendEntryEvent, async (_event, tabId: string, serviceId: string) => {
      const service = rpaBrowserServiceMap[serviceId]
      const tab = service.getTab(tabId)

      if (tab) {
        tab.view.webContents.sendInputEvent({
          type: 'keyDown',
          keyCode: 'Enter',
          modifiers: [],
        })

        tab.view.webContents.sendInputEvent({
          type: 'keyUp',
          keyCode: 'Enter',
        })
      }
    })
  },
}

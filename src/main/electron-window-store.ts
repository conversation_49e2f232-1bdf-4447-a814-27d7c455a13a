import type { BrowserWindow } from 'electron'
import type Store from 'electron-store'
import type { StoreSchema } from './store'

class WindowStateManager {
  private store: Store<StoreSchema>

  constructor(store: Store<StoreSchema>) {
    // Initialize the store with the defined schema
    this.store = store
  }
  // 获取窗口的初始配置
  getInitialWindowConfig() {
    const { width, height } = this.store.get('windowSize')
    const { x, y } = this.store.get('windowPosition')
    return {
      width,
      height,
      x,
      y,
    }
  }
  // 确保窗口在可见范围内
  // private ensureWindowInBounds(
  //   windowSize: { width: number; height: number },
  //   position: { x?: number; y?: number },
  //   display: Electron.Display,
  // ): { x: number; y: number } {
  //   const { width: displayWidth, height: displayHeight } = display.workAreaSize
  //   const { x: displayX, y: displayY } = display.workArea // 可能是负数

  //   console.log('display', display)

  //   let adjustedX = position.x ?? displayX
  //   let adjustedY = position.y ?? displayY

  //   // 调整 X 坐标，确保窗口不超出左侧或右侧
  //   if (adjustedX < displayX) adjustedX = displayX // 不能超出左边界
  //   if (adjustedX + windowSize.width > displayX + displayWidth) {
  //     adjustedX = displayX + displayWidth - windowSize.width // 不能超出右边界
  //   }

  //   // 调整 Y 坐标，确保窗口不超出顶部或底部
  //   if (adjustedY < displayY) adjustedY = displayY // 不能超出上边界
  //   if (adjustedY + windowSize.height > displayY + displayHeight) {
  //     adjustedY = displayY + displayHeight - windowSize.height // 不能超出底部
  //   }

  //   return { x: adjustedX, y: adjustedY }
  // }

  // 设置窗口状态追踪
  setupWindowStateTracking(mainWindow: BrowserWindow) {
    // 保存窗口尺寸
    mainWindow.on('resize', () => {
      const [width, height] = mainWindow.getSize()
      this.store.set('windowSize', { width, height })
    })

    // 保存窗口位置
    mainWindow.on('moved', () => {
      const [x, y] = mainWindow.getPosition()
      // const currentDisplay = screen.getDisplayNearestPoint({ x, y })
      this.store.set('windowPosition', { x, y })
      // this.store.set('displayId', screen.getAllDisplays().indexOf(currentDisplay))
    })
  }
  // Restore window to last known state
}

// Export for use in main process
export default WindowStateManager

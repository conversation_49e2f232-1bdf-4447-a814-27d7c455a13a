import { app, ipc<PERSON>ain, <PERSON>u, Tray } from 'electron'
import macOSTrayIcon from '../../resources/tray/macOSTemplate.png?asset'
import windowsTrayIcon from '../../resources/tray/windowsTemplate.png?asset'
import { mainEvents } from '@common/events/main-events'

// 托盘实例
let tray: Tray | null = null

// 获取当前应用名称
const appName: string = import.meta.env.VITE_APP_NAME
const version: string = app.getVersion()

/**
 * 初始化托盘
 * @return {Promise<Electron.CrossProcessExports.Tray>}
 */
export const showTray = async (): Promise<Tray> => {
  // 防止重复初始化
  if (tray) {
    return tray
  }

  console.debug('初始化托盘')

  // 检查应用名称是否定义
  if (appName == undefined || appName == '') {
    throw new Error('App name is not defined')
  }

  // 检查版本是否定义
  if (version == undefined || version == '') {
    throw new Error('Version is not defined')
  }

  const iconPath = process.platform === 'darwin' ? macOSTrayIcon : windowsTrayIcon

  // 创建托盘
  tray = new Tray(iconPath)

  tray.setToolTip(appName)

  tray.on('click', () => {
    ipcMain.emit(mainEvents.openMainWindow)
  })

  // 首次创建菜单
  createTrayMenu()

  return tray
}

/**
 * 创建托盘菜单
 */
export const createTrayMenu = () => {
  // 创建托盘菜单
  const contextMenu = Menu.buildFromTemplate([
    // 打开网址
    {
      label: `打开${appName}`,
      type: 'normal',
      click: async () => {
        console.log('托盘打开窗口')
        ipcMain.emit(mainEvents.openMainWindow)
      },
    },

    // 分割
    { type: 'separator' },

    // 版本信息
    {
      label: `版本 ${version}`,
      enabled: false,
    },

    // 分割
    { type: 'separator' },

    // 退出应用
    {
      label: '退出',
      type: 'normal',
      click: () => {
        // 主动退出应用
        ipcMain.emit(mainEvents.exitClicked)
      },
    },
  ])

  // 检查托盘是否初始化
  if (tray == null) {
    throw new Error('Tray is not initialized')
  }

  // 设置托盘菜单
  tray.setContextMenu(contextMenu)

  return contextMenu
}

export const hideTray = () => {
  if (tray) {
    tray.destroy()
    tray = null
  }
}

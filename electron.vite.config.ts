import path, { resolve } from 'path'
import { defineConfig, externalizeDepsPlugin } from 'electron-vite'
import react from '@vitejs/plugin-react'
import svgr from 'vite-plugin-svgr'
import packageJson from './package.json'
import { viteStaticCopy } from 'vite-plugin-static-copy'
import { normalizePath } from 'vite'

export default defineConfig({
  main: {
    resolve: {
      alias: {
        '@main': resolve('src/main'),
        '@common': resolve('src/common'),
      },
    },
    plugins: [externalizeDepsPlugin({ exclude: ['electron-store'] })],
    build: {
      rollupOptions: {
        external: ['sharp'],
      },
      watch: {},
    },
  },
  preload: {
    build: {
      rollupOptions: {
        input: {
          'account-authorize': resolve(__dirname, 'src/preload/account-authorization/index.ts'),
          index: resolve(__dirname, 'src/preload/index.ts'),
        },
      },
      watch: {},
    },
    resolve: {
      alias: {
        '@main': resolve('src/main'),
        '@common': resolve('src/common'),
      },
    },
    plugins: [externalizeDepsPlugin()],
  },
  renderer: {
    define: {
      'import.meta.env.APP_VERSION': JSON.stringify(packageJson.version),
    },
    build: {
      rollupOptions: {
        input: {
          index: resolve(__dirname, 'src/renderer/index.html'),
          hahaha: resolve(__dirname, 'src/renderer/browserHeader.html'),
        },
      },
    },
    resolve: {
      alias: {
        '@browser': resolve('src/renderer/browser'),
        '@renderer': resolve('src/renderer/src'),
        '@common': resolve('src/common'),
      },
    },
    plugins: [
      viteStaticCopy({
        targets: [
          {
            src: normalizePath(
              path.join(
                import.meta.dirname,
                'node_modules',
                'mediainfo.js',
                'dist',
                'MediaInfoModule.wasm',
              ),
            ),
            dest: '',
          },
        ],
      }),
      react(),
      svgr(),
    ],
    optimizeDeps: {
      include: ['react/jsx-runtime'],
    },
    css: {
      preprocessorOptions: {
        less: {
          javascriptEnabled: true,
        },
      },
    },
  },
})
